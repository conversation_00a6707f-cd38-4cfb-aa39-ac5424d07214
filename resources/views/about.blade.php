<x-app-layout>

    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(/assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1>{{$toptitle}}</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>
                            <a href="#">{{$toptitle}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </section><!-- banner-section end -->

    <section class="team-section service-section ">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms" style="background-image: url(assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            <div class="row clearfix"  style="background-color: white;padding: 20px;border-radius: 20px;position: relative;">
                @foreach($pagedetails->content as $doc)
                    @if($doc->name =='akordion')
                        <div class="acordion-wrapper">
                            @include('flexible-components.' .$doc->name())
                        </div>
                    @else
                        @include('flexible-components.' .$doc->name())
                    @endif
                @endforeach
            </div>
        </div>
    </section>

    <script>
        document.getElementById("hakkimizda").classList.add('current');
    </script>
    @section('title')  @if($pagedetails->seo_title!=null) {{$pagedetails->seo_title}} @else {{$pagedetails->title}} @endif  @endsection

    @section('seo_title') @if($pagedetails->seo_title!=null) {{$pagedetails->seo_title}} @else {{$pagedetails->title}}  @endif @endsection

    @section('seo_description') @if($pagedetails->meta_description!=null) {{$pagedetails->meta_description}} @else {{nova_get_setting('description')}} @endif @endsection

    @section('seo_keywords') @if($pagedetails->meta_keywords!=null) {{$pagedetails->meta_keywords}} @else {{nova_get_setting('keywords')}} @endif @endsection
</x-app-layout>
