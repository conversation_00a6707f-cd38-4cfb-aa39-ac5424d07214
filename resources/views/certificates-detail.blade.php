<x-app-layout>

    <!-- banner-section -->
    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(/assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1 class="d-none  d-lg-block">{{$pagedetails->title}}</h1>
                        <h1 class="d-lg-none">{{$pagedetails->title}}</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>
                            <a href="/egitimlerimiz/{{$slug}}">{{$toptitle}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </section><!-- banner-section end -->
    <!-- sidebar-page-container -->

    <section class="service-section alternat-2 blog-details">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(/assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            <div class="row clearfix">
                <div class="col-lg-4 col-md-12 col-sm-12 sidebar-side">
                    <div class="" style="background-color: white;
				    padding: 30px;
				    border-radius: 20px;">
                        <div class="events-sidebar">
                            <div class="speaker-info">
                                <h3>Kontenjan</h3>
                                <ul class="info-list clearfix">
                                    <li><i class="icon-42"></i><span
                                            style="font-size: 25px; font-weight: bold;"> {{$pagedetails->quota}}</span>
                                    </li>
                                </ul>

                            </div>
                        </div>
                        <div class="events-sidebar">
                            <div class="speaker-info">
                                <h3>Eğitim Detayları</h3>
                                <ul class="info-list clearfix">
                                    <li>
                                        <i class="icon-56"></i><span id="fulldate"></span>
                                    </li>
                                    @foreach($pagedetails->details as $doc)
                                        <li><i class="icon-{{ $doc->icon }}"></i><span> {{$doc->text}} </span></li>
                                    @endforeach

                                </ul>

                            </div>
                        </div>

                        {{--                        <div class="blog-sidebar" style="margin-left: 0px;">--}}
                        {{--                            <div class="sidebar-widget tags-widget">--}}
                        {{--                                <div class="widget-content">--}}
                        {{--                                    <ul class="tags-list clearfix">--}}
                        {{--                                        <li><a href="#">Uzaktan Eğitim</a></li>--}}
                        {{--                                        <li><a href="#">Canlı Sınıf</a></li>--}}
                        {{--                                        <li><a href="#">GTU İşbirliğiyle</a></li>--}}
                        {{--                                        <li><a href="#">python</a></li>--}}
                        {{--                                    </ul>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                        </div>--}}
                    </div>
                </div>
                <div class="col-lg-8 col-md-12 col-sm-12 content-side">
                    <div class="blog-details-content">
                        <div class="news-block-three d-none d-lg-block">
                            <div class="inner-box">
                                <div class="lower-content"
                                     style="align-items: center; display: flex; position: inherit;">
                                    <div class="post-date" style="position: inherit;">
                                        <h3>
                                            <i id="day"></i>
                                            <span id="month"></span>
                                        </h3>
                                    </div>
                                    <div class="inner" style="padding-left: 30px;">
                                        <h3>{{$pagedetails->title}}</h3>
                                        <div class="post-info">
                                            <p>
                                                @foreach($pagedetails->details as $doc)
                                                    <i class="icon-{{ $doc->icon }}"></i><span> {{$doc->text}} </span>
                                                    &nbsp;
                                                @endforeach
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-one">
                            @foreach($pagedetails->content as $doc)
                                @if($doc->name() =='akordion')
                                    <div class="acordion-wrapper">
                                        @include('flexible-components.' .$doc->name())
                                    </div>
                                @else
                                    @include('flexible-components.' .$doc->name())
                                @endif
                            @endforeach
                        </div>
                        <div class="auto-container">
                            <div class="sec-title centred">
                                <h2>Ön Kayıt</h2>
                            </div>
                            <div class="form-inner">
                                <form method="post" action="{{url($formslug)}}" class="default-form">
                                    @csrf
                                    <div class="row clearfix justify-content-center">
                                        <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                            <input style="background-color: #fff;" type="number" name="idnumber"
                                                   placeholder="T.C KİMLİK NUMARASI *" required="">
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                            <input style="background-color: #fff;" type="text" name="fullname"
                                                   placeholder="AD-SOYAD *" required="">
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                            <input style="background-color: #fff;" type="email" name="email"
                                                   placeholder="E-POSTA ADRESİ*" required="">
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                            <input style="background-color: #fff;" type="tel" name="phone" required=""
                                                   placeholder="TELEFON *">
                                        </div>
                                        {{--<div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                            <input style="background-color: #fff;" type="text" name="istenen-egitim" required="" placeholder="BAŞVURMAK İSTEDİĞİNİZ EĞİTİM *">
                                        </div>--}}
                                        {{--                                        <div class="col-lg-12 col-md-12 col-sm-12 form-group">--}}
                                        {{--                                            <select class="wide" name="educationtype">--}}
                                        {{--                                                <option selected disabled>EĞİTİM ŞEKLİ SEÇİNİZ *</option>--}}
                                        {{--                                                <option value="ONLINE">ONLINE</option>--}}
                                        {{--                                                <option value="YÜZ-YÜZE">YÜZ-YÜZE</option>--}}
                                        {{--                                            </select>--}}
                                        {{--                                        </div>--}}
                                        <div class="col-lg-12 col-md-12 col-sm-12">
                                            <input style="background-color: #fff;" type="hidden"
                                                   name="sertfika/kurs-adi" required=""
                                                   value="{{$pagedetails->title}}">
                                        </div>

                                        <div>
                                            <input type="checkbox" id="kvkk" name="kvkk-onay" required>
                                            <label for="kvkk">KVKK</label><br>
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-sm-12 form-group message-btn centred">
                                            <button class="theme-btn-one" type="submit" name="submit-form">KAYIT OL
                                            </button>
                                        </div>

                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!--
                        <div class="author-box">
                            <div class="shape">
                                <div class="shape-1" style="background-image: url(/assets/images/shape/shape-73.png);"></div>
                                <div class="shape-2" style="background-image: url(/assets/images/shape/shape-74.png);"></div>
                            </div>
                            <figure class="author-thumb">
                                <img alt="" src="/assets/images/news/author-1.png">
                            </figure>
                            <div class="inner">
                                <h3>About Merrie Lewis</h3>
                                <p>Excepteur sint occaecat cupidat non proident sunt in culpa qui officia deserunt mollit anim id est laborum. Sed perspiciatis unde omnis iste natus error sit voluptatem acusantium doloremque laudantium totam rem aperiam eaque ipsa quae ab illo inventore.</p>
                                <ul class="social-links clearfix">
                                    <li>
                                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                                    </li>
                                    <li>
                                        <a href="#"><i class="fab fa-twitter"></i></a>
                                    </li>
                                    <li>
                                        <a href="#"><i class="fab fa-instagram"></i></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        -->
                </div>
            </div>
        </div>
        </div>
    </section>
    <script>
        document.getElementById("egitimlerimiz").classList.add('current');
    </script>
    @section('script')
        <script src="https://momentjs.com/downloads/moment.js"></script>
        <script src="https://momentjs.com/downloads/moment-with-locales.min.js"></script>
        <script>
            moment.locale('tr');
            var nowDate = moment({{($pagedetails->start_date)->format('Ymd')}}, "YYYYMMDD").format('DD');
            var nowDate2 = moment({{($pagedetails->start_date)->format('Ymd')}}, "YYYYMMDD").format('MMMM');
            var nowDate3 = moment({{($pagedetails->start_date)->format('Ymd')}}, "YYYYMMDD").format('LL');

            console.log('nowDate :' + nowDate);
            console.log('nowDate :' + nowDate2);
            console.log('nowDate :' + nowDate3);
            document.getElementById("day").innerHTML = nowDate;
            document.getElementById("month").innerHTML = nowDate2;
            document.getElementById("fulldate").innerHTML = nowDate3;
        </script>
    @endsection
    @section('title')
        @if($pagedetails->seo_title!=null)
            {{$pagedetails->seo_title}}
        @else
            {{$pagedetails->title}}
        @endif
    @endsection

    @section('seo_title')
        @if($pagedetails->seo_title!=null)
            {{$pagedetails->seo_title}}
        @else
            {{$pagedetails->title}}
        @endif
    @endsection

    @section('seo_description')
        @if($pagedetails->meta_description!=null)
            {{$pagedetails->meta_description}}
        @else
            {{nova_get_setting('description')}}
        @endif
    @endsection

    @section('seo_keywords')
        @if($pagedetails->meta_keywords!=null)
            {{$pagedetails->meta_keywords}}
        @else
            {{nova_get_setting('keywords')}}
        @endif
    @endsection
</x-app-layout>
