<x-app-layout>

    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(/assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1>Eğitim önerileriniz</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>Eğitim önerileriniz</li>
                    </ul>
                </div>
            </div>
        </section>
    </section><!-- banner-section end -->
    <!-- sidebar-page-container -->

    <section class="service-section alternat-2 blog-details">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(/assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            @isset($formSubmitting)
                <div class="alert alert-success text-center" id="mydiv21" role="alert" style="margin-top: 20px;">
                    Form Gönderme İşlemi Başarıyla Gerçekleşti
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endisset
            <div class="form-inner">
                <form method="post" action="{{url('egitimlerimiz/egitim-onerileriniz')}}" class="default-form">
                    @csrf
                    <div class="row clearfix justify-content-center">
                        <div class="col-lg-8">
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="fullname"
                                       placeholder="AD-SOYAD *" required="">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="email" name="email"
                                       placeholder="E-POSTA ADRESİ *" required="">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="tel" name="phone" required=""
                                       placeholder="TELEFON *">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="meslek" required=""
                                       placeholder="MESLEĞİNİZ *">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="unvan" required=""
                                       placeholder="ÜNVANINIZ*">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <select style="background-color: #fff;" name="egitim-tipi" id="egitim-tipi">
                                    @foreach($mergedarray as $item)
                                        <option value="{{$item["title"]}}">{{$item["title"]}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <!--
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="egitim-sekli" required="" placeholder="LİSANS DERECESİ/BÖLÜM/PROGRAM*">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="egitim-sekli" required="" placeholder="YÜKSEK LİSANS DERECESİ/BÖLÜM/PROGRAM *">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="egitim-sekli" required="" placeholder="DOKTORA DERECESİ/BÖLÜM/PROGRAM *">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="egitim-sekli" required="" placeholder="DOÇENTLİK DERECESİ*">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="egitim-sekli" required="" placeholder="SON 2 YILDA VERİLEN DERSLER *">
                            </div>
-->
                            {{--                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">--}}
                            {{--                                <input style="background-color: #fff;" type="text" name="mesaj" required=""--}}
                            {{--                                       placeholder="EKLEMEK İSTEDİKLERİNİZ *">--}}
                            {{--                            </div>--}}
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <textarea style="background-color: #fff;" placeholder="EKLEMEK İSTEDİKLERİNİZ *"
                                          name="mesaj" id="mesaj" cols="30" rows="10"></textarea>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group message-btn centred">
                                <button class="theme-btn-one" type="submit" name="submit-form">GÖNDER</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
    <script>
        document.getElementById("egitmenlik-basvurusu").classList.add('current');
    </script>
    @section('title')
        {{ 'Eğitmenlik Başvurusu'}}
    @endsection

    @section('seo_title')
        {{ 'Eğitmenlik Başvurusu'}}
    @endsection

    @section('seo_description')
        {{nova_get_setting('description')}}
    @endsection

    @section('seo_keywords')
        {{nova_get_setting('keywords')}}
    @endsection
</x-app-layout>
