<x-app-layout>
    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(/assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1>{{$page->title}}</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>{{$page->title}}</li>
                    </ul>
                </div>
            </div>
        </section>
    </section><!-- banner-section end -->
    <!-- sidebar-page-container -->

    <section class="service-section alternat-2 blog-details">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(/assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12 content-side">
                    <div class="blog-details-content">
                        <div class="content-one">
                            @foreach($page->content as $doc)
                                @if($doc->name() =='akordion')
                                    <div class="acordion-wrapper">
                                        @include('flexible-components.' .$doc->name())
                                    </div>
                                @else
                                    @include('flexible-components.' .$doc->name())
                                @endif
                            @endforeach

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @section('title')  @if($page->seo_title!=null) {{$page->seo_title}} @else {{$page->title}} @endif  @endsection

    @section('seo_title') @if($page->seo_title!=null) {{$page->seo_title}} @else {{$page->title}}  @endif @endsection

    @section('seo_description') @if($page->meta_description!=null) {{$page->meta_description}} @else {{nova_get_setting('description')}} @endif @endsection

    @section('seo_keywords') @if($page->meta_keywords!=null) {{$page->meta_keywords}} @else {{nova_get_setting('keywords')}} @endif @endsection
</x-app-layout>
