<x-app-layout>

    <!-- banner-section -->
    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1>İletişim</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>İletişim</li>
                    </ul>
                </div>

            </div>
        </section>
    </section><!-- banner-section end -->
    <!-- sidebar-page-container -->

    <section class="service-section alternat-2 blog-details">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms"
                 style="background-image: url(assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            @isset($formSubmitting)
                @if($formSubmitting == true)
                    <div class="alert alert-success text-center relative" id="mydiv21" role="alert"
                         style="margin-top: 20px;">
                        Form Başarıyla Gönderildi
                        <button type="button" class="close" style="position:absolute; top:5px; right: 5px;"
                                data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @elseif($formSubmitting == false)
                    <div class="alert alert-danger text-center" id="mydiv22" role="alert" style="margin-top: 20px;">
                        Form Gönderilemedi
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @endif
            @endisset
            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-12 footer-top row justify-content-center"
                     style="padding: 0px 0px 20px 0px; border-bottom: 0px;">
                    <div class="footer-widget logo-widget">
                        <div class="footer-logo">
                            <figure class="logo">
                                <a href="index.html"><img alt=""
                                                          src="/storage/{{nova_get_setting('default_logo')}}"></a>
                            </figure>
                        </div>
                        <div class="text">
                            <ul class="info clearfix">
                                @if(nova_get_setting('address'))
                                    <li>
                                        <i class="icon-26"></i>{{nova_get_setting('address')}}
                                    </li>
                                @endif
                                @if(nova_get_setting('tel1'))
                                    <li>
                                        <i class="icon-24"></i><a
                                            href="tel:+90{{nova_get_setting('tel1')}}">0{{PhoneFormat('tel1')}}</a>
                                    </li>
                                @endif
                                @if(nova_get_setting('tel2'))
                                    <li>
                                        <i class="icon-24"></i><a
                                            href="tel:+90{{nova_get_setting('tel2')}}">0{{PhoneFormat('tel2')}}</a>
                                    </li>
                                @endif
                                @if(nova_get_setting('email'))
                                    <li>
                                        <i class="icon-25"></i><a
                                            href="mailto:{{nova_get_setting('email')}}">{{nova_get_setting('email')}}</a>
                                    </li>
                                @endif
                            </ul>
                            <div class="icongroup">
                                <div>
                                    <a href="face" target="_blank">
                                        <i class="fab fa-facebook iconstyle"></i>
                                    </a>
                                </div>
                                <div>
                                    <a href="e" target="_blank">
                                        <i class="fab fa-twitter iconstyle"></i>
                                    </a>
                                </div>
                                <div>
                                    <a href="insta" target="_blank">
                                        <i class="fab fa-instagram iconstyle"></i>
                                    </a>
                                </div>
                                <div>
                                    <a href="e" target="_blank">
                                        <i class="fab fa-linkedin iconstyle"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-12">
                    <div class="sec-title centred">
                        <h2>İletişim Formu</h2>
                    </div>
                    <div class="form-inner">
                        <form method="post" action="{{url('iletisim-form')}}" class="default-form">
                            @csrf
                            <div class="row clearfix">
                                <div class="col-lg-6 col-md-6 col-sm-12 form-group">
                                    <input style="background-color: #fff;" type="text" name="fullname"
                                           placeholder="İsim Soyisim" required="">
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 form-group">
                                    <input style="background-color: #fff;" type="email" name="email"
                                           placeholder="E-Posta" required="">
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 form-group">
                                    <input style="background-color: #fff;" type="text" name="phone" required=""
                                           placeholder="Telefon Numarası">
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 form-group">
                                    <input style="background-color: #fff;" type="text" name="subject" required=""
                                           placeholder="Konu">
                                </div>
                                <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                    <textarea style="background-color: #fff;" name="message"
                                              placeholder="İletmek İstediğiniz Mesaj"></textarea>
                                </div>
                                <div class="col-lg-12 col-md-12 col-sm-12 form-group message-btn centred">
                                    <button class="theme-btn-one" type="submit" name="submit-form">Gönder</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- google-map-section -->
    <section class="google-map-section">
        <div class="map-inner">
            <div class="map-container">
                {!! nova_get_setting('map') !!}
            </div>
        </div>
    </section>
    <script>
        document.getElementById("iletisim").classList.add('current');
    </script>
    @section('title')
        {{ 'İletişim'}}
    @endsection

    @section('seo_title')
        {{ 'İletişim'}}
    @endsection

    @section('seo_description')
        {{nova_get_setting('description')}}
    @endsection

    @section('seo_keywords')
        {{nova_get_setting('keywords')}}
    @endsection
</x-app-layout>
