<x-app-layout>
    <!-- banner-section -->
    <section class="banner-section style-four style-five" style="padding: 125px 0px 90px 0px;">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <div class="banner-carousel owl-theme owl-carousel owl-dots-none">
            @foreach($sliders as $slider)
                <div class="slide-item">
                    <div class="auto-container">
                        <div class="row slider-reverse align-items-center clearfix">
                            <div class="col-lg-6 col-md-12 col-sm-12 content-column">
                                <div class="content-box">
                                    <h2>{{$slider->slider_title}}</h2>
                                    <div class="text-white">{!! $slider->slider_text !!}</div>
                                    @if( $slider->text_align == true )
                                        <div class="btn-box">
                                            <a class="theme-btn-one" target="{{$slider->target}}"
                                               href="{{$slider->slider_link}}">Tıklayınız</a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12 col-sm-12 image-column">
                                <div class="image-box">
                                    <div class="shape">
                                        <div class="shape-3"
                                             style="background-image: url(/assets/images/shape/shape-52.png);"></div>
                                        <div class="shape-4"
                                             style="background-image: url(/assets/images/shape/shape-52.png);"></div>
                                    </div>
                                    <figure class="image">
                                        @if($slider->image_id)
                                            <img alt=""
                                                 src="/storage/{{getImageLibraryImage($slider->image_id)->name}}">
                                        @endif
                                    </figure>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </section><!-- banner-section end -->
    <!-- service-section -->
    <section class="testimonial-section bg-special">
        <div class="auto-container">
            <div class="row clearfix">
                <div class="col-xl-12 col-lg-12 col-md-12 paddings-of-announcement inner-column">
                    <div class="inner-content announcement-text ">
                        <div class="single-item-carousel owl-carousel owl-theme owl-dots-none">
                            @foreach($notifications as $item)
                                <div class="content_block_3">
                                    <div class="content-box">
                                        <div class="text">
                                            <p><a style="color: #142441;" href="{{$item->slug}}">{{$item->about}}</a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="service-section alternat-2">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(/assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            <div class="sec-title centred">
            </div>
            <div class="row clearfix">
                <div class="col-lg-3 col-md-6 col-sm-12 service-block">
                    <div class="service-block-one mainlink wow fadeInUp animated cursor-pointer" data-wow-delay="00ms"
                         data-wow-duration="1500ms" onclick="window.location='{{nova_get_setting('pagelink_1')}}';">
                        <div class="inner-box bg-dynamic"
                             style="background-image: url('/storage/{{nova_get_setting('pagelogo_1')}}');">
                            <h4 class="text-center"><a class="text-white"
                                                       href="{{nova_get_setting('pagelink_1')}}">{{nova_get_setting('pagetitle_1')}}</a>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-12 service-block">
                    <div class="service-block-one mainlink wow fadeInUp animated" data-wow-delay="200ms"
                         data-wow-duration="1500ms" onclick="window.location='{{nova_get_setting('pagelink_2')}}';">
                        <div class="inner-box bg-dynamic"
                             style="background-image: url('/storage/{{nova_get_setting('pagelogo_2')}}');">
                            <h4 class="text-center"><a class="text-white"
                                                       href="{{nova_get_setting('pagelink_2')}}">{{nova_get_setting('pagetitle_2')}}</a>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-12 service-block">
                    <div class="service-block-one mainlink wow fadeInUp animated" data-wow-delay="400ms"
                         data-wow-duration="1500ms" onclick="window.location='{{nova_get_setting('pagelink_3')}}';">
                        <div class="inner-box bg-dynamic"
                             style="background-image: url('/storage/{{nova_get_setting('pagelogo_3')}}');">
                            <h4 class="text-center"><a class="text-white"
                                                       href="{{nova_get_setting('pagelink_3')}}">{{nova_get_setting('pagetitle_3')}} </a>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-12 service-block">
                    <div class="service-block-one mainlink wow fadeInUp animated" data-wow-delay="600ms"
                         data-wow-duration="1500ms" onclick="window.location='{{nova_get_setting('pagelink_4')}}';">
                        </a>
                        <div class="inner-box bg-dynamic"
                             style="background-image: url('/storage/{{nova_get_setting('pagelogo_4')}}');">
                            <h4 class="text-center"><a class="text-white"
                                                       href="{{nova_get_setting('pagelink_4')}}">{{nova_get_setting('pagetitle_4')}} </a>
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!-- service-section end -->
    <!-- about-style-two -->
    <section class="about-style-two sec-pad">
        <div class="auto-container">
            <div class="row align-items-center clearfix">
                <div class="col-lg-6 col-md-12 d-none d-sm-block image-column">
                    <div class="image_block_8">
                        <div class="image-box">
                            <div class="shape">
                                <div class="shape-1 rotate-me"
                                     style="background-image: url(/assets/images/shape/shape-56.png);"></div>
                                <div class="shape-2"
                                     style="background-image: url(/assets/images/shape/shape-57.png);"></div>
                                <div class="shape-3"
                                     style="background-image: url(/assets/images/shape/shape-57.png);"></div>
                            </div>
                            <figure class="image image-1">
                                <img alt="" src="doc/campus-2.jpg">
                            </figure>
                            <figure class="image image-2 paroller">
                                <img alt="" src="doc/alumni-4.jpg">
                            </figure>
                            <figure class="image image-3 paroller-2">
                                <img alt="" src="doc/academic-8.jpg">
                            </figure>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-12 col-sm-12 content-column">
                    <div class="content_block_1">
                        <div class="content-box">
                            <div class="sec-title">
                                <h6>Hakkımızda</h6>
                                <h2>Hakkımızda</h2>
                            </div>
                            <div class="bold-text" style="text-align: justify;">
                                <p>Gebze Yüksek Teknoloji Enstitüsü olarak 1992 yılından bu yana oluşturulan bilimsel
                                    tecrübe ve birikimi 2014 yılında devralan Gebze Teknik Üniversitesi, Gebze halkı ve
                                    sanayisinin eğitim ihtiyaçlarına çözüm sunmak üzere Aralık 2015’te Sürekli Eğitim
                                    Merkezinde yeni bir yapılanmaya gitmiştir. GTÜ-SEM, konumundan hareketle Gebze
                                    halkına ve işletme çalışanlarına yönelik pek çok örgün ve uzaktan eğitim
                                    sunmaktadır. Eğitimler talebe göre üniversite kampüsünde ve/veya işletme bünyesinde
                                    verilebilmektedir. GTÜ-SEM, bireysel eğitimlerin yanı sıra çağın gerektirdiği
                                    yenilik ve değişime ayak uydurmak isteyen işletmelere yönelik inovasyon, yaratıcı
                                    düşünme, yeniden yapılanma gibi bireysel eğitimlere nazaran daha kapsamlı ve
                                    kurumsal eğitim ve danışmanlıklar da sunmaktadır. Ayrıca Gebze Teknik
                                    Üniversitesinin bilimsel proje sayısı ve tecrübesinden hareketle gerek bireysel
                                    işletmeler, gerekse OSBler, odalar, birlikler gibi örgütlenmelerle hem bilimsel ve
                                    teknolojik altyapıyı hem de nitelikli işgücünü geliştirmeyi hedefleyen projeler
                                    tasarlanmaktadır.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!-- about-style-two end -->

    <!-- news-section -->
    <section class="news-section sec-pad">
        <div class="auto-container">
            <div class="sec-title">
                <h6>Son Haberler</h6>
                <h2>HABERLER</h2>
            </div>
            <div class="three-item-carousel owl-carousel owl-theme owl-dots-none">
                @foreach($news as $newitem)
                    <div class="news-block-one">
                        <div class="inner-box">
                            <figure class="image-box">
                                <a href="/haberler/{{$newitem->slug}}"><i class="fas fa-link"></i></a>
                                @if($newitem->image_id)
                                    {!!'<img src="/storage/'.getImageLibraryImage($newitem->image_id)->name.'" alt="'.$newitem->title.'">'!!}
                                @endif
                            </figure>
                            <div class="lower-content">
                                <div class="shape"
                                     style="background-image: url(/assets/images/shape/shape-13.png);"></div>
                                <div class="post-date">
                                    <h3 id="ftitle{{$newitem->id}}">
                                        {{\Illuminate\Support\Str::of($newitem->news_date)->explode('-')[0]}}
                                        <span
                                            id="fspan{{$newitem->id}}">{{\Illuminate\Support\Str::of($newitem->news_date)->explode('-')[1]}}</span>
                                    </h3>
                                </div>
                                <h4><a href="/haberler/{{$newitem->slug}}">{{$newitem->title}}</a></h4>
                                <p>{{$newitem->about}}</p>
                                <div class="btn-box"><a href="/haberler/{{$newitem->slug}}" class="theme-btn-two">Habere
                                        Git</a></div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    <!-- news-section end -->
    <script>
        document.getElementById("anasayfa").classList.add('current');
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/tr.min.js"></script>
    <script>
        moment.locale("tr");
        var dateSpan = document.createElement('span');
        var nowDate = "1996/12/12"
        @foreach($news as $newitemdate)
            nowDate = "{{(\Carbon\Carbon::parse($newitemdate->news_date)->format('Y/m/d'))}}";
        dateSpan.innerHTML = moment(nowDate).format(' MMMM ');
        document.getElementById("ftitle{{$newitemdate->id}}").innerHTML = moment(nowDate).format(' Do ') + dateSpan.outerHTML;
        // console.log(nowDate);
        // console.log(moment(nowDate).format(' MMMM '));
        // console.log(moment(nowDate).format(' Do '));
        @endforeach
    </script>
    @if(nova_get_setting('isactive')!=3)
        @include('layouts.popup')
    @endif

    @section('title')
        {{ 'Anasayfa'}}
    @endsection

    @section('seo_title')
        {{ 'Anasayfa'}}
    @endsection

    @section('seo_description')
        {{nova_get_setting('description')}}
    @endsection

    @section('seo_keywords')
        {{nova_get_setting('keywords')}}
    @endsection
</x-app-layout>
