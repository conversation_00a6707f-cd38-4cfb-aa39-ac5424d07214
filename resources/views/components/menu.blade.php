@foreach($menu as $item)
    @if($item['enabled']==1)
    <li id="{{$item['value']}}"  @if(count( $item['children'] ) > 0){!!  'class="dropdown"'!!}@endif>
        <a href="{{ Str::startsWith($item['value'], 'http') ? '' : '/' }}{{$item['value']}}" target="{{$item['target']}}">
            {{$item['name']}}
        </a>
        @if( count( $item['children'] ) > 0 )
            <ul class="sub-menu">
                @include('components.submenu', ['menu' => $item['children'], 'submenu' => true, 'onek' =>$item['value'] ])
            </ul>
        @endif
    </li>
    @endif
@endforeach
