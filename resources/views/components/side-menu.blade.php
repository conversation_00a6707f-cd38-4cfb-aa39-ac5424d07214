@if($menu!=null)
    @foreach($menu as $item)
        @if($item['enabled']==1)
        @if(\Illuminate\Support\Str::slug($item['name'],'-')==$slug3)
            <h3 class="widget-title">{{$item['name']}}</h3>
            @if( count( $item['children'] ) > 0 )
                <ul>
                    @foreach($item['children'] as $item2)
                        @if($item2['enabled']==1)
                        <li style="box-sizing: border-box;" >
                            <a href="/{{$slug3}}/{{$item2['value']}}" id="{{\Illuminate\Support\Str::slug($item2['name'])}}">
                                {{$item2['name']}}
                            </a>
                            @if(count( $item2['children'] ) > 0)
                                <div class="akordion-li2">
                                <i class="fal fa-angle-down right-menu-i"></i>
                                </div>
                            @endif
                            @if(count( $item2['children'] ) > 0)
                                <div class="akordion-a">
                                    @foreach($item2['children'] as $item3)
                                        @if($item3['enabled']==1)
                                        <a href="/{{$slug3}}/@if($item2['value']==null){{\Illuminate\Support\Str::slug($item2['name'])}}@else{{$item2['value']}}@endif/{{$item3['value']}}" id="{{$item3['value']}}" style="padding-left: 45px;border-radius: 0 0 3px 3px;">
                                            <i class="fal fa-angle-right"></i> {{$item3['name']}}
                                        </a>
                                        @endif
                                    @endforeach
                                </div>
                            @endif
                        </li>
                        @endif
                    @endforeach
                </ul>
            @endif
        @endif
        @endif
    @endforeach
@endif
