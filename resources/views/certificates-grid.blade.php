<x-app-layout>

    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(/assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1>{{$toptitle}}</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>
                            <a href="/egitimlerimiz/{{$slug}}">{{$toptitle}}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </section><!-- banner-section end -->

    <section class="service-section">
        <div class="pattern-layer">
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2  wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms" style="background-image: url(/assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            <div class="row clearfix items-stretch">
                @foreach($pages as $page)
                <div class="col-lg-4 col-md-6 col-sm-12 service-block mb-5">
                    <div class="service-block-one wow fadeInUp animated" data-wow-delay="00ms" data-wow-duration="1500ms">
                        <div class="inner-box" style="min-height: 210px;"x>
                            <h4><a href="/egitimlerimiz/{{$slug}}/{{$page->slug}}">{{$page->title}}</a></h4>
                            <p>{{$page->about}}</p>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    <script>
        document.getElementById("egitimlerimiz").classList.add('current');
    </script>
    @section('title') {{$toptitle}} @endsection

    @section('seo_title') {{$toptitle}} @endsection

    @section('seo_description') {{nova_get_setting('description')}} @endsection

    @section('seo_keywords') {{nova_get_setting('keywords')}} @endsection
</x-app-layout>
