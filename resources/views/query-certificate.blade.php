<x-app-layout>

    <!-- banner-section -->
    <section class="banner-section style-four style-five">
        <div class="pattern-layer">
            <div class="pattern-1 rotate-me" style="background-image: url(/assets/images/shape/shape-52.png);"></div>
            <div class="pattern-2 wow slideInLeft animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-53.png);"></div>
            <div class="pattern-3 wow slideInRight animated animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-54.png);"></div>
            <div class="pattern-4" style="background-image: url(/assets/images/shape/shape-68.png);"></div>
        </div>
        <section class="page-title centred">
            <div class="auto-container">
                <div class="content-box">
                    <div class="shape" style="background-image: url(/assets/images/shape/shape-63.png);"></div>
                    <div class="title">
                        <h1>Sertifika Sorgulama</h1>
                    </div>
                    <ul class="bread-crumb clearfix">
                        <li>
                            <a href="/anasayfa">Anasayfa</a>
                        </li>
                        <li>Sertifika Sorgulama</li>
                    </ul>
                </div>

            </div>
        </section>
    </section><!-- banner-section end -->
    <!-- sidebar-page-container -->

    <section class="service-section alternat-2 blog-details">
        <div class="pattern-layer">
            <div class="pattern-3" style="background-image:url(/assets/images/shape/shape-55.png);"></div>
            <div class="pattern-1 wow zoomIn animated" data-wow-delay="00ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-3.png);"></div>
            <div class="pattern-2 wow zoomIn animated" data-wow-delay="300ms" data-wow-duration="1500ms"
                 style="background-image: url(/assets/images/shape/shape-4.png);"></div>
        </div>
        <div class="auto-container">
            @isset($formSubmitting)
                @if($formSubmitting == true)
                    <div class="alert alert-success text-center relative" id="mydiv21" role="alert"
                         style="margin-top: 20px;">

                        @foreach($persons as $person)
                            <p style="color: #000000;font-weight:600;">TC NO : {{$person->id_number}}</p>
                            <p style="color: #000000;font-weight:600;">Ad Soyad : {{$person->full_name}}</p>
                            <p style="color: #000000;font-weight:600;">Sertifika Adı : {{$person->certificate_name}}</p>
                            <p style="color: #000000;font-weight:600;">Barkod : {{$person->barcode}}</p>
                            @if($person->media_id)
                                {!!'<a href="/storage/'.getImageLibraryImage($person->media_id)->name.'" target="_blank">Sertifikayı Görmek İçin Tıklayınız.</a>'!!}
                            @endif
                            <button type="button" class="close" style="position:absolute; top:5px; right: 5px;"
                                    data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            @if(count($persons)>2)
                                <hr>
                            @endif
                        @endforeach
                    </div>
                @elseif($formSubmitting == false)
                    <div class="alert alert-danger text-center" id="mydiv22" role="alert" style="margin-top: 20px;">
                        Aradığınız Bilgilerde Giriş Bulunamadı
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @endif
            @endisset
            <div class="form-inner">
                <form method="post" action="{{url('sertifika-form')}}" class="default-form">
                    @csrf
                    <div class="row clearfix justify-content-center">
                        <div class="col-sm-12 col-md-8">
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="number" name="idnumber" id="tckimlikno"
                                       onblur="tckimlikkontorolu(this);" maxlength="11" required=""
                                       placeholder="T.C Kimlik Numarası *">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group">
                                <input style="background-color: #fff;" type="text" name="username"
                                       placeholder="AD-SOYAD *" required="">
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 form-group message-btn centred">
                                <button class="theme-btn-one" type="submit" id="submitbutton" name="submit-form">
                                    SORGULA
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
    <script>
        document.getElementById("egitimlerimiz").classList.add('current');
    </script>
    @section('title')
        {{ 'Sertifika Sorgula'}}
    @endsection

    @section('seo_title')
        {{ 'Sertifika Sorgula'}}
    @endsection

    @section('seo_description')
        {{nova_get_setting('description')}}
    @endsection

    @section('seo_keywords')
        {{nova_get_setting('keywords')}}
    @endsection
    <script>
        function tckimlikkontorolu(tcno) {
            var tckontrol, toplam;
            tckontrol = tcno;
            tcno = tcno.value;
            toplam = Number(tcno.substring(0, 1)) + Number(tcno.substring(1, 2)) +
                Number(tcno.substring(2, 3)) + Number(tcno.substring(3, 4)) +
                Number(tcno.substring(4, 5)) + Number(tcno.substring(5, 6)) +
                Number(tcno.substring(6, 7)) + Number(tcno.substring(7, 8)) +
                Number(tcno.substring(8, 9)) + Number(tcno.substring(9, 10));
            strtoplam = String(toplam);
            onunbirlerbas = strtoplam.substring(strtoplam.length, strtoplam.length - 1);

            // if (onunbirlerbas == tcno.substring(10, 11)) {
            //     document.getElementById('submitbutton').disabled = false;
            // } else {
            //     document.getElementById('submitbutton').disabled = true;
            // }
        }
    </script>
</x-app-layout>
