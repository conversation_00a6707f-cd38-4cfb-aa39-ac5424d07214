<?php

namespace <PERSON><PERSON>\Nova\Http\Middleware;

use <PERSON><PERSON>\Nova\Events\ServingNova;

class DispatchServingNovaEvent
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Illuminate\Http\Response
     */
    public function handle($request, $next)
    {
        ServingNova::dispatch($request);

        return $next($request);
    }
}
