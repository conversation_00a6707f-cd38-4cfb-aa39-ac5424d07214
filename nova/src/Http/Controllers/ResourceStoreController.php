<?php

namespace Lara<PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Http\Requests\CreateResourceRequest;
use <PERSON>vel\Nova\Nova;

class ResourceStoreController extends Controller
{
    /**
     * Create a new resource.
     *
     * @param  \Laravel\Nova\Http\Requests\CreateResourceRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(CreateResourceRequest $request)
    {
        $resource = $request->resource();

        $resource::authorizeToCreate($request);

        $resource::validateForCreation($request);

        $model = DB::transaction(function () use ($request, $resource) {
            [$model, $callbacks] = $resource::fill(
                $request,
                $resource::newModel()
            );

            if ($request->viaRelationship()) {
                tap($request->findParentResourceOrFail(), function ($resource) use ($request) {
                    abort_unless($resource->hasRelatableField($request, $request->viaRelationship), 404);
                })->model()->{$request->viaRelationship}()->save($model);
            } else {
                $model->save();
            }

            Nova::actionEvent()->forResourceCreate($request->user(), $model)->save();

            collect($callbacks)->each->__invoke();

            return $model;
        });

        return response()->json([
            'id' => $model->getKey(),
            'resource' => $model->attributesToArray(),
            'redirect' => $resource::redirectAfterCreate($request, $request->newResourceWith($model)),
        ], 201);
    }
}
