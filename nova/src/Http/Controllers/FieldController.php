<?php

namespace <PERSON><PERSON>\Nova\Http\Controllers;

use Illuminate\Routing\Controller;
use <PERSON><PERSON>\Nova\Contracts\RelatableField;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class FieldController extends Controller
{
    /**
     * Retrieve the given field for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function show(NovaRequest $request)
    {
        return response()->json(
            $request->newResource()
                    ->availableFields($request)
                    ->when($request->relatable, function ($fields) {
                        return $fields->whereInstanceOf(RelatableField::class);
                    })
                    ->findFieldByAttribute($request->field, function () {
                        abort(404);
                    })
        );
    }
}
