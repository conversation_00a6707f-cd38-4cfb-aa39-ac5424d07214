<?php

namespace {{ namespace }};

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;

class {{ class }} extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = '{{ component }}';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query;
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        return [];
    }
}
