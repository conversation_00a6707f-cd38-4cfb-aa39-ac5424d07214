<?php

namespace {{ namespace }};

use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Events\ServingNova;
use <PERSON>vel\Nova\Nova;

class FilterServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            Nova::script('{{ component }}', __DIR__.'/../dist/js/filter.js');
            Nova::style('{{ component }}', __DIR__.'/../dist/css/filter.css');
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
