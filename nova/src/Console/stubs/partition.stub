<?php

namespace {{ namespace }};

use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Partition;

class {{ class }} extends Partition
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->count($request, Model::class, 'groupByColumn');
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return  \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'uri-key';
    }
}
