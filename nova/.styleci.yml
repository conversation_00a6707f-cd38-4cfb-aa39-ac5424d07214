php:
    preset: laravel

css:
    tab-width: 2
    use-tabs: false
    print-width: 80
    double-quotes: false
    finder:
        exclude:
            - modules
            - node_modules
            - storage
            - vendor
            - public
        name:
            - '*.css'
            - '*.scss'
            - '*.less'

vue:
    tab-width: 2
    use-tabs: false
    print-width: 80
    double-quotes: false
    trailing-commas: es5
    semicolons: false
    arrow-parens: avoid
    bracket-spacing: true
    finder:
        exclude:
            - src/Console/asset-stubs/resources/js
            - src/Console/card-stubs/resources/js
            - src/Console/field-stubs/resources/js
            - src/Console/filter-stubs/resources/js
            - src/Console/resource-tool-stubs/resources/js
            - src/Console/tool-stubs/resources/js
            - modules
            - node_modules
            - storage
            - vendor
        name: '*.vue'

js:
    tab-width: 2
    use-tabs: false
    print-width: 80
    double-quotes: false
    trailing-commas: es5
    semicolons: false
    arrow-parens: avoid
    bracket-spacing: true
    finder:
        exclude:
            - public
            - modules
            - node_modules
            - storage
            - vendor
        name:
            - '*.js'
            - '*.jsx'
        not-name:
            - '*.min.js'
