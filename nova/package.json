{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "test": "jest", "watch:test": "jest --watch"}, "devDependencies": {"@vue/test-utils": "^1.0.0-beta.16", "axios": "^0.21.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^23.0.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.6.1", "cross-env": "^5.0.0", "flush-promises": "^1.0.0", "jest": "^23.0.1", "jest-mock-axios": "^2.1.11", "jest-serializer-vue": "^2.0.0", "laravel-mix": "^1.0", "lodash": "^4.17.0", "tailwindcss": "^0.6.0", "vue": "^2.5.0", "vue-jest": "^2.6.0", "vue-router": "^3.0.1", "vue-server-renderer": "^2.5.16"}, "dependencies": {"autosize": "^4.0.2", "chartist": "^0.11.0", "chartist-plugin-tooltips": "^0.0.17", "codemirror": "^5.36.0", "flatpickr": "4.6.7", "form-backend-validation": "^2.3.3", "inflector-js": "^1.0.1", "laravel-nova": "^1.9.0", "laravel-vapor": "^0.4.0", "markdown-it": "^8.4.1", "moment": "^2.22.2", "moment-timezone": "^0.5.21", "mousetrap": "^1.6.3", "numbro": "^2.1.2", "places.js": "^1.7.3", "popper.js": "^1.14.3", "portal-vue": "^2.1.6", "slugify": "^1.4.5", "trix": "^1.0.0", "v-tooltip": "^2.0.2", "vue-async-computed": "^3.3.1", "vue-clickaway": "^2.2.2", "vue-meta": "^2.4.0", "vue-toasted": "^1.1.24", "vuex": "^3.1.1"}}