<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         beStrictAboutTestsThatDoNotTestAnything="false"
         bootstrap="vendor/autoload.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
    <testsuites>
        <testsuite name="Package Tests">
            <directory suffix="Test.php">./tests/Controller</directory>
            <directory suffix="Test.php">./tests/Feature</directory>
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./src</directory>
        </whitelist>
    </filter>
    <php>
        <env name="APP_KEY" value="base64:la8jDWcqBHfGO6PR+OA9FAZqdi0XQKuhnzqc5tUATZs="/>
        <!--
        <env name="REDIS_CLIENT" value="predis"/>

        <env name="RUN_MYSQL_TESTS" value="false"/>
        <env name="RUN_POSTGRES_TESTS" value="false"/>
        -->
    </php>
</phpunit>
