# Limit to only `issues` or `pulls`
only: pulls
# Number of days of inactivity before an issue becomes stale
daysUntilStale: 30
# Number of days of inactivity before a stale issue is closed
daysUntilClose: 3
  # Label to use when marking an issue as stale
  staleLabel: stale
  # Issues with these labels will never be considered stale
  exemptLabels:
    - ready
    - needs docs
  # Comment to post when closing a stale issue. Set to `false` to disable
  closeComment: false  
  # Comment to post when marking an issue as stale. Set to `false` to disable
  markComment: >
  This pull request has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs. Thank you
  for your contributions.
