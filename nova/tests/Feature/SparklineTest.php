<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use <PERSON><PERSON>\Nova\Fields\Sparkline;
use <PERSON><PERSON>\Nova\Tests\IntegrationTest;

class SparklineTest extends IntegrationTest
{
    public function test_can_change_chart_style()
    {
        $field = Sparkline::make('Values')->data([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0,
        ])->asBarChart();

        $field->resolve(null);
        $this->assertEquals('Bar', $field->chartStyle);
    }
}
