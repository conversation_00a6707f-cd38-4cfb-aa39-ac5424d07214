<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use <PERSON><PERSON>\Nova\Tests\Fixtures\User;
use <PERSON><PERSON>\Nova\Tests\Fixtures\UserResource;
use <PERSON>vel\Nova\Tests\IntegrationTest;

class ResourceTest extends IntegrationTest
{
    public function test_can_use_title_from_json_attribute()
    {
        $user = factory(User::class)->create([
            'meta' => ['name' => '<PERSON> Otwell'],
        ]);

        UserResource::$title = 'meta.name';
        $resource = new UserResource($user);

        $this->assertSame('<PERSON> Otwell', $resource->title());
    }

    public function test_can_use_title_from_array_attribute()
    {
        $user = factory(User::class)->create([
            'meta' => ['Taylor Otwell'],
        ]);

        UserResource::$title = 'meta.0';
        $resource = new UserResource($user);

        $this->assertSame('<PERSON> Otwell', $resource->title());
    }
}
