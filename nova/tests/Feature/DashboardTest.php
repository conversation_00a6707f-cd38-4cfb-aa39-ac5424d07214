<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Dashboard;
use <PERSON>vel\Nova\Nova;
use <PERSON>vel\Nova\Tests\IntegrationTest;

class DashboardTest extends IntegrationTest
{
    public function setUp(): void
    {
        parent::setUp();
    }

    public function test_authorization_callback_is_executed()
    {
        Nova::dashboards([
            new class () extends Dashboard {
                public function authorize(Request $request)
                {
                    return false;
                }
            },
        ]);

        $this->assertCount(0, Nova::availableDashboards(Request::create('/')));
    }
}
