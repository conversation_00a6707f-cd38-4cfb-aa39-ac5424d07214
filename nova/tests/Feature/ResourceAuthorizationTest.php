<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use Illuminate\Support\Facades\Gate;
use <PERSON>vel\Nova\Tests\Fixtures\Tag;
use <PERSON>vel\Nova\Tests\Fixtures\TagPolicy;
use <PERSON>vel\Nova\Tests\Fixtures\TagResource;
use <PERSON>vel\Nova\Tests\IntegrationTest;

class ResourceAuthorizationTest extends IntegrationTest
{
    public function setUp(): void
    {
        parent::setUp();
    }

    public function test_resource_is_automatically_authorizable_if_it_has_policy()
    {
        $this->assertFalse(TagResource::authorizable());

        Gate::policy(Tag::class, TagPolicy::class);

        $this->assertTrue(TagResource::authorizable());
    }
}
