<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Nova\Fields\Trix;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Tests\Fixtures\Post;
use <PERSON>vel\Nova\Tests\IntegrationTest;

class TrixTest extends IntegrationTest
{
    public function setUp(): void
    {
        parent::setUp();
    }

    public function test_fields_can_execute_custom_filling_callback()
    {
        $field = Trix::make('Trix key')->fillUsing(
            function (
                NovaRequest $request,
                Model $model,
                string $attribute,
                string $requestAttribute
            ) {
                return function () use ($request, $model, $attribute, $requestAttribute) {
                    $this->assertInstanceOf(Post::class, $model);
                    $this->assertEquals('trix_key', $attribute);
                    $this->assertEquals('trix_key', $requestAttribute);
                    $this->assertEquals('TRIX_DATA', $request->{$attribute});
                };
            }
        );

        $model = new Post();
        $result = $field->fill(NovaRequest::create('/?trix_key=TRIX_DATA'), $model);

        $this->assertIsCallable($result);

        $result();
    }
}
