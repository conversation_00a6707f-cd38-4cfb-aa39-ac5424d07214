<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Nova;
use <PERSON>vel\Nova\Tests\IntegrationTest;
use <PERSON><PERSON>\Nova\Tool;

class ToolTest extends IntegrationTest
{
    public function setUp(): void
    {
        parent::setUp();
    }

    public function test_authorization_callback_is_executed()
    {
        Nova::tools([
            new class () extends Tool {
                public function authorize(Request $request)
                {
                    return false;
                }
            },
        ]);

        $this->assertCount(0, Nova::availableTools(Request::create('/')));
    }
}
