<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use <PERSON><PERSON>\Nova\Nova;
use <PERSON>vel\Nova\Tests\IntegrationTest;

class AuthorizationTest extends IntegrationTest
{
    public function setUp(): void
    {
        parent::setUp();
    }

    public function test_authorization_callback_is_executed()
    {
        Nova::auth(function ($request) {
            return $request;
        });

        $this->assertEquals('<PERSON>', Nova::check('<PERSON>'));
    }
}
