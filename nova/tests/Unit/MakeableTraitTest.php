<?php

namespace <PERSON><PERSON>\Nova\Tests\Unit;

use <PERSON><PERSON>\Nova\Makeable;
use <PERSON>vel\Nova\Tests\IntegrationTest;

class MakeableTraitTest extends IntegrationTest
{
    public function test_makeable_trait_works()
    {
        $instance = MakeableTest::make('<PERSON>', '<PERSON>');

        $this->assertEquals('<PERSON>', $instance->first);
        $this->assertEquals('Tess', $instance->second);
    }
}

class MakeableTest
{
    use Makeable;

    public $first;
    public $second;

    public function __construct($first, $second)
    {
        $this->first = 'David';
        $this->second = 'Tess';
    }
}
