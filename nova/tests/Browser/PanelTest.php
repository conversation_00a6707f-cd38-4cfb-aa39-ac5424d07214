<?php

namespace <PERSON><PERSON>\Nova\Tests\Browser;

use App\Models\User;
use Database\Factories\AddressFactory;
use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Nova\Testing\Browser\Pages\Detail;
use <PERSON><PERSON>\Nova\Testing\Browser\Pages\Update;
use <PERSON><PERSON>\Nova\Tests\DuskTestCase;

/**
 * @group external-network
 */
class PanelTest extends DuskTestCase
{
    /**
     * @test
     */
    public function fields_can_be_placed_into_panels()
    {
        $address = AddressFactory::new()->create();

        $this->browse(function (Browser $browser) use ($address) {
            $browser->loginAs(User::find(1))
                    ->visit(new Detail('addresses', $address->id))
                    ->assertSee('More Address Details');

            $browser->blank();
        });
    }

    /**
     * @test
     */
    public function fields_can_be_placed_into_edit_panels()
    {
        $address = AddressFactory::new()->create();

        $this->browse(function (Browser $browser) use ($address) {
            $browser->loginAs(User::find(1))
                ->visit(new Update('addresses', $address->id))
                ->assertSee('More Address Details');

            $browser->blank();
        });
    }
}
