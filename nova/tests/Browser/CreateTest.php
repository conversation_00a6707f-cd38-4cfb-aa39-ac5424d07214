<?php

namespace <PERSON><PERSON>\Nova\Tests\Browser;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Nova\Testing\Browser\Pages\Create;
use <PERSON><PERSON>\Nova\Tests\DuskTestCase;

class CreateTest extends DuskTestCase
{
    /**
     * @test
     */
    public function resource_can_be_created()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs(User::find(1))
                    ->visit(new Create('users'))
                    ->type('@name', 'Adam Wathan')
                    ->type('@email', '<EMAIL>')
                    ->type('@password', 'secret')
                    ->create();

            $user = User::orderBy('id', 'desc')->first();

            $browser->assertPathIs('/nova/resources/users/'.$user->id);

            $this->assertEquals('Adam Wathan', $user->name);
            $this->assertEquals('<EMAIL>', $user->email);
            $this->assertTrue(Hash::check('secret', $user->password));
            $this->assertTrue($user->active);

            $browser->blank();
        });
    }

    /**
     * @test
     */
    public function validation_errors_are_displayed()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs(User::find(1))
                    ->visit(new Create('users'))
                    ->create()
                    ->waitForText('There was a problem submitting the form.', 15)
                    ->assertSee('The Name field is required.')
                    ->assertSee('The Email field is required.')
                    ->assertSee('The Password field is required.');

            $browser->blank();
        });
    }

    /**
     * @test
     */
    public function resource_can_be_created_and_another_resource_can_be_added()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs(User::find(1))
                    ->visit(new Create('users'))
                    ->type('@name', 'Adam Wathan')
                    ->type('@email', '<EMAIL>')
                    ->type('@password', 'secret')
                    ->createAndAddAnother();

            $user = User::orderBy('id', 'desc')->first();

            $browser->assertPathIs('/nova/resources/users/new');

            $this->assertEquals('Adam Wathan', $user->name);
            $this->assertEquals('<EMAIL>', $user->email);
            $this->assertTrue(Hash::check('secret', $user->password));

            $browser->blank();
        });
    }
}
