<?php

namespace <PERSON><PERSON>\Nova\Tests\Browser;

use App\Models\User;
use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Nova\Testing\Browser\Components\IndexComponent;
use <PERSON><PERSON>\Nova\Testing\Browser\Pages\Create;
use <PERSON><PERSON>\Nova\Testing\Browser\Pages\Detail;
use <PERSON><PERSON>\Nova\Tests\DuskTestCase;

class CreateWithHasOneTest extends DuskTestCase
{
    public function test_has_one_should_be_filled()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs(User::find(1))
                ->visit(new Create('people'))
                ->type('@name', '<PERSON>an')
                ->create()
                ->visit(new Detail('people', 1))
                ->within(new IndexComponent('employees'), function ($browser) {
                    $browser->click('@create-button');
                })
                ->waitFor('[data-testid="content"] form', 25)
                ->assertDisabled('@people');

            $browser->blank();
        });
    }
}
