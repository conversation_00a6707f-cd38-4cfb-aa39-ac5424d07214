<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Panel;
use <PERSON><PERSON>\Nova\Resource;

class PanelResource extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \Laravel\Nova\Tests\Fixtures\User::class;

    /**
     * Indicates if the resource should be globally searchable.
     *
     * @var bool
     */
    public static $globallySearchable = false;

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            new Panel('Basics', [
                ID::make('ID', 'id'),

                Text::make('Name', 'name')
                            ->creationRules('required', 'string', 'max:255')
                            ->updateRules('required', 'string', 'max:255'),

                $this->when(false, function () {
                    return Text::make('Exclude', 'exclude');
                }),
            ]),

            Text::make('Email', 'email'),
            Text::make('Password', 'password'),

            HasMany::make('Posts', 'posts', PostResource::class),
            BelongsToMany::make('Roles', 'roles', RoleResource::class),

            new Panel('Extra', [
                $this->when(true, function () {
                    return Text::make('Include', 'include');
                }),
            ]),
        ];
    }

    /**
     * Get the URI key for the resource.
     *
     * @return string
     */
    public static function uriKey()
    {
        return 'panels';
    }
}
