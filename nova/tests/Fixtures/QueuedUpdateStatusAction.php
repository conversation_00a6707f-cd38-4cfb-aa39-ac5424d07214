<?php

namespace Lara<PERSON>\Nova\Tests\Fixtures;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class QueuedUpdateStatusAction extends Action implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return string|void
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $this->markAsFailed($models->where('id', 1)->first(), 'Test Message');
        $this->markAsFinished($models->where('id', 2)->first());
    }
}
