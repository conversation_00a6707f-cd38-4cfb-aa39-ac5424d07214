<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Text;

class RequiredFieldAction extends Action
{
    public static $applied = [];
    public static $appliedFields = [];

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return string|void
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        static::$applied[] = $models;
        static::$appliedFields[] = $fields;

        return Action::message('Hello World');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Text::make('Test', 'test')->rules('required'),
        ];
    }
}
