<?php

namespace Lara<PERSON>\Nova\Tests\Fixtures;

use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class UnauthorizedAction extends Action
{
    use ProvidesActionFields;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return string|void
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        //
    }
}
