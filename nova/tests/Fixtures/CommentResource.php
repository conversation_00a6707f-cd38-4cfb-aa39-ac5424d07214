<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\MorphTo;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Resource;

class CommentResource extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \Laravel\Nova\Tests\Fixtures\Comment::class;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'body',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make('ID', 'id'),
            MorphTo::make('Commentable', 'commentable')->display([PostResource::class => function ($resource) {
                return $resource->title;
            }])->types([
                PostResource::class => 'Post',
            ])
            ->viewable($_SERVER['nova.comment.viewable-field'] ?? true)
            ->searchable()
            ->default($_SERVER['nova.user.default-value'] ?? null)
            ->defaultResource($_SERVER['nova.user.default-resource'] ?? null),
            BelongsTo::make('Author', 'author', UserResource::class),
            Text::make('Body', 'body')->rules('required', 'string', 'max:255'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [new IdFilter()];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [new NoopAction()];
    }

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest $request
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        if (isset($_SERVER['nova.comments.useEager'])) {
            return $query->with('commentable', 'author');
        }

        return $query;
    }

    /**
     * Build a "relatable" query for the posts.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function relatablePosts(NovaRequest $request, $query)
    {
        if (! isset($_SERVER['nova.comment.useCustomRelatablePosts'])) {
            return PostResource::relatableQuery($request, $query);
        }

        $_SERVER['nova.comment.relatablePosts'] = $query;

        return $query->where('id', '<', 3);
    }

    /**
     * Get the URI key for the resource.
     *
     * @return string
     */
    public static function uriKey()
    {
        return 'comments';
    }
}
