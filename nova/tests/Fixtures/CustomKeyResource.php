<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Resource;

class CustomKeyResource extends Resource
{
    public static $model = CustomKey::class;

    public static function uriKey()
    {
        return 'custom-keys';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(),
        ];
    }
}
