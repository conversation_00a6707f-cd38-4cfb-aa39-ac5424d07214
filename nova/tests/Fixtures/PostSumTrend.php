<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Metrics\Trend;
use <PERSON><PERSON>\Nova\Metrics\Value;

class PostSumTrend extends Trend
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function calculate(Request $request)
    {
        return $this->sum(
            $request,
            Post::class,
            $_SERVER['nova.postCountUnit'] ?? Trend::BY_MONTHS,
            'word_count'
        );
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return $_SERVER['nova.postCountRanges'] ?? [
            6 => 'Last 6 Months',
            12 => 'Last 12 Months',
        ];
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'post-sum-trend';
    }
}
