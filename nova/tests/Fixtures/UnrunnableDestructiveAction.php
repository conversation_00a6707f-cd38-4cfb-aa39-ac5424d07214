<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\DestructiveAction;
use <PERSON>vel\Nova\Fields\ActionFields;

class UnrunnableDestructiveAction extends DestructiveAction
{
    use ProvidesActionFields;

    public static $applied = [];

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return string|void
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        static::$applied[] = $models;
    }
}
