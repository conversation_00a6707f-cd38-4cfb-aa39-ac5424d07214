<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

trait ProvidesActionFields
{
    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Text::make('Test', 'test')
                ->suggestions([
                    'Hello',
                    'World',
                ]),

            new class ('Callback', 'callback') extends Text {
                public function fill(NovaRequest $request, $model)
                {
                    return function () {
                        return 'callback';
                    };
                }
            },
        ];
    }
}
