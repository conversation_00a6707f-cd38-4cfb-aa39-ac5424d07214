<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\DestructiveAction as BaseDestructiveAction;
use <PERSON><PERSON>\Nova\Fields\ActionFields;

class DestructiveAction extends BaseDestructiveAction
{
    use ProvidesActionFields;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return string|void
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        return Action::message('Hello World');
    }
}
