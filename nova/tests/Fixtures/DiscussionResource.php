<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Trix;
use <PERSON><PERSON>\Nova\Resource;

class DiscussionResource extends Resource
{
    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static $group = 'Content';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \Laravel\Nova\Tests\Fixtures\Discussion::class;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'title',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            BelongsTo::make('User', 'user', UserResource::class),
            Text::make('Title')->rules('required', 'string', 'max:255'),
            Trix::make('Body')->rules('required')->withFiles('public'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [
            //
        ];
    }

    /**
     * Get the URI key for the resource.
     *
     * @return string
     */
    public static function uriKey()
    {
        return 'discussions';
    }
}
