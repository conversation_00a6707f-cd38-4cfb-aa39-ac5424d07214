<?php

namespace <PERSON><PERSON>\Nova\Tests\Fixtures;

use <PERSON><PERSON>\Nova\Contracts\Cover;
use <PERSON><PERSON>\Nova\Fields\Avatar;
use <PERSON>vel\Nova\Fields\Field;

class GitHubAvatar extends Avatar implements Cover
{
    /**
     * Resolve the thumbnail URL for the field.
     *
     * @return string|null
     */
    public function resolveThumbnailUrl()
    {
        return 'https://github.com/taylorotwell.png?size=40';
    }
}
