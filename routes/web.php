<?php

use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
//
Route::get('/ikonlar', function () {
    return view('icons');
});
//
//Route::get('/anasayfa', function () {
//    return view('dashboard');
//})->middleware(['auth'])->name('dashboard');

Route::post('/iletisim-form', [\App\Http\Controllers\PostController::class, 'ContactForm']);
Route::post('/sertifika-form', [\App\Http\Controllers\PostController::class, 'CertificateForm']);
Route::post('/sertifika-on-form', [\App\Http\Controllers\PostController::class, 'CertificateForm2']);
Route::post('/kurs-on-form', [\App\Http\Controllers\PostController::class, 'CourseForm']);
Route::post('/egitmenlik-basvurusu-form', [\App\Http\Controllers\PostController::class, 'InstructorForm']);
Route::post('/egitimlerimiz/egitim-onerileriniz/{formSubmitting?}', [\App\Http\Controllers\PostController::class, 'SuggestingForm']);

Route::get('/', [HomeController::class, 'Anasayfa']);

Route::get('/hakkimizda/{slug}/', [HomeController::class, 'About']);
Route::get('/haberler/{slug}/', [HomeController::class, 'News']);

Route::get('/egitmenlik-basvurusu', function () {
    return view('instructor-application');
});

Route::get('/egitimlerimiz/sertifika-sorgula', function () {
    return view('query-certificate');
});
//Route::get('/egitimlerimiz/egitim-onerileriniz', function () {
//    return view('training-suggestions');
//});
Route::get('/egitimlerimiz/egitim-onerileriniz/{formSubmitting?}', [HomeController::class, 'SuggestionForm']);

Route::get('/egitimlerimiz/{slug}', [HomeController::class, 'Education']);
Route::get('/egitimlerimiz/dil-egitimlerimiz/{slug}', [HomeController::class, 'EducationDetail']);
Route::get('/egitimlerimiz/bireysel-egitimlerimiz/{slug}', [HomeController::class, 'CourseDetail']);

Route::get('/iletisim-ok', function () {
    return view('contact')->with(['formSubmitting' => 'OK',]);
});
Route::get('/sertifika-ok', function () {
    return view('query-certificate')->with(['formSubmitting' => 'OK',]);
});
Route::get('/egitmenlik-basvurusu-ok', function () {
    return view('instructor-application')->with(['formSubmitting' => 'OK',]);
});
Route::get('/sertifika-on-form-ok', function () {
    return view('instructor-application')->with(['formSubmitting' => 'OK',]);
});
Route::get('/kurs-on-form-ok', function () {
    return view('instructor-application')->with(['formSubmitting' => 'OK',]);
});
//Route::get('/egitimlerimiz/egitim-onerileriniz-ok', function () {
//    return view('training-suggestions')->with(['formSubmitting' => 'OK',]);
//});

Route::get('/{slug2}/{slug}', [HomeController::class, 'slug'])->where('slug2', '^((?!panel|nova|.png|.jpg|.gif|.jpeg).)*$');
Route::get('/{slug}', [HomeController::class, 'slug'])->where('slug', '^((?!panel|nova|.png|.jpg|.gif|.jpeg).)*$');
require __DIR__ . '/auth.php';
