<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCoursesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->text('menu_name')->nullable();
            $table->string('title');
            $table->integer('quota');
            $table->integer('subscriber');
            $table->string('start_date');
            $table->string('education_time');
            $table->string('education_type');
            $table->longText('content')->nullable();
            $table->longText('details')->nullable();
            $table->integer('order')->default(999)->nullable();
            $table->integer('is_active')->nullable();
            $table->integer('is_publish')->nullable();
            $table->text('seo_title')->nullable();
            $table->string('about');
            $table->text('meta_keywords')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('slug')->nullable();
            $table->string('image_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('courses');
    }
}
