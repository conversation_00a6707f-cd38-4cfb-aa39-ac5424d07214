const mix = require('laravel-mix');
require('laravel-mix-purgecss');

mix.js('resources/js/app.js', 'public/js').postCss('resources/css/app.css', 'public/css', [
    require('postcss-import'),
    require('tailwindcss'),
    require('autoprefixer'),
]).combine(
    [
        'resources/js/src/jquery.js',
        'resources/js/src/popper.min.js',
        'resources/js/src/bootstrap.min.js',
        'resources/js/src/owl.js',
        'resources/js/src/wow.js',
/*
        'resources/js/src/validation.js',
*/
        'resources/js/src/jquery.fancybox.js',
        'resources/js/src/appear.js',
        'resources/js/src/isotope.js',
/*
        'resources/js/src/jquery.nice-select.min.js',
*/
        'resources/js/src/nav-tool.js',
        'resources/js/src/jquery.paroller.min.js',
        'resources/js/src/script.js',
    ],
    'public/js/combined.js'
).version();

mix.css('resources/css/combined.css', 'css')
    .purgeCss({
        enabled: true,
    }).version();
