@import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css);
/*!
 * Font Awesome Pro 5.8.0 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 */
.fa,
.fas,
.fal,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1; }

@keyframes fa-spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */

.fa-angle-down:before {
  content: "\f107"; }

.fa-angle-left:before {
  content: "\f104"; }

.fa-angle-right:before {
  content: "\f105"; }

.fa-angle-up:before {
  content: "\f106"; }

.fa-clock:before {
  content: "\f017"; }

.fa-do-not-enter:before {
  content: "\f5ec"; }

.fa-envelope:before {
  content: "\f0e0"; }

.fa-facebook:before {
  content: "\f09a"; }

.fa-facebook-f:before {
  content: "\f39e"; }

.fa-facebook-square:before {
  content: "\f082"; }

.fa-globe:before {
  content: "\f0ac"; }

.fa-instagram:before {
  content: "\f16d"; }

.fa-link:before {
  content: "\f0c1"; }

.fa-linkedin:before {
  content: "\f08c"; }

.fa-map-marker:before {
  content: "\f041"; }

.fa-phone:before {
  content: "\f095"; }

.fa-times:before {
  content: "\f00d"; }

.fa-twitter:before {
  content: "\f099"; }
@font-face {
  font-family: 'Font Awesome 5 Brands';
  font-style: normal;
  font-weight: normal;
  font-display: auto;
  src: url("/fonts/fa-brands-400.eot");
  src: url("/fonts/fa-brands-400d41d.eot?#iefix") format("embedded-opentype"), url("/fonts/fa-brands-400.woff2") format("woff2"), url("/fonts/fa-brands-400.woff") format("woff"), url("/fonts/fa-brands-400.ttf") format("truetype"), url("/fonts/fa-brands-400.svg#fontawesome") format("svg"); }

.fab {
  font-family: 'Font Awesome 5 Brands'; }
@font-face {
  font-family: 'Font Awesome 5 Pro';
  font-style: normal;
  font-weight: 300;
  font-display: auto;
  src: url("/fonts/fa-light-300.eot");
  src: url("/fonts/fa-light-300d41d.eot?#iefix") format("embedded-opentype"), url("/fonts/fa-light-300.woff2") format("woff2"), url("/fonts/fa-light-300.woff") format("woff"), url("/fonts/fa-light-300.ttf") format("truetype"), url("/fonts/fa-light-300.svg#fontawesome") format("svg"); }

.fal {
  font-family: 'Font Awesome 5 Pro';
  font-weight: 300; }
@font-face {
  font-family: 'Font Awesome 5 Pro';
  font-style: normal;
  font-weight: 400;
  font-display: auto;
  src: url("/fonts/fa-regular-400.eot");
  src: url("/fonts/fa-regular-400d41d.eot?#iefix") format("embedded-opentype"), url("/fonts/fa-regular-400.woff2") format("woff2"), url("/fonts/fa-regular-400.woff") format("woff"), url("/fonts/fa-regular-400.ttf") format("truetype"), url("/fonts/fa-regular-400.svg#fontawesome") format("svg"); }
@font-face {
  font-family: 'Font Awesome 5 Pro';
  font-style: normal;
  font-weight: 900;
  font-display: auto;
  src: url("/fonts/fa-solid-900.eot");
  src: url("/fonts/fa-solid-900d41d.eot?#iefix") format("embedded-opentype"), url("/fonts/fa-solid-900.woff2") format("woff2"), url("/fonts/fa-solid-900.woff") format("woff"), url("/fonts/fa-solid-900.ttf") format("truetype"), url("/fonts/fa-solid-900.svg#fontawesome") format("svg"); }

.fa,
.fas {
  font-family: 'Font Awesome 5 Pro';
  font-weight: 900; }

@font-face {
  font-family: 'icomoon';
  src:  url('/fonts/icomoonc4db.eot?axc0in');
  src:  url('/fonts/icomoonc4db.eot?axc0in#iefix') format('embedded-opentype'),
    url('/fonts/icomoonc4db.ttf?axc0in') format('truetype'),
    url('/fonts/icomoonc4db.woff?axc0in') format('woff'),
    url('/fonts/icomoonc4db.svg?axc0in#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-1:before {
  content: "\e900";
}
.icon-2:before {
  content: "\e901";
}
.icon-3:before {
  content: "\e902";
}
.icon-4:before {
  content: "\e903";
}
.icon-5:before {
  content: "\e904";
}
.icon-6:before {
  content: "\e905";
}
.icon-7:before {
  content: "\e906";
}
.icon-8:before {
  content: "\e907";
}
.icon-9:before {
  content: "\e908";
}
.icon-10:before {
  content: "\e909";
}
.icon-11:before {
  content: "\e90a";
}
.icon-12:before {
  content: "\e90b";
}
.icon-13:before {
  content: "\e90c";
}
.icon-14:before {
  content: "\e90d";
}
.icon-15:before {
  content: "\e90e";
}
.icon-16:before {
  content: "\e90f";
}
.icon-17:before {
  content: "\e910";
}
.icon-18:before {
  content: "\e911";
}
.icon-19:before {
  content: "\e912";
}
.icon-20:before {
  content: "\e913";
}
.icon-21:before {
  content: "\e914";
}
.icon-22:before {
  content: "\e915";
}
.icon-23:before {
  content: "\e916";
}
.icon-24:before {
  content: "\e917";
}
.icon-25:before {
  content: "\e918";
}
.icon-26:before {
  content: "\e919";
}
.icon-27:before {
  content: "\e91a";
}
.icon-28:before {
  content: "\e91b";
}
.icon-29:before {
  content: "\e91c";
}
.icon-30:before {
  content: "\e91d";
}
.icon-31:before {
  content: "\e91e";
}
.icon-32:before {
  content: "\e91f";
}
.icon-33:before {
  content: "\e920";
}
.icon-34:before {
  content: "\e921";
}
.icon-35:before {
  content: "\e922";
}
.icon-36:before {
  content: "\e923";
}
.icon-37:before {
  content: "\e924";
}
.icon-38:before {
  content: "\e925";
}
.icon-39:before {
  content: "\e926";
}
.icon-40:before {
  content: "\e927";
}
.icon-41:before {
  content: "\e928";
}
.icon-42:before {
  content: "\e929";
}
.icon-43:before {
  content: "\e92a";
}
.icon-44:before {
  content: "\e92b";
}
.icon-45:before {
  content: "\e92c";
}
.icon-46:before {
  content: "\e92d";
}
.icon-47:before {
  content: "\e92e";
}
.icon-48:before {
  content: "\e92f";
}
.icon-49:before {
  content: "\e930";
}
.icon-50:before {
  content: "\e931";
}
.icon-51:before {
  content: "\e932";
}
.icon-52:before {
  content: "\e933";
}
.icon-53:before {
  content: "\e934";
}
.icon-54:before {
  content: "\e935";
}
.icon-55:before {
  content: "\e936";
}
.icon-56:before {
  content: "\e937";
}
.icon-57:before {
  content: "\e938";
}
.icon-58:before {
  content: "\e939";
}
.icon-59:before {
  content: "\e93a";
}
.icon-60:before {
  content: "\e93b";
}

/* 
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel .animated {
  animation-duration: 1000ms;
  animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
  z-index: 0;
}
.owl-carousel .owl-animated-out {
  z-index: 1;
}
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}
@keyframes fadeOut {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/* 
 * 	Owl Carousel - Auto Height Plugin
 */
.owl-height {
  transition: height 500ms ease-in-out;
}

/* 
 *  Core Owl Carousel CSS File
 */
.owl-carousel {
  display: none;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  /* position relative and z-index fix webkit rendering fonts issue */
  position: relative;
  z-index: 1;
}
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
}
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  /* fix for flashing background */
  -webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel.owl-loaded {
  display: block;
}
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}
.owl-carousel.owl-hidden {
  opacity: 0;
}
.owl-carousel .owl-refresh .owl-item {
  display: none;
}
.owl-carousel .owl-item {
  position: relative;
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.owl-carousel .owl-item img {
  display: block;
  width: none;
  -webkit-transform-style: preserve-3d;
}
.owl-carousel .owl-grab {
  cursor: move;
  cursor: -o-grab;
  cursor: -ms-grab;
  cursor: grab;
}
.owl-carousel.owl-rtl {
  direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* No Js */

/* 
 * 	Owl Carousel - Lazy Load Plugin
 */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 400ms ease;
}
.owl-carousel .owl-item img {
  transform-style: preserve-3d;
}

/* 
 * 	Owl Carousel - Video Plugin
 */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url("<!DOCTYPE html>\n<html style=\"height:100%\">\n<head>\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\" />\n<title> 404 Not Found
\n</title></head>\n<body style=\"color: #444; margin:0;font: normal 14px/20px Arial, Helvetica, sans-serif; height:100%; background-color: #fff;\">\n<div style=\"height:auto; min-height:100%; \">     <div style=\"text-align: center; width:800px; margin-left: -400px; position:absolute; top: 30%; left:50%;\">\n        <h1 style=\"margin:0; font-size:150px; line-height:150px; font-weight:bold;\">404</h1>\n<h2 style=\"margin-top:20px;font-size: 30px;\">Not Found
\n</h2>\n<p>The resource requested could not be found on this server!</p>\n</div></div><div style=\"color:#f0f0f0; font-size:12px;margin:auto;padding:0px 30px 0px 30px;position:relative;clear:both;height:100px;margin-top:-101px;background-color:#474747;border-top: 1px solid rgba(0,0,0,0.15);box-shadow: 0 1px 0 rgba(255, 255, 255, 0.3) inset;\">\n<br>Proudly powered by  <a style=\"color:#fff;\" href=\"http://www.litespeedtech.com/error-page\">LiteSpeed Web Server</a><p>Please be advised that LiteSpeed Technologies Inc. is not a web hosting company and, as such, has no control over content found on this site.</p></div></body></html>\n") no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: scale 100ms ease;
}
.owl-carousel .owl-video-play-icon:hover {
  transition: scale(1.3, 1.3);
}
.owl-carousel .owl-video-playing .owl-video-tn,
.owl-carousel .owl-video-playing .owl-video-play-icon {
  display: none;
}
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity 400ms ease;
}
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
}
.owl-theme .owl-dots .owl-dot {
    display: inline-block;
}
.owl-theme .owl-dots .owl-dot span {
  background: #222;
  display: block;
  margin: 0px 5px 0px 5px;
  transition: opacity 200ms ease 0s;
  width: 15px;
  height: 15px;
}
.owl-theme .owl-dots .owl-dot.active span {
  background: none repeat scroll 0 0 #cda274;
}

/*!
 * Bootstrap v4.4.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 The Bootstrap Authors
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
:root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #007bff;
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

 figure, footer, header, main, nav, section {
  display: block;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: .5rem;
  margin-left: 0;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
}

a:not([href]) {
  color: inherit;
  text-decoration: none;
}

a:not([href]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

select {
  word-wrap: normal;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button:not(:disabled),
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .h1 {
  font-size: 2.5rem;
}

h2, .h2 {
  font-size: 2rem;
}

h3, .h3 {
  font-size: 1.75rem;
}

h4, .h4 {
  font-size: 1.5rem;
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

small,
.small {
  font-size: 80%;
  font-weight: 400;
}

mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.figure {
  display: inline-block;
}

code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}

a > code {
  color: inherit;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

 .col, .col-sm-5, .col-sm-7, .col-sm-12, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-12, .col-lg-3, .col-lg-4, .col-lg-6, .col-lg-8, .col-lg-12, .col-xl-12 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

@media (min-width: 576px) {
  .col-sm-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-sm-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 992px) {
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 1200px) {
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
}

.table tbody + tbody {
  border-top: 2px solid #dee2e6;
}

.table-active,
.table-active > th,
.table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control:disabled, .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

select.form-control[size], select.form-control[multiple] {
  height: auto;
}

textarea.form-control {
  height: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.btn {
  display: inline-block;
  font-weight: 400;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}

.btn:hover {
  color: #212529;
  text-decoration: none;
}

.btn:focus, .btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn.disabled, .btn:disabled {
  opacity: 0.65;
}

a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}


.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}


.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}


.show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}


.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}


.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}


.show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}


.show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}


.show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}


.show > .btn-warning.dropdown-toggle {
  color: #212529;
  background-color: #d39e00;
  border-color: #c69500;
}


.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}


.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #bd2130;
  border-color: #b21f2d;
}


.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}


.show > .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}


.show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}


.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}


.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}


.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}


.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}


.show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}


.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}


.show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}


.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}


.show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}


.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}


.show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}


.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}


.show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}


.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}


.show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}


.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}


.show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}


.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.fade {
  transition: opacity 0.15s linear;
}

@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}

.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}

.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}

.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"] {
  right: auto;
  bottom: auto;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}

.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #007bff;
}

.dropdown-item.disabled, .dropdown-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}


.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}


.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #007bff;
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid {
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
}

.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .nav-link.show {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .nav-link.show {
  color: #fff;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

.card > hr {
  margin-right: 0;
  margin-left: 0;
}

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-success hr {
  border-top-color: #b1dfbb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-danger hr {
  border-top-color: #f1b0b7;
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.media {
  display: flex;
  align-items: flex-start;
}

.media-body {
  flex: 1;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .5;
}

.close:hover {
  color: #000;
  text-decoration: none;
}

.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {
  opacity: .75;
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

a.close.disabled {
  pointer-events: none;
}

.toast {
  max-width: 350px;
  overflow: hidden;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  opacity: 0;
  border-radius: 0.25rem;
}

.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}

.toast.showing {
  opacity: 1;
}

.toast.show {
  display: block;
  opacity: 1;
}

.toast.hide {
  display: none;
}

.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.modal.show .modal-dialog {
  transform: none;
}

.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 1rem);
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.show {
  opacity: 0.9;
}

.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}

.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}

.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}

.popover .arrow::before, .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 0.5rem 0.75rem;
  color: #212529;
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}


.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-left),
.active.carousel-item-right {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-right),
.active.carousel-item-left {
  transform: translateX(-100%);
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}

.bg-white {
  background-color: #fff !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.border-top {
  border-top: 1px solid #dee2e6 !important;
}

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.d-none {
  display: none !important;
}

.d-flex {
  display: flex !important;
}

@media (min-width: 576px) {
  .d-sm-block {
    display: block !important;
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-block {
    display: block !important;
  }
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.align-items-center {
  align-items: center !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.position-static {
  position: static !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.w-auto {
  width: auto !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.ml-1 {
  margin-left: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.ml-2 {
  margin-left: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mr-3 {
  margin-right: 1rem !important;
}

.ml-3 {
  margin-left: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4 {
  margin-left: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}


.px-1 {
  padding-right: 0.25rem !important;
}


.py-1 {
  padding-bottom: 0.25rem !important;
}


.px-1 {
  padding-left: 0.25rem !important;
}


.py-2 {
  padding-top: 0.5rem !important;
}


.py-2 {
  padding-bottom: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pl-3 {
  padding-left: 1rem !important;
}


.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}


.py-4 {
  padding-bottom: 1.5rem !important;
}


.px-4 {
  padding-left: 1.5rem !important;
}


.mx-auto {
  margin-right: auto !important;
}


.mx-auto {
  margin-left: auto !important;
}

.text-center {
  text-align: center !important;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #007bff !important;
}

a.text-primary:hover, a.text-primary:focus {
  color: #0056b3 !important;
}

.text-muted {
  color: #6c757d !important;
}

.visible {
  visibility: visible !important;
}

@media print {
  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.btn) {
    text-decoration: underline;
  }
  pre {
    white-space: pre-wrap !important;
  }
  pre {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  body {
    min-width: 992px !important;
  }
  .container {
    min-width: 992px !important;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
}
@charset "UTF-8";body.fancybox-active{overflow:hidden}body.fancybox-iosfix{position:fixed;left:0;right:0}.fancybox-is-hidden{position:absolute;top:-9999px;left:-9999px;visibility:hidden}.fancybox-container{position:fixed;top:0;left:0;width:100%;height:100%;z-index:99992;-webkit-tap-highlight-color:transparent;-webkit-backface-visibility:hidden;backface-visibility:hidden;transform:translateZ(0);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif}.fancybox-bg,.fancybox-inner,.fancybox-stage{position:absolute;top:0;right:0;bottom:0;left:0}.fancybox-bg{background:#1e1e1e;opacity:0;transition-duration:inherit;transition-property:opacity;transition-timing-function:cubic-bezier(.47,0,.74,.71)}.fancybox-is-open .fancybox-bg{opacity:.87;transition-timing-function:cubic-bezier(.22,.61,.36,1)}.fancybox-caption-wrap,.fancybox-infobar,.fancybox-toolbar{position:absolute;direction:ltr;z-index:99997;opacity:0;visibility:hidden;transition:opacity .25s,visibility 0s linear .25s;box-sizing:border-box}.fancybox-show-caption .fancybox-caption-wrap,.fancybox-show-infobar .fancybox-infobar,.fancybox-show-toolbar .fancybox-toolbar{opacity:1;visibility:visible;transition:opacity .25s,visibility 0s}.fancybox-infobar{top:0;left:0;font-size:13px;padding:0 10px;height:44px;min-width:44px;line-height:44px;color:#ccc;text-align:center;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;-webkit-touch-callout:none;-webkit-tap-highlight-color:transparent;-webkit-font-smoothing:subpixel-antialiased;mix-blend-mode:exclusion}.fancybox-toolbar{top:0;right:0;margin:0;padding:0}.fancybox-stage{overflow:hidden;direction:ltr;z-index:99994;-webkit-transform:translateZ(0)}.fancybox-is-closing .fancybox-stage{overflow:visible}.fancybox-slide{position:absolute;top:0;left:0;width:100%;height:100%;margin:0;padding:0;overflow:auto;outline:none;white-space:normal;box-sizing:border-box;text-align:center;z-index:99994;-webkit-overflow-scrolling:touch;display:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;transition-property:transform,opacity}.fancybox-slide:before{content:"";display:inline-block;vertical-align:middle;height:100%;width:0}.fancybox-is-sliding .fancybox-slide,.fancybox-slide--current,.fancybox-slide--next,.fancybox-slide--previous{display:block}.fancybox-slide--next{z-index:99995}.fancybox-slide>*{display:inline-block;position:relative;padding:24px;margin:44px 0;border-width:0;vertical-align:middle;text-align:left;background-color:#fff;overflow:auto;box-sizing:border-box}.fancybox-slide>base,.fancybox-slide>link,.fancybox-slide>meta,.fancybox-slide>script,.fancybox-slide>style,.fancybox-slide>title{display:none}.fancybox-slide .fancybox-image-wrap{position:absolute;top:0;left:0;margin:0;padding:0;border:0;z-index:99995;background:transparent;cursor:default;overflow:visible;transform-origin:top left;background-size:100% 100%;background-repeat:no-repeat;-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-user-select:none;-moz-user-select:none;user-select:none;transition-property:transform,opacity}.fancybox-can-zoomOut .fancybox-image-wrap{cursor:zoom-out}.fancybox-can-zoomIn .fancybox-image-wrap{cursor:zoom-in}.fancybox-can-drag .fancybox-image-wrap{cursor:grab}.fancybox-image,.fancybox-spaceball{position:absolute;top:0;left:0;width:100%;height:100%;margin:0;padding:0;border:0;max-width:none;max-height:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}.fancybox-spaceball{z-index:1}.fancybox-iframe{display:block;padding:0;border:0;height:100%}.fancybox-error,.fancybox-iframe{margin:0;width:100%;background:#fff}.fancybox-error{padding:40px;max-width:380px;cursor:default}.fancybox-error p{margin:0;padding:0;color:#444;font-size:16px;line-height:20px}.fancybox-button{box-sizing:border-box;display:inline-block;vertical-align:top;width:44px;height:44px;margin:0;padding:10px;border:0;border-radius:0;background:rgba(30,30,30,.6);transition:color .3s ease;cursor:pointer;outline:none}.fancybox-button,.fancybox-button:link,.fancybox-button:visited{color:#ccc}.fancybox-button:focus,.fancybox-button:hover{color:#fff}.fancybox-button[disabled]{color:#ccc;cursor:default;opacity:.6}.fancybox-button svg{display:block;position:relative;overflow:visible;shape-rendering:geometricPrecision}.fancybox-button svg path{fill:currentColor;stroke:currentColor;stroke-linejoin:round;stroke-width:3}.fancybox-button--share svg path{stroke-width:1}.fancybox-button--pause svg path:nth-child(1),.fancybox-button--play svg path:nth-child(2){display:none}.fancybox-button--zoom svg path{fill:transparent}.fancybox-navigation{display:none}.fancybox-show-nav .fancybox-navigation{display:block}.fancybox-navigation button{position:absolute;top:50%;margin:-50px 0 0;z-index:99997;background:transparent;width:60px;height:100px;padding:17px}.fancybox-navigation button:before{content:"";position:absolute;top:30px;right:10px;width:40px;height:40px;background:rgba(30,30,30,.6)}.fancybox-navigation .fancybox-button--arrow_left{left:0}.fancybox-navigation .fancybox-button--arrow_right{right:0}.fancybox-close-small{position:absolute;top:0;right:0;width:40px;height:40px;padding:0;margin:0;border:0;border-radius:0;background:transparent;z-index:10;cursor:pointer}.fancybox-close-small:after{content:"×";position:absolute;top:5px;right:5px;width:30px;height:30px;font:22px/30px Arial,Helvetica Neue,Helvetica,sans-serif;color:#888;font-weight:300;text-align:center;border-radius:50%;border-width:0;background-color:transparent;transition:background-color .25s;box-sizing:border-box;z-index:2}.fancybox-close-small:focus{outline:none}.fancybox-close-small:focus:after{outline:1px dotted #888}.fancybox-close-small:hover:after{color:#555;background:#eee}.fancybox-is-scaling .fancybox-close-small,.fancybox-is-zoomable.fancybox-can-drag .fancybox-close-small{display:none}.fancybox-caption-wrap{bottom:0;left:0;right:0;padding:60px 2vw 0;background:linear-gradient(180deg,transparent 0,rgba(0,0,0,.1) 20%,rgba(0,0,0,.2) 40%,rgba(0,0,0,.6) 80%,rgba(0,0,0,.8));pointer-events:none}.fancybox-caption{padding:30px 0;border-top:1px solid hsla(0,0%,100%,.4);font-size:14px;color:#fff;line-height:20px;-webkit-text-size-adjust:none}.fancybox-caption a,.fancybox-caption button,.fancybox-caption select{pointer-events:all;position:relative}.fancybox-caption a{color:#fff;text-decoration:underline}.fancybox-slide>.fancybox-loading{border:6px solid hsla(0,0%,39%,.4);border-top:6px solid hsla(0,0%,100%,.6);border-radius:100%;height:50px;width:50px;animation:a .8s infinite linear;background:transparent;position:absolute;top:50%;left:50%;margin-top:-30px;margin-left:-30px;z-index:99999}@keyframes a{0%{transform:rotate(0deg)}to{transform:rotate(359deg)}}.fancybox-animated{transition-timing-function:cubic-bezier(0,0,.25,1)}.fancybox-share{padding:30px;border-radius:3px;background:#f4f4f4;max-width:90%;text-align:center}.fancybox-share h1{color:#222;margin:0 0 20px;font-size:35px;font-weight:700}.fancybox-share p{margin:0;padding:0}p.fancybox-share__links{margin-right:-10px}.fancybox-share__button{display:inline-block;text-decoration:none;margin:0 10px 10px 0;padding:0 15px;min-width:130px;border:0;border-radius:3px;background:#fff;white-space:nowrap;font-size:14px;font-weight:700;line-height:40px;-webkit-user-select:none;-moz-user-select:none;user-select:none;color:#fff;transition:all .2s}.fancybox-share__button:hover{text-decoration:none}.fancybox-share__button--fb{background:#3b5998}.fancybox-share__button--fb:hover{background:#344e86}.fancybox-share__button--pt{background:#bd081d}.fancybox-share__button--pt:hover{background:#aa0719}.fancybox-share__button--tw{background:#1da1f2}.fancybox-share__button--tw:hover{background:#0d95e8}.fancybox-share__button svg{position:relative;top:-1px;width:25px;height:25px;margin-right:7px;vertical-align:middle}.fancybox-share__button svg path{fill:#fff}.fancybox-share__input{box-sizing:border-box;width:100%;margin:10px 0 0;padding:10px 15px;background:transparent;color:#5d5b5b;font-size:14px;outline:none;border:0;border-bottom:2px solid #d7d7d7}.fancybox-thumbs{display:none;position:absolute;top:0;bottom:0;right:0;width:212px;margin:0;padding:2px 2px 4px;background:#fff;-webkit-tap-highlight-color:transparent;-webkit-overflow-scrolling:touch;-ms-overflow-style:-ms-autohiding-scrollbar;box-sizing:border-box;z-index:99995}.fancybox-show-thumbs .fancybox-thumbs{display:block}.fancybox-show-thumbs .fancybox-inner{right:212px}.fancybox-thumbs>ul{list-style:none;position:absolute;position:relative;width:100%;height:100%;margin:0;padding:0;overflow-x:hidden;overflow-y:auto;font-size:0;white-space:nowrap}.fancybox-thumbs>ul>li{float:left;overflow:hidden;padding:0;margin:2px;width:100px;height:75px;max-width:calc(50% - 4px);max-height:calc(100% - 8px);position:relative;cursor:pointer;outline:none;-webkit-tap-highlight-color:transparent;-webkit-backface-visibility:hidden;backface-visibility:hidden;box-sizing:border-box}li.fancybox-thumbs-loading{background:rgba(0,0,0,.1)}.fancybox-thumbs>ul>li>img{position:absolute;top:0;left:0;max-width:none;max-height:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}.fancybox-thumbs>ul>li:before{content:"";position:absolute;top:0;right:0;bottom:0;left:0;border:4px solid #4ea7f9;z-index:99991;opacity:0;transition:all .2s cubic-bezier(.25,.46,.45,.94)}.fancybox-thumbs>ul>li.fancybox-thumbs-active:before{opacity:1}@media (max-width:800px){.fancybox-thumbs{width:110px}.fancybox-show-thumbs .fancybox-inner{right:110px}.fancybox-thumbs>ul>li{max-width:calc(100% - 10px)}}
@charset "UTF-8";

/*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animated.infinite {
  animation-iteration-count: infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }

  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }

  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }

  90% {
    transform: translate3d(0,-4px,0);
  }
}

@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }

  25%, 75% {
    opacity: 0;
  }
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@keyframes pulse {
  0% {
    transform: scale3d(1, 1, 1);
  }

  50% {
    transform: scale3d(1.05, 1.05, 1.05);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes rubberBand {
  0% {
    transform: scale3d(1, 1, 1);
  }

  30% {
    transform: scale3d(1.25, 0.75, 1);
  }

  40% {
    transform: scale3d(0.75, 1.25, 1);
  }

  50% {
    transform: scale3d(1.15, 0.85, 1);
  }

  65% {
    transform: scale3d(.95, 1.05, 1);
  }

  75% {
    transform: scale3d(1.05, .95, 1);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }

  10%, 30%, 50%, 70%, 90% {
    transform: translate3d(-10px, 0, 0);
  }

  20%, 40%, 60%, 80% {
    transform: translate3d(10px, 0, 0);
  }
}

@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }

  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }

  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }

  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }

  100% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

.swing {
  transform-origin: top center;
  animation-name: swing;
}

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1);
  }

  10%, 20% {
    transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }

  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@keyframes wobble {
  0% {
    transform: none;
  }

  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }

  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }

  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }

  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }

  100% {
    transform: none;
  }
}

@keyframes jello {
  11.1% {
    transform: none
  }

  22.2% {
    transform: skewX(-12.5deg) skewY(-12.5deg)
  }
  33.3% {
    transform: skewX(6.25deg) skewY(6.25deg)
  }
  44.4% {
    transform: skewX(-3.125deg) skewY(-3.125deg)
  }
  55.5% {
    transform: skewX(1.5625deg) skewY(1.5625deg)
  }
  66.6% {
    transform: skewX(-0.78125deg) skewY(-0.78125deg)
  }
  77.7% {
    transform: skewX(0.390625deg) skewY(0.390625deg)
  }
  88.8% {
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg)
  }
  100% {
    transform: none
  }
}

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }

  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    transform: scale3d(.9, .9, .9);
  }

  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    transform: scale3d(.97, .97, .97);
  }

  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes bounceInDown {
  0%, 60%, 75%, 90%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0);
  }

  60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0);
  }

  75% {
    transform: translate3d(0, -10px, 0);
  }

  90% {
    transform: translate3d(0, 5px, 0);
  }

  100% {
    transform: none;
  }
}

@keyframes bounceInLeft {
  0%, 60%, 75%, 90%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0);
  }

  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0);
  }

  75% {
    transform: translate3d(-10px, 0, 0);
  }

  90% {
    transform: translate3d(5px, 0, 0);
  }

  100% {
    transform: none;
  }
}

@keyframes bounceInRight {
  0%, 60%, 75%, 90%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    transform: translate3d(3000px, 0, 0);
  }

  60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0);
  }

  75% {
    transform: translate3d(10px, 0, 0);
  }

  90% {
    transform: translate3d(-5px, 0, 0);
  }

  100% {
    transform: none;
  }
}

@keyframes bounceInUp {
  0%, 60%, 75%, 90%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    transform: translate3d(0, 3000px, 0);
  }

  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }

  75% {
    transform: translate3d(0, 10px, 0);
  }

  90% {
    transform: translate3d(0, -5px, 0);
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes bounceOut {
  20% {
    transform: scale3d(.9, .9, .9);
  }

  50%, 55% {
    opacity: 1;
    transform: scale3d(1.1, 1.1, 1.1);
  }

  100% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }
}

@keyframes bounceOutDown {
  20% {
    transform: translate3d(0, 10px, 0);
  }

  40%, 45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }

  100% {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}

@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0);
  }

  100% {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
  }
}

@keyframes bounceOutRight {
  20% {
    opacity: 1;
    transform: translate3d(-20px, 0, 0);
  }

  100% {
    opacity: 0;
    transform: translate3d(2000px, 0, 0);
  }
}

@keyframes bounceOutUp {
  20% {
    transform: translate3d(0, -10px, 0);
  }

  40%, 45% {
    opacity: 1;
    transform: translate3d(0, 20px, 0);
  }

  100% {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDownBig {
  0% {
    opacity: 0;
    transform: translateY(-2000px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeftBig {
  0% {
    opacity: 0;
    transform: translateX(-2000px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRightBig {
  0% {
    opacity: 0;
    transform: translateX(2000px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeInUpBig {
  0% {
    opacity: 0;
    transform: translateY(2000px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(20px);
  }
}

@keyframes fadeOutDownBig {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(2000px);
  }
}

@keyframes fadeOutLeft {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-20px);
  }
}

@keyframes fadeOutLeftBig {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-2000px);
  }
}

@keyframes fadeOutRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(20px);
  }
}

@keyframes fadeOutRightBig {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(2000px);
  }
}

@keyframes fadeOutUp {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes fadeOutUpBig {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-2000px);
  }
}

@keyframes flip {
  0% {
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    animation-timing-function: ease-out;
  }

  40% {
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    animation-timing-function: ease-out;
  }

  50% {
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    animation-timing-function: ease-in;
  }

  80% {
    transform: perspective(400px) scale3d(.95, .95, .95);
    animation-timing-function: ease-in;
  }

  100% {
    transform: perspective(400px);
    animation-timing-function: ease-in;
  }
}

.animated.flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  animation-name: flip;
}

@keyframes flipInX {
  0% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
  }

  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }

  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }

  100% {
    transform: perspective(400px);
  }
}

@keyframes flipInY {
  0% {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    animation-timing-function: ease-in;
  }

  60% {
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }

  80% {
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }

  100% {
    transform: perspective(400px);
  }
}

@keyframes flipOutX {
  0% {
    transform: perspective(400px);
  }

  30% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }

  100% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}

@keyframes flipOutY {
  0% {
    transform: perspective(400px);
  }

  30% {
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }

  100% {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}

@keyframes lightSpeedIn {
  0% {
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }

  60% {
    transform: skewX(20deg);
    opacity: 1;
  }

  80% {
    transform: skewX(-5deg);
    opacity: 1;
  }

  100% {
    transform: none;
    opacity: 1;
  }
}

@keyframes lightSpeedOut {
  0% {
    opacity: 1;
  }

  100% {
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}

@keyframes rotateIn {
  0% {
    transform-origin: center;
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }

  100% {
    transform-origin: center;
    transform: none;
    opacity: 1;
  }
}

@keyframes rotateInDownLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }

  100% {
    transform-origin: left bottom;
    transform: none;
    opacity: 1;
  }
}

@keyframes rotateInDownRight {
  0% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  100% {
    transform-origin: right bottom;
    transform: none;
    opacity: 1;
  }
}

@keyframes rotateInUpLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  100% {
    transform-origin: left bottom;
    transform: none;
    opacity: 1;
  }
}

@keyframes rotateInUpRight {
  0% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }

  100% {
    transform-origin: right bottom;
    transform: none;
    opacity: 1;
  }
}

@keyframes rotateOut {
  0% {
    transform-origin: center;
    opacity: 1;
  }

  100% {
    transform-origin: center;
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}

@keyframes rotateOutDownLeft {
  0% {
    transform-origin: left bottom;
    opacity: 1;
  }

  100% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}

@keyframes rotateOutDownRight {
  0% {
    transform-origin: right bottom;
    opacity: 1;
  }

  100% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}

@keyframes rotateOutUpLeft {
  0% {
    transform-origin: left bottom;
    opacity: 1;
  }

  100% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}

@keyframes rotateOutUpRight {
  0% {
    transform-origin: right bottom;
    opacity: 1;
  }

  100% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}

@keyframes hinge {
  0% {
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }

  20%, 60% {
    transform: rotate3d(0, 0, 1, 80deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }

  40%, 80% {
    transform: rotate3d(0, 0, 1, 60deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }

  100% {
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@keyframes rollIn {
  0% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }

  100% {
    opacity: 1;
    transform: none;
  }
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@keyframes rollOut {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}

@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }

  50% {
    opacity: 1;
  }
}

.zoomIn {
  animation-name: zoomIn;
}

@keyframes zoomInStable {
 0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }

  33.333% {
    opacity: 1;
 transform: scale3d(1.1, 1.1, 1.1);
  }
  
  66.666666% {
    opacity: 1;
 transform: scale3d(1, 1, 1);
  }
}

@keyframes zoomInDown {
  0% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
  }

  60% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
  }
}

@keyframes zoomInLeft {
  0% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
  }

  60% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
  }
}

@keyframes zoomInRight {
  0% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
  }

  60% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
  }
}

@keyframes zoomInUp {
  0% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
    animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
  }

  60% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
  }
}

@keyframes zoomOut {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }

  100% {
    opacity: 0;
  }
}

@keyframes zoomOutDown {
  40% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
  }

  100% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
    transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
  }
}

@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
  }

  100% {
    opacity: 0;
    transform: scale(.1) translate3d(-2000px, 0, 0);
    transform-origin: left center;
  }
}

@keyframes zoomOutRight {
  40% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
  }

  100% {
    opacity: 0;
    transform: scale(.1) translate3d(2000px, 0, 0);
    transform-origin: right center;
  }
}

@keyframes zoomOutUp {
  40% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
  }

  100% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
    transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
  }
}

@keyframes slideInDown {
  0% {
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  0% {
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

.slideInLeft {
  animation-name: slideInLeft;
}

@keyframes slideInRight {
  0% {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

.slideInRight {
  animation-name: slideInRight;
}

@keyframes slideInUp {
  0% {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideOutDown {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    visibility: hidden;
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes slideOutLeft {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes slideOutRight {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes slideOutUp {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    visibility: hidden;
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes anime {
from {
	opacity: 0;
	transform: scaleY(0);
	-webkit-transform: scaleY(0);
	-moz-transform: scaleY(0);
	-ms-transform: scaleY(0);
	-o-transform: scaleY(0);
}
to {
	opacity: 1;
	transform: scaleY(1);
	-webkit-transform: scaleY(1);
	-ms-transform: scaleY(1);
	-o-transform: scaleY(1);
	-moz-transform: scaleY(1);
}
}


@keyframes float-bob-y {
  0% {
    transform: translateY(-20px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(-20px);
  }
}

@keyframes float-bob-x {
  0% {
    transform: translateX(-100px);
  }

  50% {
    transform: translateX(-10px);
  }

  100% {
    transform: translateX(-100px);
  }
}




@keyframes float-bob {
  0% {
    transform: translateX(-430px);
  }

  50% {
    transform: translateX(-10px);
  }

  100% {
    transform: translateX(-430px);
  }
}

@keyframes zoom-fade {
  0% {
    transform: scale(0.9);
  }

  50% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.9);
  }
}

@keyframes zoom-fade-two {
  0% {
    transform: scale(1.5);
  }

  50% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.5);
  }
}



@keyframes rotateme {
    from {
        transform: rotate(0deg);
    }
    to { 
        transform: rotate(360deg);
    }
}


.rotate-me {
    animation-name: rotateme; 
    animation-duration: 24s; 
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  
  -webkit-animation-name: rotateme; 
    -webkit-animation-duration: 24s; 
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
  
  -moz-animation-name: rotateme; 
    -moz-animation-duration: 24s; 
    -moz-animation-iteration-count: infinite;
    -moz-animation-timing-function: linear;
  
  -ms-animation-name: rotateme; 
    -ms-animation-duration: 24s; 
    -ms-animation-iteration-count: infinite;
    -ms-animation-timing-function: linear;
  
  -o-animation-name: rotateme; 
    -o-animation-duration: 24s; 
    -o-animation-iteration-count: infinite;
    -o-animation-timing-function: linear;
}

@keyframes footer-animate {
  0% {
    transform: translateX(-1000px);
  }

  50% {
    transform: translateX(-10px);
  }

  100% {
    transform: translateX(-1000px);
  }
}

/** squares **/
@keyframes squares{0%{transform:scale(1);opacity:0}20%{transform:scale(1.24);opacity:1}100%{transform:scale(2.1);opacity:0}} 



/** service-hexagon **/@keyframes service_hexagon_2{0%{transform:rotateY(0deg)}100%{transform:rotateY(360deg)}}@keyframes service_hexagon{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
/* template-color */

.scroll-top {
    border: 2px dashed #85C5C2;
    color: #85C5C2;
}

.theme-btn-one {
    background: #85C5C2;
}

.main-menu .navigation > li.current > a,
.main-menu .navigation > li:hover > a {
    color: #85C5C2;
}


.main-header .search-btn button:hover {
    color: #22b6af;
}

.main-header .social-links li a:hover {
    color: #22b6af;
}

.main-menu .navigation > li > ul {
    border-top: 3px solid #85C5C2;
}

.main-menu .navigation > li > ul > li > ul {
    border-top: 3px solid #22b6af;
}

.main-menu .navigation > li > ul > li > a:hover {
    color: #22b6af;
}

.main-menu .navigation > li > ul > li > ul > li > a:hover {
    color: #22b6af;
}

.sec-title h6 {
    color: #85C5C2;
}

.theme-btn-two:hover {
    background: #85C5C2;
}

.service-block-one .inner-box h4 a:hover {
    color: #22b6af;
}

.service-block-one .inner-box:before {
    background: #ffffff;
}

.service-block-one .inner-box .theme-btn-two:hover {
    color: #7393B3 !important;
}

.social-style-one li a:hover {
    background: #22b6af;
}

.team-block-one .inner-box .lower-content h4 a:hover {
    color: #22b6af;
}

.content_block_3 .content-box .author-box .designation {
    color: #22b6af;
}

.testimonial-section .owl-nav .owl-prev:hover,
.testimonial-section .owl-nav .owl-next:hover {
    color: #22b6af;
}

.news-block-one .inner-box .lower-content .post-date h3 {
    color: #85C5C2;
}

.news-block-one .inner-box .lower-content h4 a:hover {
    color: #22b6af;
}

.news-block-one .inner-box .lower-content .post-info a:hover {
    color: #22b6af;
}

.news-section .owl-nav .owl-prev:hover,
.news-section .owl-nav .owl-next:hover {
    color: #22b6af;
}

.footer-top .logo-widget .text .info li a:hover {
    color: #22b6af;
}

.footer-top .logo-widget .text .info li i {
    color: #85C5C2;
}

.footer-top .links-widget .links-list li a:hover {
    color: #22b6af;
}

.footer-top .links-widget .links-list li a:before {
    background: #22b6af;
}

.footer-bottom a:hover {
    color: #22b6af;
}

h4 a:hover {
    color: #22b6af;
}

.main-header.style-four .menu-right-content li.search-btn button:hover {
    color: #22b6af;
}

.banner-section.style-four .image-box .image:before {
    background: #22b6af;
}

.project-block-one .inner-box .content-box .view-btn a {
    color: #22b6af;
}

.project-block-one .inner-box .content-box .view-btn a:hover {
    background: #22b6af;
    border-color: #22b6af;
}

.default-form .form-group input:focus,
.default-form .form-group textarea:focus {
    border-color: #22b6af !important;
}

.events-sidebar .speaker-info .info-list li i {
    color: #22b6af;
}

.pagination li a:hover,
.pagination li a.current {
    background: #22b6af;
    border-color: #22b6af;
}

.blog-sidebar .category-widget .category-list li a:hover {
    color: #85C5C2;
}

.blog-sidebar .category-widget .category-list li a:before {
    color: #85C5C2;
}

.blog-sidebar .sidebar-widget .tags-list li a:hover {
    background: #22b6af;
    border-color: #22b6af;
}

.news-block-three .inner-box .lower-content .inner h3 a:hover {
    color: #22b6af;
}

.news-block-three .inner-box .lower-content .post-date h3 {
    color: #85C5C2;
}

.news-block-three .inner-box .lower-content .inner .post-info a:hover {
    color: #22b6af;
}

.blog-details-content .author-box .inner .social-links li a:hover {
    background: #22b6af;
    border-color: #22b6af;
}

.menu-area .mobile-nav-toggler {
    background-color: #85C5C2;
}

/* Css For Laborex */

/************ TABLE OF CONTENTS ***************
1. Fonts
2. Reset
3. Global
4. Main Header/style-one/style-two
5. Main Slider/style-one/style-two
6. Intro Section
7. Welcome Section
9. Cta Section
8. Research Fields
10. Testimonial Section
11. Researches Section
12. Team Section
14. Video
15. Fact Counter
16. News Section
19. Clients Section
20. Main Footer
21. Footer Bottom
22. Research Style Two
23. Innovative Solution
24. Discover Section
25. Testimonial Section
26. Chooseus Section
27. News Style Two
28. Page Title
29. Research Page Section
30. Research Details
31. Professor Page Section
32. Professor Details
33. About Section
34. Error Section
35. Blog Page Section
36. Blog Details
37. Blog Sidebar
38. Contact Section
39. Google Map


**********************************************/


/***

====================================================================
  Reset
====================================================================

 ***/
* {
    margin: 0px;
    padding: 0px;
    border: none;
    outline: none;
}


/***

====================================================================
  Global Settings
====================================================================

 ***/


body {
    font-size: 16px;
    color: #848484;
    line-height: 26px;
    font-weight: 400;
    background: #ffffff;
    font-family: 'Open Sans', sans-serif;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center top;
    -webkit-font-smoothing: antialiased;
}

@media (min-width: 1200px) {
    .container {
        max-width: 1170px;
        padding: 0px 15px;
    }
}

.container-fluid {
    padding: 0px;
}

.auto-container {
    position: static;
    max-width: 1200px;
    padding: 0px 15px;
    margin: 0 auto;
}

.boxed_wrapper {
    position: relative;
    margin: 0 auto;
    overflow: hidden !important;
    width: 100%;
    min-width: 300px;
}


a {
    font-family: 'Barlow', sans-serif;
    text-decoration: none;
    transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

a:hover {
    text-decoration: none;
    outline: none;
}

input, button, select, textarea {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    font-size: 15px;
    background: transparent;
}

ul, li {
    list-style: none;
    padding: 0px;
    margin: 0px;
}

input {
    transition: all 500ms ease;
}

button:focus,
input:focus,
textarea:focus {
    outline: none;
    box-shadow: none;
    transition: all 500ms ease;
}

p {
    position: relative;
    font-family: 'Open Sans', sans-serif;
    color: #848484;
    font-weight: 400;
    margin: 0px;
    transition: all 500ms ease;
}

h1, h2, h3, h4, h5, h6 {
    position: relative;
    font-family: 'Barlow', sans-serif;
    font-weight: 400;
    color: #142441;
    margin: 0px;
    transition: all 500ms ease;
}

/* Preloader */

/* AnimaciÃ³n del preloader */
@keyframes spinner {
    to {
        transform: rotateZ(360deg);
    }
}

@keyframes letters-loading {
    0%,
    75%,
    100% {
        opacity: 0;
        transform: rotateY(-90deg);
    }

    25%,
    50% {
        opacity: 1;
        transform: rotateY(0deg);
    }
}


.centred {
    text-align: center;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}


figure {
    margin: 0px;
}

img {
    display: inline-block;
    max-width: 100%;
    height: auto;
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .7s;
    transition-property: all;
}

/** button **/

.theme-btn-one {
    position: relative;
    display: inline-block;
    overflow: hidden;
    font-size: 16px;
    line-height: 25px;
    font-weight: 700;
    color: #fff !important;
    text-align: center;
    padding: 14px 36px 15px 36px;
    border-radius: 30px;
    z-index: 1;
    transition: all 500ms ease;
}

.theme-btn-one:before {
    webkit-transition-duration: 800ms;
    transition-duration: 800ms;
    position: absolute;
    width: 200%;
    height: 200%;
    content: "";
    bottom: 110%;
    left: 50%;
    background: #111111;
    transform: translateX(-50%);
    border-radius: 50%;
    z-index: -1;
}

.theme-btn-one:hover:before {
    bottom: -40%;
}

.theme-btn-two {
    position: relative;
    display: inline-block;
    font-size: 14px;
    line-height: 25px;
    font-family: 'Open Sans', sans-serif;
    font-weight: 700;
    color: #142441 !important;
    background: #f2f3f5;
    text-align: center;
    border-radius: 30px;
    padding: 7.5px 22px;
    z-index: 1;
    transition: all 500ms ease;
}

.theme-btn-two:hover {
    color: #fff !important;
}


.pagination {
    position: relative;
    display: block;
}

.pagination li {
    position: relative;
    display: inline-block;
    margin-right: 7px;
}

.pagination li:last-child {
    margin: 0px !important;
}

.pagination li a {
    position: relative;
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    font-family: 'Barlow', sans-serif;
    height: 60px;
    width: 60px;
    line-height: 60px;
    background: #ffffff;
    text-align: center;
    border: 1px solid #e5e5e5;
    color: #2d2929;
    border-radius: 50%;
    z-index: 1;
    transition: all 500ms ease;
}

.pagination li a:hover,
.pagination li a.current {
    color: #fff;
}

.sec-pad {
    padding: 143px 0px 150px 0px;
}

.scroll-top {
    width: 55px;
    height: 55px;
    line-height: 55px;
    position: fixed;
    bottom: 105%;
    right: 30px;
    font-size: 40px;
    z-index: 99;
    border-radius: 50%;
    text-align: center;
    cursor: pointer;
    transition: 1s ease;
}

.scroll-top.open {
    bottom: 50px;
}

.scroll-top:hover {
    border-color: #06194b;
    color: #06194b;
}

.sec-title {
    position: relative;
    display: block;
    margin-bottom: 60px;
}

.sec-title h6 {
    display: inline-block;
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
    padding-left: 50px;
    text-transform: uppercase;
    margin-bottom: 13px;
}

.sec-title.centred h6 {
    padding-right: 50px;
}

.sec-title h6:before {
    position: absolute;
    content: '';
    background: url(/assets/images/icons/decor-2.png);
    width: 37px;
    height: 7px;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
}

.sec-title.centred h6:before {
    background: url(/assets/images/icons/decor-1.png);
}

.sec-title.centred h6:after {
    position: absolute;
    content: '';
    background: url(/assets/images/icons/decor-2.png);
    width: 37px;
    height: 7px;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
}

.sec-title h2 {
    display: block;
    font-size: 42px;
    line-height: 48px;
    font-weight: 700;
    margin: 0px;
}


/***

====================================================================
                        Home-Page-One
====================================================================

***/


/** main-header **/

.main-header {
    position: relative;
    left: 0px;
    top: 0px;
    z-index: 999;
    width: 100%;
    transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.sticky-header {
    position: fixed;
    opacity: 0;
    visibility: hidden;
    left: 0px;
    top: 0px;
    width: 100%;
    z-index: 0;
    background: #ffffff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.fixed-header .sticky-header {
    z-index: 999;
    opacity: 1;
    visibility: visible;
    -op-animation-name: fadeInDown;
    animation-name: fadeInDown;
    -op-animation-duration: 500ms;
    animation-duration: 500ms;
    -op-animation-timing-function: linear;
    animation-timing-function: linear;
    -op-animation-iteration-count: 1;
    animation-iteration-count: 1;
}

/** header-upper **/

.main-header .logo-box {
    position: relative;
    padding: 29px 145px 34px 0px;
}

.main-header .logo-box .logo {
    position: relative;
    max-width: 240px;
    width: 100%;
}

.main-header .logo-box .logo img {
    width: 100%;
}

.main-header .search-btn button {
    position: relative;
    display: inline-block;
    font-size: 20px;
    color: #b0b3b9;
    font-weight: 400;
}

.main-header .search-btn button:hover {

}

.main-header .social-links li {
    position: relative;
    display: inline-block;
    margin-right: 25px;
}

.main-header .social-links li:last-child {
    margin: 0px !important;
}

.main-header .social-links li a {
    position: relative;
    display: inline-block;
    font-size: 16px;
    color: #b0b3b9;
}

.main-header .social-links li a:hover {

}


/** main-menu **/

.main-menu {
    float: left;
}

.main-menu .navbar-collapse {
    padding: 0px;
    display: block !important;
}

.main-menu .navigation {
    margin: 0px;
}

.main-menu .navigation > li {
    position: inherit;
    float: left;
    z-index: 2;
    padding: 18px 0px;
    margin: 0px 15px;
    transition: all 300ms ease;
}

.main-menu .navigation > li:last-child {
    margin-right: 0px !important;
}

.main-menu .navigation > li:first-child {
    margin-left: 0px !important;
}

.main-menu .navigation > li > a {
    position: relative;
    display: block;
    text-align: center;
    font-size: 16px;
    line-height: 30px;
    font-family: 'Open Sans', sans-serif;
    font-weight: 700;
    padding-right: 12px;
    opacity: 1;
    color: #142441;
    z-index: 1;
    transition: all 500ms ease;
}

.main-menu .navigation > li.current > a,
.main-menu .navigation > li:hover > a {

}


.main-menu .navigation > li.dropdown > a:before {
    position: absolute;
    content: "\f107";
    font-family: 'Font Awesome 5 Pro';
    top: 2px;
    right: 0px;
}

.main-menu .navigation > li > ul {
    position: absolute;
    left: inherit;
    top: 100%;
    width: 230px;
    z-index: 100;
    display: none;
    opacity: 0;
    visibility: hidden;
    padding: 10px 0px;
    padding-bottom: 15px;
    filter: alpha(opacity=0);
    background: #fff;
    border-radius: 10px;
    transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
    -ms-transition: all 0.2s ease-out;
    box-shadow: 0px 4px 4px 1px rgba(0, 0, 0, 0.2);
    transform: rotateX(-90deg);
    transform-origin: 0 0;
}

.main-menu .navigation > li > ul > li {
    position: relative;
    width: 100%;
    padding: 0px 30px;
    transition: all 0.2s cubic-bezier(0.4, 0.28, 0.31, 1.28) 0s;
    opacity: 0;
    visibility: hidden;
    transform: translateY(5px);
}

.main-menu .navigation > li:hover > ul > li {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.main-menu .navigation > li > ul > li:nth-child(2n+1) {
    transition-delay: 0.1s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+2) {
    transition-delay: 0.15s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+3) {
    transition-delay: 0.2s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+4) {
    transition-delay: 0.25s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+5) {
    transition-delay: 0.3s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+6) {
    transition-delay: 0.35s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+7) {
    transition-delay: 0.4s;
}

.main-menu .navigation > li > ul > li:nth-child(2n+8) {
    transition-delay: 0.45s;
}

.main-menu .navigation > li > ul > li:last-child {
    border-bottom: none;
}

.main-menu .navigation > li > ul > li > a {
    position: relative;
    display: block;
    padding: 7px 0px;
    line-height: 24px;
    font-weight: 600;
    font-size: 15px;
    font-family: 'Open Sans', sans-serif;
    text-transform: capitalize;
    color: #142441;
    text-align: left;
    transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.main-menu .navigation > li > ul > li > a:before {
    position: absolute;
    content: "\f105";
    font-family: 'Font Awesome 5 Pro';
    left: 0px;
    top: 8px;
    opacity: 0;
    transition: all 500ms ease;
}

.main-menu .navigation > li > ul > li > a:hover:before {
    opacity: 1;
}

.main-menu .navigation > li > ul > li > a:hover {
    padding-left: 15px;
}

.main-menu .navigation > li > ul > li:last-child > a {
    border-bottom: none;
}

.main-menu .navigation > li > ul > li.dropdown > a:after {
    font-family: 'Font Awesome 5 Pro';
    content: "\f105";
    position: absolute;
    right: 20px;
    top: 7px;
    display: block;
    line-height: 24px;
    font-size: 16px;
    font-weight: 800;
    text-align: center;
    z-index: 5;
}

.main-menu .navigation > li > ul > li > ul {
    position: absolute;
    right: 100%;
    top: 0%;
    width: 230px;
    z-index: 100;
    display: none;
    padding: 10px 0px;
    padding-bottom: 15px;
    filter: alpha(opacity=0);
    background: #fff;
    border-radius: 10px;
    transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
    -ms-transition: all 0.2s ease-out;
    box-shadow: 0px 4px 4px 1px rgba(0, 0, 0, 0.2);
    transform: rotateX(-90deg);
    transform-origin: 0 0;
}

.main-menu .navigation > li > ul > li > ul > li {
    position: relative;
    width: 100%;
    padding: 0px 30px;
}

.main-menu .navigation > li > ul > li > ul > li {
    position: relative;
    width: 100%;
    padding: 0px 30px;
    transition: all 0.2s cubic-bezier(0.4, 0.28, 0.31, 1.28) 0s;
    opacity: 0;
    visibility: hidden;
    transform: translateY(5px);
}

.main-menu .navigation > li > ul > li:hover > ul > li {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.main-menu .navigation > li > ul > li > ul > li:nth-child(2n+1) {
    transition-delay: 0.1s;
}

.main-menu .navigation > li > ul > li > ul > li:nth-child(2n+2) {
    transition-delay: 0.15s;
}

.main-menu .navigation > li > ul > li > ul > li:nth-child(2n+3) {
    transition-delay: 0.2s;
}

.main-menu .navigation > li > ul > li > ul > li:nth-child(2n+4) {
    transition-delay: 0.25s;
}

.main-menu .navigation > li > ul > li > ul > li:last-child {
    border-bottom: none;
}

.main-menu .navigation > li > ul > li > ul > li:last-child {
    border-bottom: none;
}

.main-menu .navigation > li > ul > li > ul > li > a {
    position: relative;
    display: block;
    padding: 7px 0px;
    line-height: 24px;
    font-weight: 600;
    font-size: 15px;
    font-family: 'Open Sans', sans-serif;
    text-transform: capitalize;
    color: #142441;
    text-align: left;
    transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.main-menu .navigation > li > ul > li > ul > li > a:before {
    position: absolute;
    content: "\f105";
    font-family: 'Font Awesome 5 Pro';
    left: 0px;
    top: 8px;
    opacity: 0;
    transition: all 500ms ease;
}

.main-menu .navigation > li > ul > li > ul > li > a:hover:before {
    opacity: 1;
}

.main-menu .navigation > li > ul > li > ul > li:last-child > a {
    border-bottom: none;
}

.main-menu .navigation > li > ul > li > ul > li > a:hover {
    padding-left: 15px;
}

.main-menu .navigation > li > ul > li > ul > li.dropdown > a:after {
    font-family: 'Font Awesome 5 Pro';
    content: "\f105";
    position: absolute;
    right: 20px;
    top: 12px;
    display: block;
    line-height: 24px;
    font-size: 16px;
    font-weight: 900;
    z-index: 5;
}

.main-menu .navigation > li.dropdown:hover > ul {
    visibility: visible;
    opacity: 1;
    filter: alpha(opacity=100);
    top: 100%;
    transform: rotateX(0);
}

.main-menu .navigation li > ul > li.dropdown:hover > ul {
    visibility: visible;
    opacity: 1;
    filter: alpha(opacity=100);
    top: 0%;
    transform: rotateX(0);
}

.main-menu .navigation li.dropdown .dropdown-btn {
    position: absolute;
    right: -32px;
    top: 66px;
    width: 34px;
    height: 30px;
    text-align: center;
    font-size: 18px;
    line-height: 26px;
    color: #3b3b3b;
    cursor: pointer;
    display: none;
    z-index: 5;
    transition: all 500ms ease;
}

.main-menu .navigation li.current.dropdown .dropdown-btn,
.main-menu .navigation li:hover .dropdown-btn {

}

.main-menu .navigation li.dropdown ul li.dropdown .dropdown-btn {
    display: none;
}

.menu-area .mobile-nav-toggler {
    position: relative;
    float: right;
    font-size: 40px;
    line-height: 50px;
    cursor: pointer;
    color: #3786ff;
    display: none;
}

.menu-area .mobile-nav-toggler .icon-bar {
    position: relative;
    height: 2px;
    width: 30px;
    display: block;
    margin-bottom: 5px;
    background-color: #fff !important;
    transition: all 300ms ease;
}

.menu-area .mobile-nav-toggler .icon-bar:last-child {
    margin-bottom: 0px;
}


/** megamenu-style **/


/** mobile-menu **/

.mobile-menu {
    position: fixed;
    right: 0;
    top: 0;
    width: 300px;
    padding-right: 30px;
    max-width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    z-index: 999999;
    transition: all 900ms ease;
}

.mobile-menu .navbar-collapse {
    display: block !important;
}

.mobile-menu .nav-logo {
    position: relative;
    padding: 10px 25px;
    text-align: left;
    padding-bottom: 30px;
}

.mobile-menu-visible {
    overflow: hidden;
}

.mobile-menu-visible .mobile-menu {
    opacity: 1;
    visibility: visible;
}

.mobile-menu .menu-backdrop {
    position: fixed;
    left: 0%;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: all 900ms ease;
    background-color: #000;
}

.mobile-menu-visible .mobile-menu .menu-backdrop {
    opacity: 0.70;
    visibility: visible;
    right: 100%;
    -webkit-transition: all .8s ease-out 0s;
    -o-transition: all .8s ease-out 0s
}

.mobile-menu .menu-box {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
    background: #7393B3;
    padding: 0px 0px;
    z-index: 5;
    opacity: 0;
    visibility: hidden;
    border-radius: 0px;
    transform: translateX(100%);
    transition: all 900ms ease !important;
}

.mobile-menu-visible .mobile-menu .menu-box {
    opacity: 1;
    visibility: visible;
    transition: all 0.7s ease;
    transform: translateX(0%);
}

.mobile-menu .close-btn {
    position: absolute;
    right: 25px;
    top: 10px;
    line-height: 30px;
    width: 24px;
    text-align: center;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    z-index: 10;
    transition: all 0.9s ease;
}

.mobile-menu-visible .mobile-menu .close-btn {
    transform: rotate(360deg);
}

.mobile-menu .close-btn:hover {
    transform: rotate(90deg);
}

.mobile-menu .navigation {
    position: relative;
    display: block;
    width: 100%;
    float: none;
}

.mobile-menu .navigation li {
    position: relative;
    display: block;
    border-top: 1px solid rgba(255, 255, 255, 0.10);
}

.mobile-menu .navigation:last-child {
    border-bottom: 1px solid rgba(255, 255, 255, 0.10);
}

.mobile-menu .navigation li > ul > li:first-child {
    border-top: 1px solid rgba(255, 255, 255, 0.10);
}

.mobile-menu .navigation li > a {
    position: relative;
    display: block;
    line-height: 24px;
    padding: 10px 25px;
    font-size: 15px;
    font-weight: 500;
    color: #ffffff;
    text-transform: uppercase;
    transition: all 500ms ease;
}

.mobile-menu .navigation li ul li > a {
    font-size: 16px;
    margin-left: 20px;
    text-transform: capitalize;
}

.mobile-menu .navigation li > a:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 0;
    border-left: 5px solid #fff;
    transition: all 500ms ease;
}

.mobile-menu .navigation li.current > a:before {
    height: 100%;
}


.mobile-menu .navigation li.dropdown .dropdown-btn {
    position: absolute;
    right: 6px;
    top: 6px;
    width: 32px;
    height: 32px;
    text-align: center;
    font-size: 16px;
    line-height: 32px;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.10);
    cursor: pointer;
    border-radius: 2px;
    transition: all 500ms ease;
    z-index: 5;
}

.mobile-menu .navigation li.dropdown .dropdown-btn.open {
    color: #ffffff;
    transform: rotate(90deg);
}

.mobile-menu .navigation li > ul,
.mobile-menu .navigation li > ul > li > ul {
    display: none;
}

.mobile-menu .social-links {
    position: relative;
    padding: 0px 25px;
}

.mobile-menu .social-links li {
    position: relative;
    display: inline-block;
    margin: 0px 10px 10px;
}

.mobile-menu .social-links li a {
    position: relative;
    line-height: 32px;
    font-size: 16px;
    color: #ffffff;
    transition: all 500ms ease;
}

.mobile-menu .social-links li a:hover {

}

.mobile-menu .contact-info {
    position: relative;
    padding: 20px 30px 20px 30px;
}

.mobile-menu .contact-info h4 {
    position: relative;
    font-size: 20px;
    color: #ffffff;
    font-weight: 700;
    margin-bottom: 20px;
}

.mobile-menu .contact-info ul li {
    position: relative;
    display: block;
    font-size: 15px;
    color: rgba(255, 255, 255, 0.80);
    margin-bottom: 3px;
}

.mobile-menu .contact-info ul li a {
    color: rgba(255, 255, 255, 0.80);
}

.mobile-menu .contact-info ul li a:hover {

}

.mobile-menu .contact-info ul li:last-child {
    margin-bottom: 0px;
}

.main-header .outer-box {
    position: relative;
}

/** search-popup **/

.search-popup {
    position: fixed;
    left: 0;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 99999;
    visibility: hidden;
    opacity: 0;
    overflow: auto;
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(101%);
    transition: all 700ms ease;
    -moz-transition: all 700ms ease;
    -webkit-transition: all 700ms ease;
    -ms-transition: all 700ms ease;
    -o-transition: all 700ms ease;
}

.search-popup.popup-visible {
    transform: translateY(0%);
    visibility: visible;
    opacity: 1;
}

.search-popup h3 {
    text-transform: uppercase;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 20px;
    margin-top: 30px;
    letter-spacing: 1px;
    text-align: center;
}


/** banner-section **/

.banner-section {
    position: relative;
    width: 100%;
    padding: 110px 0px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.banner-carousel {
    position: relative;
}

.banner-carousel .slide-item {
    position: relative;
}

.owl-dots-none .owl-dots {
    display: none !important;
}

.banner-carousel .content-box h2 {
    display: block;
    font-size: 44px;
    line-height: 54px;
    font-weight: 700;
    margin-bottom: 21px;
}

.banner-carousel .content-box p {
    font-size: 18px;
    line-height: 30px;
    color: #222;
    margin-bottom: 32px;
}

.banner-carousel .content-box .theme-btn-one {
    margin-right: 16px;
    padding: 14px 45px 15px 45px;
}

.rotate-me {
    animation-name: rotateme;
    animation-duration: 30s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    -webkit-animation-name: rotateme;
    -webkit-animation-duration: 30s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    -moz-animation-name: rotateme;
    -moz-animation-duration: 30s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-timing-function: linear;
    -ms-animation-name: rotateme;
    -ms-animation-duration: 30s;
    -ms-animation-iteration-count: infinite;
    -ms-animation-timing-function: linear;
    -o-animation-name: rotateme;
    -o-animation-duration: 30s;
    -o-animation-iteration-count: infinite;
    -o-animation-timing-function: linear;
}

.bg-color-1 {
    background: #e9f8f8;
}


/** service-section **/

.service-section {
    position: relative;
    padding-top: 10px;
    background: #ffffff;
}

.service-section:before {
    position: absolute;
    content: '';
    background: #e9f8f8;
    width: 100%;
    height: calc(100% - 40px);
    left: 0px;
    top: 0px;
    -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 75%, 0% 100%, 0% 0%);
            clip-path: polygon(0% 0%, 100% 0%, 100% 75%, 0% 100%, 0% 0%);
}

.service-block-one .inner-box {
    position: relative;
    display: block;
    background: #7493b3;
    overflow: hidden;
    border-radius: 20px;
    padding: 50px 30px 50px 30px;
    box-shadow: 0 10px 30px #edf2f2;
}

.service-block-one .inner-box:before {
    position: absolute;
    content: '';
    width: 100%;
    height: 0%;
    left: 0px;
    bottom: 0px;
    transition: all 500ms ease;
}

.service-block-one .inner-box:hover:before {
    height: 100%;
    top: 0px;
}


.service-block-one .inner-box:hover h4 a,
.service-block-one .inner-box:hover p {
    color: #142441;
}

.service-block-one .inner-box h4 {
    display: block;
    font-size: 20px;
    line-height: 26px;
    font-weight: 700;
    margin-bottom: 19px;
}

.service-block-one .inner-box h4 a {
    display: inline-block;
    color: #ffffff;
}

.service-block-one .inner-box h4 a:hover {

}

.service-block-one .inner-box p {
    position: relative;
    margin-bottom: 4px;
    transition: all 500ms ease;
    color: #ffffff;
}

.service-block-one .inner-box .theme-btn-two {
    background: #f2f3f5;
}

.service-block-one .inner-box .theme-btn-two:hover {

}

.service-section .pattern-layer .pattern-1 {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 608px;
    height: 563px;
    background-repeat: no-repeat;
}

.service-section .pattern-layer .pattern-2 {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 513px;
    height: 529px;
    background-repeat: no-repeat;
}


/** about-section **/

.content_block_1 .content-box {
    position: relative;
    display: block;
}

.content_block_1 .content-box .bold-text p {
    font-size: 18px;
    line-height: 30px;
    color: #222;
}

.content_block_1 .content-box .bold-text {
    margin-bottom: 31px;
}

.content_block_1 .content-box .text p {
    line-height: 30px;
    margin-bottom: 26px;
}

.content_block_1 .content-box .sec-title {
    margin-bottom: 45px;
}


/** video-section **/

@keyframes ripple {
    70% {
        box-shadow: 0 0 0 40px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}


/** research-section **/


/** team-section **/

.team-section {
    position: relative;
    padding: 30px 0 60px;
}

.social-style-one li {
    position: relative;
    display: inline-block;
    float: left;
    margin-right: 15px;
}

.social-style-one li:last-child {
    margin: 0px !important;
}

.social-style-one li a {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    background: #f2f3f5;
    color: #142441;
    text-align: center;
    border-radius: 50%;
}

.social-style-one li a:hover {
    color: #ffffff;
}

.team-block-one .inner-box {
    position: relative;
    display: block;
}

.team-block-one .inner-box .image-box {
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 20px;
}

.team-block-one .inner-box .image-box img {
    width: 100%;
    border-radius: 20px;
}

.team-block-one .inner-box .image-box:before {
    position: absolute;
    content: '';
    background: #000;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    transform: scale(0, 0);
    opacity: 0.6;
    transition: all 500ms ease;
}

.team-block-one .inner-box:hover .image-box:before {
    transform: scale(1, 1);
}

.team-block-one .inner-box .image-box .social-style-one {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0, 0);
    width: 100%;
    text-align: center;
    transition: all 500ms ease;
}

.team-block-one .inner-box:hover .image-box .social-style-one {
    transform: translate(-50%, -50%) scale(1, 1);
}

.team-block-one .inner-box .image-box .social-style-one li {
    float: none;
    margin: 0px 6px;
}

.team-block-one .inner-box .lower-content {
    position: relative;
    display: block;
    padding-top: 22px;
}

.team-block-one .inner-box .lower-content h4 {
    display: block;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
    margin-bottom: 6px;
}

.team-block-one .inner-box .lower-content h4 a {
    display: inline-block;
    color: #142441;
}

.team-block-one .inner-box .lower-content h4 a:hover {

}

.team-block-one .inner-box .lower-content .designation {
    position: relative;
    display: block;
}


/** testimonial-section **/

.testimonial-section {
    position: relative;
    width: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
}

.testimonial-section:before {
    position: absolute;
    content: '';
    background: -webkit-linear-gradient(0deg, rgba(255, 255, 255, 1) 10%, rgba(255, 255, 255, 0.0) 100%);
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    right: 0px;
}

.testimonial-section .inner-content {
    position: relative;
    display: block;
    max-width: 670px;
    width: 100%;
    background: #ffffff;
    padding: 63px 70px 70px 70px;
    border-radius: 20px;
    box-shadow: 0 10px 30px #e2e8ea;
}

.testimonial-section .inner-content .sec-title {
    margin-bottom: 49px;
}

.content_block_3 .content-box {
    position: relative;
    display: block;
}

.content_block_3 .content-box .text p {
    font-size: 18px;
    line-height: 32px;
    color: #142441;
}

.content_block_3 .content-box .text {
    display: block;
    margin-bottom: 21px;
}

.content_block_3 .content-box .author-box {
    position: relative;
    padding: 10px 0px 5px 90px;
    min-height: 70px;
}

.content_block_3 .content-box .author-box .author-thumb {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 70px;
    height: 70px;
    border-radius: 50%;
}

.content_block_3 .content-box .author-box .author-thumb img {
    width: 100%;
    border-radius: 50%;
}

.content_block_3 .content-box .author-box h4 {
    display: block;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
    margin-bottom: 1px;
}

.content_block_3 .content-box .author-box .designation {
    position: relative;
    display: block;
    font-size: 15px;
}

.testimonial-section .owl-nav {
    position: absolute;
    right: 0px;
    bottom: 20px;
}

.testimonial-section .owl-nav .owl-prev,
.testimonial-section .owl-nav .owl-next {
    position: relative;
    display: inline-block;
    font-size: 20px;
    line-height: 30px;
    color: #bbbbbb;
    cursor: pointer;
    transition: all 500ms ease;
}

.testimonial-section .owl-nav .owl-prev {
    margin-right: 20px;
}

.testimonial-section .owl-nav .owl-prev:hover,
.testimonial-section .owl-nav .owl-next:hover {

}


/** funfact-section **/


/** events-section **/


/** news-section **/

.news-section {
    position: relative;
}

.news-section .owl-carousel .owl-stage-outer {
    overflow: visible;
}

.news-section .owl-carousel .owl-stage-outer .owl-item {
    opacity: 0;
}

.news-section .owl-carousel .owl-stage-outer .owl-item.active {
    opacity: 1;
}

.news-block-one .inner-box {
    position: relative;
    display: block;
    overflow: hidden;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px #edf2f2;
    transition: all 500ms ease;
}

.news-block-one .inner-box:hover {
    box-shadow: 0 10px 30px #e5edec;
}

.news-block-one .inner-box .image-box {
    position: relative;
    display: block;
    overflow: hidden;
    -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 84%, 0% 100%, 0% 0%);
            clip-path: polygon(0% 0%, 100% 0%, 100% 84%, 0% 100%, 0% 0%);
}

.news-block-one .inner-box .image-box:before {
    position: absolute;
    content: '';
    background: #000;
    width: 100%;
    height: 0%;
    left: 0px;
    top: 0px;
    opacity: 0.4;
    z-index: 1;
    transition: all 500ms ease;
}

.news-block-one .inner-box:hover .image-box:before {
    height: 100%;
}

.news-block-one .inner-box .image-box img {
    width: 100%;
}

.news-block-one .inner-box .image-box a {
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 100%;
    height: 100%;
    font-size: 0px;
    color: #ffffff;
    z-index: 2;
}

.news-block-one .inner-box .lower-content {
    position: relative;
    padding: 26px 30px 40px 30px;
}

.news-block-one .inner-box .lower-content .post-date {
    position: absolute;
    display: inline-block;
    right: 30px;
    top: -45px;
    width: 90px;
    height: 90px;
    background: #f2f3f5;
    text-align: center;
    border-radius: 50%;
    border: 5px solid #ffffff;
    padding: 13px 0px 10px 0px;
    box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
    margin-top: -45px;
    margin-bottom: 23px;
}

.news-block-one .inner-box .lower-content .post-date h3 {
    display: block;
    font-size: 32px;
    line-height: 32px;
    font-weight: 700;
}

.news-block-one .inner-box .lower-content .post-date h3 span {
    display: block;
    font-size: 15px;
    line-height: 16px;
    font-weight: 600;
    color: #142441;
    text-transform: uppercase;
    margin-top: 4px;
}

.news-block-one .inner-box .lower-content h4 {
    display: block;
    font-size: 20px;
    line-height: 26px;
    font-weight: 700;
    margin-bottom: 12px;
}

.news-block-one .inner-box .lower-content h4 a {
    display: inline-block;
    color: #142441;
}

.news-block-one .inner-box .lower-content h4 a:hover {

}

.news-block-one .inner-box .lower-content .post-info {
    position: relative;
    display: block;
    margin-bottom: 16px;
}

.news-block-one .inner-box .lower-content .post-info a {
    color: #848484;
    display: inline-block;
}

.news-block-one .inner-box .lower-content .post-info a:hover {

}

.news-block-one .inner-box .lower-content .post-info p {
    margin: 0px;
}

.news-block-one .inner-box .lower-content p {
    margin-bottom: 24px;
}

.news-block-one .inner-box .lower-content .theme-btn-two {
    padding: 7.5px 27px;
}

.news-block-one .inner-box .lower-content .shape {
    position: absolute;
    top: -80px;
    right: 15px;
    width: 93px;
    height: 92px;
    background-repeat: no-repeat;
}

.news-section .owl-nav {
    position: absolute;
    right: 0px;
    top: -84px;
}

.news-section .owl-nav .owl-prev,
.news-section .owl-nav .owl-next {
    position: relative;
    display: inline-block;
    font-size: 20px;
    line-height: 30px;
    color: #bbbbbb;
    cursor: pointer;
    transition: all 500ms ease;
}

.news-section .owl-nav .owl-prev {
    margin-right: 20px;
}

.news-section .owl-nav .owl-prev:hover,
.news-section .owl-nav .owl-next:hover {

}


/** contact-section **/

.ml-70 {
    margin-left: 70px;
}


/** main-footer **/

.main-footer {
    position: relative;
}

.footer-top {
    position: relative;
    padding: 120px 0px 75px 0px;
    border-bottom: 1px solid #e3e4e6;
}

.footer-top .logo-widget .footer-logo {
    position: relative;
    margin-bottom: 33px;
}

.footer-top .logo-widget .footer-logo .logo {
    max-width: 200px;
    width: 100%;
}

.footer-top .logo-widget .footer-logo .logo img {
    width: 100%;
}

.footer-top .logo-widget .text p {
    margin-bottom: 13px;
}

.footer-top .logo-widget .text .info li {
    position: relative;
    display: block;
    margin-bottom: 10px;
    padding-left: 34px;
    font-size: 16px;
    line-height: 30px;
    color: #142441;
}

.footer-top .logo-widget .text .info li:last-child {
    margin-bottom: 0px;
}

.footer-top .logo-widget .text .info li a {
    font-family: 'Open Sans', sans-serif;
}

.footer-top .logo-widget .text .info li a:hover {

}

.footer-top .logo-widget .text .info li i {
    position: absolute;
    left: 0px;
    top: 0px;
    font-size: 20px;
    line-height: 30px;
}

.footer-top .logo-widget {
    position: relative;
    margin-top: -18px;
}

.footer-top p,
.footer-top a {
    color: #142441;
    font-weight: 500;
}

.footer-top p {
    line-height: 30px;
}

.footer-top .widget-title {
    position: relative;
    display: block;
    margin-bottom: 39px;
}

.footer-top .widget-title h4 {
    font-size: 20px;
    line-height: 26px;
    font-weight: 700;
}

.footer-top .links-widget .links-list li {
    position: relative;
    display: block;
    margin-bottom: 10px;
}

.footer-top .links-widget .links-list li:last-child {
    margin-bottom: 0px;
}

.footer-top .links-widget .links-list li a {
    position: relative;
    font-size: 16px;
    display: inline-block;
}

.footer-top .links-widget .links-list li a:hover {

}

.footer-top .links-widget .links-list li a:before {
    position: absolute;
    content: '';
    width: 0%;
    height: 1px;
    left: 0px;
    bottom: 4px;
    transition: all 500ms ease;
}

.footer-top .links-widget .links-list li a:hover:before {
    width: 100%;
}

.footer-top .shape .shape-1 {
    position: absolute;
    left: 175px;
    top: 125px;
    width: 93px;
    height: 93px;
    background-repeat: no-repeat;
    z-index: 1;
}

.footer-top .shape .shape-2 {
    position: absolute;
    right: 90px;
    top: 55px;
    width: 93px;
    height: 93px;
    background-repeat: no-repeat;
    z-index: 1;
}

.footer-top .shape .shape-3 {
    position: absolute;
    left: 50px;
    top: 140px;
    background: #fff;
    width: 195px;
    height: 195px;
    border-radius: 50%;
}

.footer-top .shape .shape-4 {
    position: absolute;
    right: 115px;
    top: 70px;
    background: #fff;
    width: 195px;
    height: 195px;
    border-radius: 50%;
}

.footer-bottom {
    position: relative;
    width: 100%;
    padding: 34px 0px;
}

.footer-bottom p,
.footer-bottom a {
    font-size: 16px;
    color: #142441;
    font-weight: 500;
}

.footer-bottom a:hover {

}


/***

====================================================================
                        Home-Page-Two
====================================================================

***/

/** header-style-two **/


/** xs-sidebar **/


/** banner-style-two **/

.banner-section .owl-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
}

.banner-section .owl-nav button {
    position: absolute;
    display: inline-block;
    width: 70px;
    height: 70px;
    line-height: 74px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    background: transparent;
    text-align: center;
    border-radius: 50%;
    font-size: 24px;
    color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 500ms ease;
}

.banner-section .owl-nav .owl-prev {
    left: 50px;
}

.banner-section .owl-nav .owl-next {
    right: 50px;
}

.banner-section .owl-nav button:hover {
    background: #fff;
    border-color: #fff;
    color: #142441;
}

h4 {
    display: block;
    font-size: 20px;
    line-height: 26px;
    font-weight: 700;
}

h4 a {
    display: inline-block;
    color: #142441;
}

h4 a:hover {

}


/** feature-section **/


/** about-style-two **/

.about-style-two {
    padding-bottom: 144px;
}

.content_block_1 .content-box .inner-box {
    position: relative;
    display: block;
    padding-top: 39px;
    margin-top: 31px;
    border-top: 1px solid #e5e5e5;
}


/** chooseus-section **/


/** service-style-two **/


/** team-style-two **/


/***

====================================================================
                        Home-Page-Three
====================================================================

***/


/** banner-style-three **/


/** about-style-three **/

.content_block_1 .content-box .bold-text p {
    color: #142441;
}


/** service-style-three **/


/** funfact-style-two **/


/** faq-section **/

.testimonial-section.alternat-2:before {
    background: -webkit-linear-gradient(180deg, rgba(242, 243, 245, 1) 10%, rgba(242, 243, 245, 0.0) 100%);
}

.testimonial-section .shape .shape-1 {
    position: absolute;
    top: 195px;
    right: 210px;
    width: 93px;
    height: 93px;
    background-repeat: no-repeat;
    z-index: 1;
}

.testimonial-section .shape .shape-2 {
    position: absolute;
    top: 80px;
    right: 80px;
    width: 195px;
    height: 192px;
    background: #f9f9fa;
    border-radius: 50%;
}


/** team-style-three **/


/** cta-section **/

.main-footer.alternat-2 {
    position: relative;
    padding-top: 144px;
}

.main-footer.alternat-2 .pattern-layer {
    position: absolute;
    left: 0px;
    top: -45px;
    width: 100%;
    height: 45px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top center;
}


/***

====================================================================
                        Home-Page-Four
====================================================================

***/


/** header-style-four **/

.main-header.style-four {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    background: transparent;
    padding: 0px 70px;
}

.main-header.style-four .header-lower .outer-box {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

.main-header.style-four .header-lower .logo-box {
    padding-right: 0px;
    padding-top: 10px;
    padding-bottom: 20px;
}

.main-header.style-four .menu-right-content li {
    position: relative;
    display: inline-block;
    float: left;
}

.main-header.style-four .menu-right-content li.search-btn {
    margin-right: 39px;
    margin-top: 13px;
}

.main-header.style-four .menu-right-content li.search-btn button {
    color: #142441;
    font-size: 22px;
    transition: all 500ms ease;
}

.main-header.style-four .menu-right-content li.search-btn button:hover {

}

.main-header.style-four .header-lower .main-menu .navigation > li {
    padding: 38px 0px;
}

.main-header.style-four .menu-right-content li .theme-btn-one {
    padding: 12px 43px 13px 43px;
}

.main-header.style-four .menu-right-content {
    padding-top: 7px;
}


/** banner-style-four **/

.banner-section.style-four {
    position: relative;
    padding: 95px 0px 170px 0px;
}

.banner-section.style-four .pattern-layer {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: bottom center;
}

.banner-section.style-four .image-box {
    position: relative;
    display: block;
    opacity: 0;
    transform: translateX(100px);
    transition: all 1000ms ease;
}

.banner-section.style-four .banner-carousel .active .image-box {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 700ms;
}

.banner-section.style-four .image-box .image img {
    max-width: 100%;
}

.banner-section.style-four .image-box .text {
    position: absolute;
    top: 115px;
    right: -110px;
    width: 230px;
    background: #fff;
    border-radius: 30px;
    padding: 50px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(34, 182, 175, 0.1);
}

.banner-section.style-four .image-box .text h3 {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 80px;
    line-height: 80px;
    background: #142441;
    border-radius: 20px;
    text-align: center;
    font-size: 32px;
    color: #fff;
    font-weight: 700;
    margin-bottom: 34px;
}

.banner-section.style-four .content-box h2 {
    font-size: 50px;
    line-height: 66px;
    font-weight: 800;
    margin-bottom: 32px;
    opacity: 0;
    padding-top: 50px;
    transform: translateY(50px);
    transition: all 1000ms ease;
}

.banner-section.style-four .banner-carousel .active .content-box h2 {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 700ms;
}

.banner-section.style-four .content-box p {
    font-size: 18px;
    line-height: 30px;
    color: #142441;
    margin-bottom: 42px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 1000ms ease;
}

.banner-section.style-four .banner-carousel .active .content-box p {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 1000ms;
}

.banner-section.style-four .content-box .btn-box {
    opacity: 0;
    transform: translateY(50px);
    transition: all 1000ms ease;
}

.banner-section.style-four .banner-carousel .active .content-box .btn-box {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 1300ms;
}

.banner-section.style-four .owl-nav button {
    color: #c0cccb;
    border-color: #c0cccb;
}

.banner-section.style-four .owl-nav button:hover {
    border-color: #fff;
    color: #142441;
}

.banner-section.style-four .image-box .shape .shape-1 {
    position: absolute;
    left: 0px;
    bottom: 60px;
    width: 173px;
    height: 173px;
    background-repeat: no-repeat;
}

.banner-section.style-four .image-box .shape .shape-2 {
    position: absolute;
    right: -80px;
    bottom: 240px;
    width: 173px;
    height: 173px;
    background-repeat: no-repeat;
}


/** about-style-four **/


/** service-style-four **/

@keyframes shine {
    100% {
        left: 125%;
    }
}


/** project-section **/

.project-block-one .inner-box {
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 20px;
}

.project-block-one .inner-box .image-box {
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 20px;
}

.project-block-one .inner-box .image-box:before {
    position: absolute;
    content: '';
    background: #000;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    right: 0px;
    transform: scale(0, 0);
    opacity: 0.4;
    z-index: 1;
    transition: all 500ms ease;
}

.project-block-one .inner-box:hover .image-box:before {
    transform: scale(1, 1);
}

.project-block-one .inner-box .image-box img {
    width: 100%;
    border-radius: 20px;
    transition: all 500ms ease
}

.project-block-one .inner-box:hover .image-box img {
    transform: scale(1.05);
}

.project-block-one .inner-box .content-box {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 30px;
    top: 30px;
    width: calc(100% - 60px);
    height: calc(100% - 60px);
    background: transparent;
    border-radius: 20px;
    padding: 30px 20px 30px 20px;
    z-index: 2;
    transform: scale(0, 0);
    text-align: center;
    transition: all 500ms ease;
}

.project-block-one .inner-box:hover .content-box {
    transform: scale(1, 1);
}

.project-block-one .inner-box .content-box .view-btn a {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border: 2px solid #e5e5e5;
    text-align: center;
    border-radius: 50%;
    font-size: 30px;
    margin-bottom: 25px;
    background: #ffffff;
}

.project-block-one .inner-box .content-box .view-btn a:hover {
    color: #fff;
}

.project-block-one .inner-box .content-box h4 {
    margin-bottom: 18px;
}


/** news-style-two **/


/** cta-style-two **/


/***

====================================================================
                        Home-Page-Five
====================================================================

***/

/** header-style-five **/

.main-header.style-five .header-lower .main-menu .navigation > li > a {
    color: #fff;
}

.main-header.style-five .header-lower .menu-right-content li.search-btn button {
    color: #fff;
}


/** banner-style-five **/

.banner-section.style-five {
    background: #7393B3;
    padding: 110px 0px 130px 0px;
}

.banner-section.style-five .content-box h2,
.banner-section.style-five .content-box p {
    color: #fff;
}

.banner-section.style-five .image-box {
    position: relative;
    margin: 0px;
    padding-left: 60px;
    padding-bottom: 30px;
    padding-top: 50px;
}

.banner-section.style-five .image-box .image {
    position: relative;
    display: block;
    border-radius: 20px;
    z-index: -1;
}

.banner-section.style-five .image-box .image:before {
    position: absolute;
    content: '';
    background: #85C5C2;
    width: 100%;
    height: 100%;
    top: 30px;
    right: 30px;
    border-radius: 20px;
}

.banner-section.style-five .image-box .image img {
    width: 100%;
    border-radius: 20px;
}

.banner-section.style-five .image-box .shape .shape-3 {
    position: absolute;
    top: 0px;
    right: -105px;
    width: 263px;
    height: 263px;
    background-repeat: no-repeat;
}

.banner-section.style-five .image-box .shape .shape-4 {
    position: absolute;
    left: 60px;
    bottom: 120px;
    width: 263px;
    height: 263px;
    background-repeat: no-repeat;
    z-index: -1;
}

.banner-section.style-five .pattern-layer .pattern-1 {
    position: absolute;
    left: 240px;
    top: 145px;
    width: 263px;
    height: 263px;
    background-repeat: no-repeat;
}

.banner-section.style-five .pattern-layer .pattern-2 {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 994px;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
}

.banner-section.style-five .pattern-layer .pattern-3 {
    position: absolute;
    right: 0px;
    bottom: 0px;
    width: 524px;
    height: 793px;
    background-repeat: no-repeat;
}

.banner-section.style-five .pattern-layer .pattern-4 {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    height: 156px;
    background-repeat: no-repeat;
    background-size: cover;
}

.service-section.alternat-2:before {
    display: none;
}

.service-section .pattern-layer .pattern-3 {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 592px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: bottom center;
}

.image_block_8 .image-box {
    position: relative;
    display: block;
    padding: 70px 250px 125px 0px;
}

.about-style-two .image_block_8 .image-box {
    margin-right: 50px;
}

.image_block_8 .image-box .image {
    border-radius: 20px;
    box-shadow: 0 20px 50px #dfe3e3;
}

.image_block_8 .image-box .image img {
    width: 100%;
    border-radius: 20px;
}

.image_block_8 .image-box .image-2 {
    position: absolute;
    top: 0px;
    right: 0px;
}

.image_block_8 .image-box .image-3 {
    position: absolute;
    left: 200px;
    bottom: 0px;
    background: #fff;
    padding: 10px;
}

.image_block_8 .image-box .image-1 {
    position: relative;
}

.image_block_8 .image-box .shape .shape-1 {
    position: absolute;
    left: -65px;
    top: -30px;
    width: 211px;
    height: 211px;
    background-repeat: no-repeat;
}

.image_block_8 .image-box .shape .shape-2 {
    position: absolute;
    top: 145px;
    right: 140px;
    width: 153px;
    height: 162px;
    background-repeat: no-repeat;
}

.image_block_8 .image-box .shape .shape-3 {
    position: absolute;
    left: 95px;
    bottom: 85px;
    width: 153px;
    height: 162px;
    background-repeat: no-repeat;
}

.three-item-carousel .team-block-one .inner-box .image-box:before {
    z-index: 1;
}

.three-item-carousel .team-block-one .inner-box .image-box .social-style-one {
    z-index: 1;
}


/***

====================================================================
                        About-Page
====================================================================

***/

/** page-title **/

.page-title {
    position: relative;
    width: 100%;
    padding: 60px 0px 10px 0px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.page-title:before {
    position: absolute;
    content: '';
    /*
  background: #7393B3;
  */
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    right: 0px;
    opacity: 0.92;
}

.page-title .title {
    position: relative;
    display: block;
    margin-bottom: 5px;
}

.page-title .title h1 {
    display: block;
    font-size: 50px;
    line-height: 58px;
    color: #fff;
    font-weight: 800;
}

.page-title .bread-crumb li {
    position: relative;
    display: inline-block;
    font-size: 16px;
    line-height: 26px;
    font-family: 'Barlow', sans-serif;
    color: #fff;
    font-weight: 600;
    padding-right: 15px;
    margin-right: 5px;
}

.page-title .bread-crumb li:last-child {
    padding: 0px !important;
    margin: 0px !important;
}

.page-title .bread-crumb li a {
    display: inline-block;
    color: #fff;
}

.page-title .bread-crumb li a:hover {
    text-decoration: underline;
}

.page-title .bread-crumb li:before {
    position: absolute;
    content: '';
    background: #fff;
    width: 4px;
    height: 1px;
    top: 14px;
    right: 0px;
}

.page-title .bread-crumb li:last-child:before {
    display: none;
}

.page-title .content-box {
    position: relative;
    display: block;
}

.page-title .content-box .shape {
    position: absolute;
    left: 0px;
    top: -30px;
    width: 100%;
    height: 172px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-position: top center;
}


/** research-style-two **/


/** video-style-two **/


/***

====================================================================
                        Books-Page
====================================================================

***/


/** journal-section **/


/** future-member **/

.default-form .form-group {
    position: relative;
    margin-bottom: 23px;
}

.default-form .form-group:last-child {
    margin-bottom: 0px;
}

.default-form .form-group label {
    position: relative;
    display: block;
    font-size: 16px;
    color: #222;
    margin-bottom: 8px;
}

.default-form .form-group input[type='text'],
.default-form .form-group input[type='email'],
.default-form .form-group input[type='number'],
.default-form .form-group input[type='tel'],
.default-form .form-group select,
.default-form .form-group textarea {
    position: relative;
    display: block;
    width: 100%;
    height: 54px;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    font-size: 16px;
    color: #8b8e93;
    padding: 10px 20px;
    transition: all 500ms ease;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}


.default-form .form-group textarea {
    height: 150px;
    resize: none;
}

.default-form .form-group input:focus,
.default-form .form-group textarea:focus {

}


/** testimonial-style-two **/


/** question-section **/

/** error-section **/


/***

====================================================================
                        Team-Page
====================================================================

***/


/** education-section **/


/** awards-section **/


/***

====================================================================
                        Events-Page
====================================================================

***/

.events-sidebar {
    position: relative;
    display: block;
}

.events-sidebar h3 {
    font-size: 24px;
    line-height: 32px;
    font-weight: 700;
    margin-bottom: 23px;
}

.events-sidebar .speaker-info .info-list li {
    position: relative;
    display: block;
    padding-left: 26px;
    margin-bottom: 5px;
}

.events-sidebar .speaker-info .info-list li:last-child {
    margin-bottom: 0px;
}

.events-sidebar .speaker-info .info-list li i {
    position: absolute;
    left: 0px;
    top: 0px;
    font-size: 18px;
    line-height: 26px;
}

.events-sidebar .speaker-info .info-list li span {
    font-weight: 600;
    color: #142441;
}

.events-sidebar .speaker-info h3 {
    margin-bottom: 18px;
}

.events-sidebar .speaker-info {
    position: relative;
    margin-bottom: 43px;
}

.events-sidebar .map-inner {
    position: relative;
    display: block;
    background: #fff;
    padding: 10px;
    border-radius: 30px;
    overflow: hidden;
    box-shadow: 0 15px 30px 5px #dfdfdf;
}


/***

====================================================================
                        Gallery-Page
====================================================================

***/

.gallery-page-section {
    position: relative;
    padding: 25px 0px;
}

.gallery-page-section .filters {
    position: relative;
    display: block;
    margin-bottom: 50px;
}

.gallery-page-2 .project-block-one .inner-box {
    margin-bottom: 30px;
}


/***

====================================================================
                        Blog-Page
====================================================================

***/

.sidebar-page-container {
    position: relative;
}

.sidebar-page-container .news-block-one .inner-box {
    margin-bottom: 30px;
}

.blog-sidebar {
    position: relative;
    display: block;
    margin-left: 40px;
}

.blog-sidebar .widget-title {
    position: relative;
    display: block;
    margin-bottom: 34px;
}

.blog-sidebar .widget-title h3 {
    font-size: 24px;
    line-height: 30px;
    font-weight: 700;
}

.blog-sidebar .category-widget .category-list li {
    position: relative;
    display: block;
    margin-bottom: 16px;
}

.blog-sidebar .category-widget .category-list li:last-child {
    margin-bottom: 0px;
}

.blog-sidebar .category-widget .category-list li a {
    position: relative;
    display: inline-block;
    font-size: 18px;
    line-height: 26px;
    font-family: 'Barlow', sans-serif;
    color: #142441;
    font-weight: 500;
    padding-left: 5px;
}

.blog-sidebar .category-widget .category-list li a:hover {
    padding-left: 15px;
}

.blog-sidebar .category-widget .category-list li a:before {
    position: absolute;
    content: '\e938';
    font-family: 'icomoon';
    font-size: 14px;
    left: -10px;
    top: 1px;
    opacity: 1;
    transition: all 500ms ease;
    color: #142441;
}

.blog-sidebar .category-widget .category-list li a:hover:before {
    opacity: 1;
    left: 0px;
    color: #85c5c2;
}

.blog-sidebar .category-widget .widget-title {
    margin-bottom: 26px;
}

.blog-sidebar .category-widget {
    position: relative;
    margin-bottom: 56px;
}

.blog-sidebar .sidebar-widget .tags-list li {
    position: relative;
    display: inline-block;
    float: left;
    margin-right: 10px;
    margin-bottom: 10px;
}

.blog-sidebar .sidebar-widget .tags-list li a {
    position: relative;
    display: inline-block;
    font-size: 16px;
    line-height: 26px;
    font-family: 'Open Sans', sans-serif;
    color: #93959e;
    border: 1px solid #e5e5e5;
    border-radius: 30px;
    text-align: center;
    padding: 8.5px 13px;
}

.blog-sidebar .sidebar-widget .tags-list li a:hover {
    color: #fff;
}

.news-block-three .inner-box .lower-content {
    position: relative;
    display: block;
    padding-top: 32px;
}

.news-block-three .inner-box .lower-content .post-date {
    position: absolute;
    display: inline-block;
    left: 0px;
    top: 40px;
    width: 90px;
    height: 90px;
    background: #f2f3f5;
    text-align: center;
    border-radius: 50%;
    border: 5px solid #ffffff;
    padding: 13px 0px 10px 0px;
    box-shadow: 0 15px 30px 5px #e9e9e9;
}

.news-block-three .inner-box .lower-content .post-date h3 {
    display: block;
    font-size: 32px;
    line-height: 32px;
    font-weight: 700;
}

.news-block-three .inner-box .lower-content .post-date h3 span {
    display: block;
    font-size: 15px;
    line-height: 16px;
    font-weight: 600;
    color: #142441;
    text-transform: uppercase;
    margin-top: 4px;
}

.news-block-three .inner-box .lower-content .inner {
    position: relative;
    padding-left: 120px;
}

.news-block-three .inner-box .lower-content .inner h3 {
    display: block;
    font-size: 30px;
    line-height: 36px;
    font-weight: 700;
    margin-bottom: 8px;
}

.news-block-three .inner-box .lower-content .inner h3 a {
    display: inline-block;
    color: #142441;
}

.news-block-three .inner-box .lower-content .inner h3 a:hover {

}

.news-block-three .inner-box .lower-content .inner .post-info {
    position: relative;
    display: block;
    margin-bottom: 16px;
}

.news-block-three .inner-box .lower-content .inner .post-info a {
    color: #848484;
    display: inline-block;
}

.news-block-three .inner-box .lower-content .inner .post-info a:hover {

}

.news-block-three .inner-box .lower-content .inner .post-info p {
    margin: 0px;
}

.news-block-three .inner-box .lower-content .inner p {
    margin-bottom: 24px;
}

.news-block-three .inner-box .lower-content .inner .theme-btn-two {
    padding: 7.5px 27px;
}

.news-block-three .inner-box .image-box {
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 20px;
}

.news-block-three .inner-box .image-box img {
    width: 100%;
    border-radius: 20px;
    transition: all 500ms ease;
}

.news-block-three .inner-box:hover .image-box img {
    transform: scale(1.1);
}

.news-block-three .inner-box .image-box:before {
    position: absolute;
    content: '';
    background: #000;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    right: 0px;
    transform: scale(0, 0);
    z-index: 1;
    opacity: 0.5;
    transition: all 500ms ease;
}

.news-block-three .inner-box:hover .image-box:before {
    transform: scale(1, 1);
}

.news-block-three .inner-box .image-box a {
    position: absolute;
    display: inline-block;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 100%;
    height: 100%;
    font-size: 0px;
    color: #fff;
    z-index: 1;
}

.news-block-three .inner-box {
    margin-bottom: 70px;
}

.blog-details-content {
    position: relative;
    display: block;
}

.blog-details-content .news-block-three .inner-box {
    margin-bottom: 63px;
}

.blog-details-content .news-block-three .inner-box .lower-content {
    padding: 0px;
}

.blog-details-content .news-block-three .inner-box .lower-content .post-date {
    top: 8px;
}

.blog-details-content .image {
    position: relative;
    display: block;
    border-radius: 20px;
}

.blog-details-content .image img {
    width: 100%;
    border-radius: 20px;
}

.blog-details-content .content-one .image {
    margin-bottom: 63px;
}

.blog-details-content .content-one .text p {
    margin-bottom: 26px;
}

.blog-details-content h3 {
    display: block;
    font-size: 24px;
    line-height: 30px;
    font-weight: 700;
}

.blog-details-content .content-one .text h3 {
    padding-top: 30px;
    margin-bottom: 17px;
}

.blog-details-content ul {
    position: relative;
}

.blog-details-content ul li {
    color: #142441;
    display: block;
    font-family: Barlow, sans-serif;
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    margin-bottom: 11px;
    padding-left: 15px;
    position: relative;
}

.blog-details-content ul li:last-child {
    margin-bottom: 0px;
}

.blog-details-content ul li:before {
    position: absolute;
    content: '\e938';
    font-family: 'icomoon';
    font-size: 14px;
    left: 0px;
    top: 0px;
}

.blog-details-content ul li:before {
    position: absolute;
    content: '\e938';
    font-family: 'icomoon';
    font-size: 14px;
    left: 0px;
    top: 0px;
}

.blog-details {
    padding-top: 40px;
    padding-bottom: 40px;
}

.blog-details-content .content-one {
    position: relative;
    display: block;
    margin-bottom: 64px;
}

.blog-details-content .content-two .image-box {
    position: relative;
    margin: 0px -5px;
    margin-bottom: 63px;
}

.blog-details-content .content-two .image-box .image {
    position: relative;
    float: left;
    width: 33.333%;
    padding: 0px 5px;
}

.blog-details-content .content-two .image-box.two-image .image {
    position: relative;
    float: left;
    width: 50%;
    padding: 0px 5px;
}

.blog-details-content .content-two .text h3 {
    margin-bottom: 17px;
}

.blog-details-content .content-two {
    position: relative;
    display: block;
    margin-bottom: 63px;
}

.blog-details-content .author-box {
    position: relative;
    display: block;
    background: #e9f8f8;
    border-radius: 20px;
    overflow: hidden;
    padding: 43px 50px 50px 200px;
    margin-bottom: 75px;
}

.blog-details-content .author-box .author-thumb {
    position: absolute;
    left: 50px;
    top: 50px;
    width: 120px;
    height: 120px;
    border-radius: 50%;
}

.blog-details-content .author-box .author-thumb img {
    width: 100%;
    border-radius: 50%;
}

.blog-details-content .author-box .inner h3 {
    margin-bottom: 16px;
}

.blog-details-content .author-box .inner p {
    margin-bottom: 22px;
}

.blog-details-content .author-box .inner .social-links li {
    position: relative;
    display: inline-block;
    margin-right: 12px;
}

.blog-details-content .author-box .inner .social-links li:last-child {
    margin: 0px !important;
}

.blog-details-content .author-box .inner .social-links li a {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 44px;
    line-height: 44px;
    border: 1px solid #cddada;
    text-align: center;
    font-size: 16px;
    color: #142441;
    border-radius: 50%;
}

.blog-details-content .author-box .inner .social-links li a:hover {
    color: #fff;
}

.blog-details-content .author-box .inner p {
    line-height: 28px;
}

.blog-details-content .author-box .shape .shape-1 {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 93px;
    height: 53px;
    background-repeat: no-repeat;
}

.blog-details-content .author-box .shape .shape-2 {
    position: absolute;
    left: 50px;
    top: 95px;
    width: 93px;
    height: 93px;
    background-repeat: no-repeat;
}


/***

====================================================================
                        Contact-Page
====================================================================

***/

.google-map-section {
    position: relative;
    width: 100%;
}

.map-container {
    position: relative;
}

.service-block .inner-box.bg-dynamic {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    padding: 50px 10px 50px 10px;
}

.service-block:nth-child(1) .inner-box.bg-dynamic {
    background-image: url('/doc/academic-4.jpg');
}

.service-block:nth-child(2) .inner-box.bg-dynamic {
    background-image: url('/doc/academic-5.jpg');
}

.service-block:nth-child(3) .inner-box.bg-dynamic {
    background-image: url('/doc/academic-6.jpg');
}

.service-block:nth-child(4) .inner-box.bg-dynamic {
    background-image: url('/doc/academic-7.jpg');
}

.service-block-one .inner-box.bg-dynamic:before {
    background: #7393b396;
    height: 100%;
}

.service-block-one:hover .inner-box:before {
    background: #ffffff;
}

.service-block-one.mainlink:hover .inner-box:before {
    background: #7393b3;
}

.testimonial-section.bg-special:before {
    background: #e9f8f8;
}

.testimonial-section.bg-special .owl-nav {
    bottom: -10px;
}

.service-block-one .inner-box h4 {
    margin-bottom: 0px;
    font-size: 24px;
}

.content_block_3 .content-box .text {
    margin-bottom: 0px;
}

.testimonial-section .owl-nav .owl-prev,
.testimonial-section .owl-nav .owl-next {
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #85c5c2;
    border-radius: 100%;
    color: #85c5c2;
    transition: all 500ms ease;
}

.testimonial-section .owl-nav .owl-prev:hover,
.testimonial-section .owl-nav .owl-next:hover {
    border: #0c1529;
    color: #ffffff;
    background: #0c1529;
}

.testimonial-section .inner-content.announcement-text {
    max-width: 100%;
    padding: 20px 35px 20px 35px;
}

@media screen and (max-width: 992px) {
    .slider-reverse {
        flex-direction: column-reverse;
    }

    .page-title .title h1 {
        font-size: 30px;
        line-height: 37px;
    }
}

@media screen and (max-width: 500px) {
    .main-header .logo-box .logo img {
        width: 75%;
    }

    .banner-section.style-four .content-box h2 {
        padding-top: 25px;
        margin-bottom: 5px;
    }

    .content_block_3 .content-box .text p {
        font-size: 16px;
        line-height: 25px;
    }

    .paddings-of-announcement {
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .testimonial-section .inner-content.announcement-text {
        padding: 20px 15px 20px 15px;
    }

    .page-title .content-box .shape {
        position: fixed;
        top: -44px;
    }
}
#fixed-social {
    position: fixed;
    top: 130px;
    left: 0;

}

#fixed-social a {
    color: #fff;
    display: block;
    height: 40px;
    position: relative;
    text-align: center;
    line-height: 40px;
    width: 40px;
    margin-bottom: 1px;
    z-index: 2;
}

#fixed-social a:hover > span {
    visibility: visible;
    left: 41px;
    opacity: 1;
}

#fixed-social a span {
    line-height: 40px;
    left: 60px;
    position: absolute;
    text-align: center;
    width: 120px;
    visibility: hidden;
    transition-duration: 0.5s;
    z-index: 1;
    opacity: 0;
}

.fixed-facebook {
    background-color: #1b74e4;
}

.fixed-facebook span {
    background-color: #1b74e4;
}

.fixed-twitter {
    background-color: #1d9bf0;

}

.fixed-twitter span {
    background-color: #1d9bf0;
}

.fixed-gplus {
    background-color: #00AF54;

}

.fixed-gplus span {
    background-color: #00AF54;
}

.fixed-linkedin {
    background-color: #FFC41E;

}

.fixed-linkedin span {
    background-color: #FFC41E;
}

.fixed-instagrem {
    background-color: #ED2B29;

}

.fixed-instagrem span {
    background-color: #ED2B29;
}

.fixed-tumblr {
    background-color: #EB1471;

}

.fixed-tumblr span {
    background-color: #EB1471;
}

.service-block-one {
    cursor: pointer;
}

.team-block-one {
    background: #ffffff;
    padding: 15px;
    border-radius: 20px;
}

.float-image-right {
    float: right;
    max-width: 50%;
    padding-left: 10px;
}

.float-image-left {
    float: left;
    max-width: 50%;
    padding-right: 10px;
}

.date-badge {
    background: #ffffff;
    padding: 4px 10px;
    border-radius: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10;
}

/* Responsive Css */

@media only screen and (max-width: 1599px){

  .main-header.style-four{
    padding: 0px 15px;
  }

  .main-header.style-four .menu-right-content li.search-btn{
    margin-right: 15px;
  }

  .main-header.style-four .main-menu .navigation > li{
    margin: 0px 8px;
  }

}

@media only screen and (max-width: 1399px){

  .banner-section.style-four .image-box .text{
    position: relative;
    top: 0px;
    right: 0px;
    width: 100%;
    margin-top: 30px;
  }

}


@media only screen and (max-width: 1200px){

  .main-menu,
  .sticky-header{
    display: none !important;
  }

  .menu-area .mobile-nav-toggler {
    display: block;
    padding: 10px;
    margin-top: 15px;
  }

  .main-header .logo-box{
    padding-right: 90px;
  }

  .footer-top .shape{
    display: none;
  }

  .banner-section .owl-nav{
    display: none;
  }

  .main-header.style-five .menu-area .mobile-nav-toggler .icon-bar{
    background: #fff;
  }

  .project-block-one .inner-box .content-box .view-btn a{
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    margin-bottom: 10px;
  }

  .project-block-one .inner-box .content-box{
    padding-left: 10px;
    padding-right: 10px;
  }

  .project-block-one .inner-box .content-box h4{
    margin-bottom: 8px;
    font-size: 18px;
  }


}



@media only screen and (min-width: 768px){
  .main-menu .navigation > li > ul,
  .main-menu .navigation > li > ul > li > ul{
    display:block !important;
    visibility:hidden;
    opacity:0;
  }
}



@media only screen and (max-width: 991px){

  .main-header .logo-box{
    padding-right: 0px;
    float: none;
    text-align: center;
    display: block;
  }

  .main-header .logo-box .logo{
    margin: 0 auto;
  }

  .service-block-one .inner-box{
    margin-bottom: 30px;
  }

  .team-block-one .inner-box{
    margin-bottom: 30px;
  }

  .team-section{
    padding-bottom: 110px;
  }

  .footer-top .footer-widget{
    margin: 0px 0px 30px 0px !important;
  }

  .footer-top{
    padding-bottom: 45px;
  }

  .testimonial-section .shape{
    display: none;
  }

  .banner-section.style-four{
    padding-top: 180px;
  }

  .banner-section.style-four .content-box{
    margin-bottom: 14px;
  }

  .banner-section.style-five .image-box{
    padding-left: 0px;
  }

  .service-section{
    padding-top: 100px;
  }

  .about-style-two .image_block_8 .image-box{
    margin-right: 0px;
    margin-bottom: 50px;
  }

  .blog-sidebar{
    margin-left: 0px;
    margin-top: 30px;
  }

}


@media only screen and (max-width: 767px){

  .sec-pad{
    padding: 65px 0px 70px 0px;
  }

  .banner-section{
    padding: 100px 0px;
  }

  .banner-carousel .content-box h2{
    font-size: 36px;
    line-height: 44px;
  }

  .service-section{
    padding-top: 0px;
  }

  .service-section .pattern-layer{
    display: none;
  }

  .sec-title h2{
    font-size: 34px;
    line-height: 42px;
  }

  .sec-title h2 br{
    display: none;
  }

  .sec-title{
    margin-bottom: 40px;
  }

  .team-section{
    padding: 70px 0px 30px 0px;
  }

  .news-section .owl-nav{
    display: none;
  }

  .footer-top{
    padding-top: 80px;
  }

  .about-style-two{
    padding-bottom: 40px;
  }

  .content_block_1 .content-box{
    margin-left: 0px;
  }

  .paroller,
  .paroller-2{
    transform: translate(0px) !important;
  }

  .banner-section.style-four .content-box h2{
    font-size: 30px;
    line-height: 40px;
  }

  .main-header.style-four .menu-right-content li.btn-box{
    display: none;
  }

  .banner-section.style-four{
    padding-top: 160px;
    padding-bottom: 150px;
  }

  .banner-section.style-five .pattern-layer{
    display: none;
  }

  .banner-section.style-four{
    padding-bottom: 100px;
  }

  .gallery-page-section{
    padding: 65px 0px;
  }



}

@media only screen and (max-width: 599px){

  .testimonial-section .inner-content{
    padding-left: 30px;
    padding-right: 30px;
  }

  .testimonial-section .owl-nav{
    display: none;
  }

  .main-header .social-links{
    float: none;
    display: inline-block;
    text-align: center;
  }

  .main-header .social-links{
    margin-bottom: 10px;
  }

  .banner-section .content-box p br{
    display: none !important;
  }

  .image_block_8 .image-box .shape{
    display: none;
  }

  .page-title .title h1{
    font-size: 30px;
    line-height: 37px;
  }

  .news-block-three .inner-box .lower-content .inner h3{
    font-size: 20px;
    line-height: 26px;
  }

  .blog-details-content .news-block-three .inner-box .lower-content .post-date{
    top: 0px;
  }

}


@media only screen and (max-width: 499px){

  .mobile-menu{
    width: 100%;
  }

  
  .blog-details-content .content-two .image-box .image{
    width: 100%;
  }

  .main-header.style-four .header-lower .logo-box{
    text-align: center;
  }

  .main-header.style-four .header-lower .outer-box{
    display: flex;
  }

  .main-header.style-four .menu-right-content{
    position: absolute;
    left: 0px;
    bottom: 0px;
  }

  .banner-section.style-four{
    padding-top: 60px;
    padding-bottom: 0px;
  }

  .banner-section.style-four .image-box .shape{
    display: none;
  }

  .about-style-two .image_block_8 .image-box{
    padding: 0px;
  }

  .image_block_8 .image-box .image-2{
    position: relative;
    margin: 30px 0px;
  }

  .image_block_8 .image-box .image-3{
    position: relative;
    left: 0px;
  }

  .blog-details-content .content-two .image-box .image{
    margin-bottom: 15px;
  }

  .blog-details-content .author-box{
    padding-left: 30px;
    padding-right: 30px;
  }

  .blog-details-content .author-box .author-thumb{
    position: relative;
    left: 0px;
    top: 0px;
    margin-bottom: 15px;
  }


}



