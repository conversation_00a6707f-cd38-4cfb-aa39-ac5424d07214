#gallery {
    padding-top: 0px;
}
.img-wrapper {
    position: relative;
    margin-top: 15px;
}
.img-wrapper img {
    width: 100%;
}

.img-overlay {
    background: rgba(0, 0, 0, 0.7);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
}
.img-overlay i {
    color: #fff;
    font-size: 3em;
}

#overlay {
    background: rgba(0, 0, 0, 0.7);
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
#overlay img {
    margin: 0;
    width: 80%;
    height: auto;
    -o-object-fit: contain;
    object-fit: contain;
    padding: 5%;
}
@media screen and (min-width: 768px) {
    #overlay img {
        width: 60%;
    }
}
@media screen and (min-width: 1200px) {
    #overlay img {
        width: 50%;
    }
}

#nextButton {
    color: #fff;
    font-size: 2em;
    transition: opacity 0.8s;
}
#nextButton:hover {
    opacity: 0.7;
}
@media screen and (min-width: 768px) {
    #nextButton {
        font-size: 3em;
    }
}

#prevButton {
    color: #fff;
    font-size: 2em;
    transition: opacity 0.8s;
}
#prevButton:hover {
    opacity: 0.7;
}
@media screen and (min-width: 768px) {
    #prevButton {
        font-size: 3em;
    }
}

#exitButton {
    color: #fff;
    font-size: 2em;
    transition: opacity 0.8s;
    position: absolute;
    top: 15px;
    right: 15px;
}
#exitButton:hover {
    opacity: 0.7;
}
@media screen and (min-width: 768px) {
    #exitButton {
        font-size: 3em;
    }
}
