!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,(function(t,e){"use strict";var n=[],i=Object.getPrototypeOf,o=n.slice,r=n.flat?function(t){return n.flat.call(t)}:function(t){return n.concat.apply([],t)},s=n.push,a=n.indexOf,l={},c=l.toString,u=l.hasOwnProperty,h=u.toString,d=h.call(Object),p={},f=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},g=function(t){return null!=t&&t===t.window},m=t.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(t,e,n){var i,o,r=(n=n||m).createElement("script");if(r.text=t,e)for(i in v)(o=e[i]||e.getAttribute&&e.getAttribute(i))&&r.setAttribute(i,o);n.head.appendChild(r).parentNode.removeChild(r)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?l[c.call(t)]||"object":typeof t}var _="3.6.0",w=function(t,e){return new w.fn.init(t,e)};function x(t){var e=!!t&&"length"in t&&t.length,n=b(t);return!f(t)&&!g(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}w.fn=w.prototype={jquery:_,constructor:w,length:0,toArray:function(){return o.call(this)},get:function(t){return null==t?o.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=w.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return w.each(this,t)},map:function(t){return this.pushStack(w.map(this,(function(e,n){return t.call(e,n,e)})))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(w.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(w.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(0<=n&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:n.sort,splice:n.splice},w.extend=w.fn.extend=function(){var t,e,n,i,o,r,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||f(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)i=t[e],"__proto__"!==e&&s!==i&&(c&&i&&(w.isPlainObject(i)||(o=Array.isArray(i)))?(n=s[e],r=o&&!Array.isArray(n)?[]:o||w.isPlainObject(n)?n:{},o=!1,s[e]=w.extend(c,r,i)):void 0!==i&&(s[e]=i));return s},w.extend({expando:"jQuery"+(_+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==c.call(t)||(e=i(t))&&("function"!=typeof(n=u.call(e,"constructor")&&e.constructor)||h.call(n)!==d))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){y(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,i=0;if(x(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},makeArray:function(t,e){var n=e||[];return null!=t&&(x(Object(t))?w.merge(n,"string"==typeof t?[t]:t):s.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:a.call(e,t,n)},merge:function(t,e){for(var n=+e.length,i=0,o=t.length;i<n;i++)t[o++]=e[i];return t.length=o,t},grep:function(t,e,n){for(var i=[],o=0,r=t.length,s=!n;o<r;o++)!e(t[o],o)!==s&&i.push(t[o]);return i},map:function(t,e,n){var i,o,s=0,a=[];if(x(t))for(i=t.length;s<i;s++)null!=(o=e(t[s],s,n))&&a.push(o);else for(s in t)null!=(o=e(t[s],s,n))&&a.push(o);return r(a)},guid:1,support:p}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=n[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){l["[object "+e+"]"]=e.toLowerCase()}));var C=function(t){var e,n,i,o,r,s,a,l,c,u,h,d,p,f,g,m,v,y,b,_="sizzle"+1*new Date,w=t.document,x=0,C=0,E=lt(),T=lt(),S=lt(),D=lt(),A=function(t,e){return t===e&&(h=!0),0},I={}.hasOwnProperty,L=[],k=L.pop,O=L.push,P=L.push,N=L.slice,j=function(t,e){for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n;return-1},$="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",M="[\\x20\\t\\r\\n\\f]",z="(?:\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",H="\\["+M+"*("+z+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+z+"))|)"+M+"*\\]",R=":("+z+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+H+")*)|.*)\\)|)",q=new RegExp(M+"+","g"),F=new RegExp("^"+M+"+|((?:^|[^\\\\])(?:\\\\.)*)"+M+"+$","g"),W=new RegExp("^"+M+"*,"+M+"*"),B=new RegExp("^"+M+"*([>+~]|"+M+")"+M+"*"),U=new RegExp(M+"|>"),Y=new RegExp(R),X=new RegExp("^"+z+"$"),Q={ID:new RegExp("^#("+z+")"),CLASS:new RegExp("^\\.("+z+")"),TAG:new RegExp("^("+z+"|[*])"),ATTR:new RegExp("^"+H),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+M+"*(even|odd|(([+-]|)(\\d*)n|)"+M+"*(?:([+-]|)"+M+"*(\\d+)|))"+M+"*\\)|)","i"),bool:new RegExp("^(?:"+$+")$","i"),needsContext:new RegExp("^"+M+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+M+"*((?:-\\d)?\\d*)"+M+"*\\)|)(?=[^-]|$)","i")},V=/HTML$/i,K=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,G=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=new RegExp("\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\([^\\r\\n\\f])","g"),nt=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},it=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ot=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},rt=function(){d()},st=_t((function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()}),{dir:"parentNode",next:"legend"});try{P.apply(L=N.call(w.childNodes),w.childNodes),L[w.childNodes.length].nodeType}catch(e){P={apply:L.length?function(t,e){O.apply(t,N.call(e))}:function(t,e){for(var n=t.length,i=0;t[n++]=e[i++];);t.length=n-1}}}function at(t,e,i,o){var r,a,c,u,h,f,v,y=e&&e.ownerDocument,w=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==w&&9!==w&&11!==w)return i;if(!o&&(d(e),e=e||p,g)){if(11!==w&&(h=J.exec(t)))if(r=h[1]){if(9===w){if(!(c=e.getElementById(r)))return i;if(c.id===r)return i.push(c),i}else if(y&&(c=y.getElementById(r))&&b(e,c)&&c.id===r)return i.push(c),i}else{if(h[2])return P.apply(i,e.getElementsByTagName(t)),i;if((r=h[3])&&n.getElementsByClassName&&e.getElementsByClassName)return P.apply(i,e.getElementsByClassName(r)),i}if(n.qsa&&!D[t+" "]&&(!m||!m.test(t))&&(1!==w||"object"!==e.nodeName.toLowerCase())){if(v=t,y=e,1===w&&(U.test(t)||B.test(t))){for((y=tt.test(t)&&vt(e.parentNode)||e)===e&&n.scope||((u=e.getAttribute("id"))?u=u.replace(it,ot):e.setAttribute("id",u=_)),a=(f=s(t)).length;a--;)f[a]=(u?"#"+u:":scope")+" "+bt(f[a]);v=f.join(",")}try{return P.apply(i,y.querySelectorAll(v)),i}catch(e){D(t,!0)}finally{u===_&&e.removeAttribute("id")}}}return l(t.replace(F,"$1"),e,i,o)}function lt(){var t=[];return function e(n,o){return t.push(n+" ")>i.cacheLength&&delete e[t.shift()],e[n+" "]=o}}function ct(t){return t[_]=!0,t}function ut(t){var e=p.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function ht(t,e){for(var n=t.split("|"),o=n.length;o--;)i.attrHandle[n[o]]=e}function dt(t,e){var n=e&&t,i=n&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function pt(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function ft(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}function gt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&st(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function mt(t){return ct((function(e){return e=+e,ct((function(n,i){for(var o,r=t([],n.length,e),s=r.length;s--;)n[o=r[s]]&&(n[o]=!(i[o]=n[o]))}))}))}function vt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in n=at.support={},r=at.isXML=function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!V.test(e||n&&n.nodeName||"HTML")},d=at.setDocument=function(t){var e,o,s=t?t.ownerDocument||t:w;return s!=p&&9===s.nodeType&&s.documentElement&&(f=(p=s).documentElement,g=!r(p),w!=p&&(o=p.defaultView)&&o.top!==o&&(o.addEventListener?o.addEventListener("unload",rt,!1):o.attachEvent&&o.attachEvent("onunload",rt)),n.scope=ut((function(t){return f.appendChild(t).appendChild(p.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length})),n.attributes=ut((function(t){return t.className="i",!t.getAttribute("className")})),n.getElementsByTagName=ut((function(t){return t.appendChild(p.createComment("")),!t.getElementsByTagName("*").length})),n.getElementsByClassName=G.test(p.getElementsByClassName),n.getById=ut((function(t){return f.appendChild(t).id=_,!p.getElementsByName||!p.getElementsByName(_).length})),n.getById?(i.filter.ID=function(t){var e=t.replace(et,nt);return function(t){return t.getAttribute("id")===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n=e.getElementById(t);return n?[n]:[]}}):(i.filter.ID=function(t){var e=t.replace(et,nt);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n,i,o,r=e.getElementById(t);if(r){if((n=r.getAttributeNode("id"))&&n.value===t)return[r];for(o=e.getElementsByName(t),i=0;r=o[i++];)if((n=r.getAttributeNode("id"))&&n.value===t)return[r]}return[]}}),i.find.TAG=n.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):n.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,i=[],o=0,r=e.getElementsByTagName(t);if("*"===t){for(;n=r[o++];)1===n.nodeType&&i.push(n);return i}return r},i.find.CLASS=n.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&g)return e.getElementsByClassName(t)},v=[],m=[],(n.qsa=G.test(p.querySelectorAll))&&(ut((function(t){var e;f.appendChild(t).innerHTML="<a id='"+_+"'></a><select id='"+_+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+M+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||m.push("\\["+M+"*(?:value|"+$+")"),t.querySelectorAll("[id~="+_+"-]").length||m.push("~="),(e=p.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||m.push("\\["+M+"*name"+M+"*="+M+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||m.push(":checked"),t.querySelectorAll("a#"+_+"+*").length||m.push(".#.+[+~]"),t.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")})),ut((function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=p.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&m.push("name"+M+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),f.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),m.push(",.*:")}))),(n.matchesSelector=G.test(y=f.matches||f.webkitMatchesSelector||f.mozMatchesSelector||f.oMatchesSelector||f.msMatchesSelector))&&ut((function(t){n.disconnectedMatch=y.call(t,"*"),y.call(t,"[s!='']:x"),v.push("!=",R)})),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),e=G.test(f.compareDocumentPosition),b=e||G.test(f.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},A=e?function(t,e){if(t===e)return h=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!n.sortDetached&&e.compareDocumentPosition(t)===i?t==p||t.ownerDocument==w&&b(w,t)?-1:e==p||e.ownerDocument==w&&b(w,e)?1:u?j(u,t)-j(u,e):0:4&i?-1:1)}:function(t,e){if(t===e)return h=!0,0;var n,i=0,o=t.parentNode,r=e.parentNode,s=[t],a=[e];if(!o||!r)return t==p?-1:e==p?1:o?-1:r?1:u?j(u,t)-j(u,e):0;if(o===r)return dt(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?dt(s[i],a[i]):s[i]==w?-1:a[i]==w?1:0}),p},at.matches=function(t,e){return at(t,null,null,e)},at.matchesSelector=function(t,e){if(d(t),n.matchesSelector&&g&&!D[e+" "]&&(!v||!v.test(e))&&(!m||!m.test(e)))try{var i=y.call(t,e);if(i||n.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){D(e,!0)}return 0<at(e,p,null,[t]).length},at.contains=function(t,e){return(t.ownerDocument||t)!=p&&d(t),b(t,e)},at.attr=function(t,e){(t.ownerDocument||t)!=p&&d(t);var o=i.attrHandle[e.toLowerCase()],r=o&&I.call(i.attrHandle,e.toLowerCase())?o(t,e,!g):void 0;return void 0!==r?r:n.attributes||!g?t.getAttribute(e):(r=t.getAttributeNode(e))&&r.specified?r.value:null},at.escape=function(t){return(t+"").replace(it,ot)},at.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},at.uniqueSort=function(t){var e,i=[],o=0,r=0;if(h=!n.detectDuplicates,u=!n.sortStable&&t.slice(0),t.sort(A),h){for(;e=t[r++];)e===t[r]&&(o=i.push(r));for(;o--;)t.splice(i[o],1)}return u=null,t},o=at.getText=function(t){var e,n="",i=0,r=t.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=o(t)}else if(3===r||4===r)return t.nodeValue}else for(;e=t[i++];)n+=o(e);return n},(i=at.selectors={cacheLength:50,createPseudo:ct,match:Q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(et,nt),t[3]=(t[3]||t[4]||t[5]||"").replace(et,nt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||at.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&at.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return Q.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&Y.test(n)&&(e=s(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(et,nt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=E[t+" "];return e||(e=new RegExp("(^|"+M+")"+t+"("+M+"|$)"))&&E(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,n){return function(i){var o=at.attr(i,t);return null==o?"!="===e:!e||(o+="","="===e?o===n:"!="===e?o!==n:"^="===e?n&&0===o.indexOf(n):"*="===e?n&&-1<o.indexOf(n):"$="===e?n&&o.slice(-n.length)===n:"~="===e?-1<(" "+o.replace(q," ")+" ").indexOf(n):"|="===e&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,i,o){var r="nth"!==t.slice(0,3),s="last"!==t.slice(-4),a="of-type"===e;return 1===i&&0===o?function(t){return!!t.parentNode}:function(e,n,l){var c,u,h,d,p,f,g=r!==s?"nextSibling":"previousSibling",m=e.parentNode,v=a&&e.nodeName.toLowerCase(),y=!l&&!a,b=!1;if(m){if(r){for(;g;){for(d=e;d=d[g];)if(a?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;f=g="only"===t&&!f&&"nextSibling"}return!0}if(f=[s?m.firstChild:m.lastChild],s&&y){for(b=(p=(c=(u=(h=(d=m)[_]||(d[_]={}))[d.uniqueID]||(h[d.uniqueID]={}))[t]||[])[0]===x&&c[1])&&c[2],d=p&&m.childNodes[p];d=++p&&d&&d[g]||(b=p=0)||f.pop();)if(1===d.nodeType&&++b&&d===e){u[t]=[x,p,b];break}}else if(y&&(b=p=(c=(u=(h=(d=e)[_]||(d[_]={}))[d.uniqueID]||(h[d.uniqueID]={}))[t]||[])[0]===x&&c[1]),!1===b)for(;(d=++p&&d&&d[g]||(b=p=0)||f.pop())&&((a?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++b||(y&&((u=(h=d[_]||(d[_]={}))[d.uniqueID]||(h[d.uniqueID]={}))[t]=[x,b]),d!==e)););return(b-=o)===i||b%i==0&&0<=b/i}}},PSEUDO:function(t,e){var n,o=i.pseudos[t]||i.setFilters[t.toLowerCase()]||at.error("unsupported pseudo: "+t);return o[_]?o(e):1<o.length?(n=[t,t,"",e],i.setFilters.hasOwnProperty(t.toLowerCase())?ct((function(t,n){for(var i,r=o(t,e),s=r.length;s--;)t[i=j(t,r[s])]=!(n[i]=r[s])})):function(t){return o(t,0,n)}):o}},pseudos:{not:ct((function(t){var e=[],n=[],i=a(t.replace(F,"$1"));return i[_]?ct((function(t,e,n,o){for(var r,s=i(t,null,o,[]),a=t.length;a--;)(r=s[a])&&(t[a]=!(e[a]=r))})):function(t,o,r){return e[0]=t,i(e,null,r,n),e[0]=null,!n.pop()}})),has:ct((function(t){return function(e){return 0<at(t,e).length}})),contains:ct((function(t){return t=t.replace(et,nt),function(e){return-1<(e.textContent||o(e)).indexOf(t)}})),lang:ct((function(t){return X.test(t||"")||at.error("unsupported lang: "+t),t=t.replace(et,nt).toLowerCase(),function(e){var n;do{if(n=g?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===f},focus:function(t){return t===p.activeElement&&(!p.hasFocus||p.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:gt(!1),disabled:gt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!i.pseudos.empty(t)},header:function(t){return Z.test(t.nodeName)},input:function(t){return K.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:mt((function(){return[0]})),last:mt((function(t,e){return[e-1]})),eq:mt((function(t,e,n){return[n<0?n+e:n]})),even:mt((function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t})),odd:mt((function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t})),lt:mt((function(t,e,n){for(var i=n<0?n+e:e<n?e:n;0<=--i;)t.push(i);return t})),gt:mt((function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t}))}}).pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[e]=pt(e);for(e in{submit:!0,reset:!0})i.pseudos[e]=ft(e);function yt(){}function bt(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function _t(t,e,n){var i=e.dir,o=e.next,r=o||i,s=n&&"parentNode"===r,a=C++;return e.first?function(e,n,o){for(;e=e[i];)if(1===e.nodeType||s)return t(e,n,o);return!1}:function(e,n,l){var c,u,h,d=[x,a];if(l){for(;e=e[i];)if((1===e.nodeType||s)&&t(e,n,l))return!0}else for(;e=e[i];)if(1===e.nodeType||s)if(u=(h=e[_]||(e[_]={}))[e.uniqueID]||(h[e.uniqueID]={}),o&&o===e.nodeName.toLowerCase())e=e[i]||e;else{if((c=u[r])&&c[0]===x&&c[1]===a)return d[2]=c[2];if((u[r]=d)[2]=t(e,n,l))return!0}return!1}}function wt(t){return 1<t.length?function(e,n,i){for(var o=t.length;o--;)if(!t[o](e,n,i))return!1;return!0}:t[0]}function xt(t,e,n,i,o){for(var r,s=[],a=0,l=t.length,c=null!=e;a<l;a++)(r=t[a])&&(n&&!n(r,i,o)||(s.push(r),c&&e.push(a)));return s}function Ct(t,e,n,i,o,r){return i&&!i[_]&&(i=Ct(i)),o&&!o[_]&&(o=Ct(o,r)),ct((function(r,s,a,l){var c,u,h,d=[],p=[],f=s.length,g=r||function(t,e,n){for(var i=0,o=e.length;i<o;i++)at(t,e[i],n);return n}(e||"*",a.nodeType?[a]:a,[]),m=!t||!r&&e?g:xt(g,d,t,a,l),v=n?o||(r?t:f||i)?[]:s:m;if(n&&n(m,v,a,l),i)for(c=xt(v,p),i(c,[],a,l),u=c.length;u--;)(h=c[u])&&(v[p[u]]=!(m[p[u]]=h));if(r){if(o||t){if(o){for(c=[],u=v.length;u--;)(h=v[u])&&c.push(m[u]=h);o(null,v=[],c,l)}for(u=v.length;u--;)(h=v[u])&&-1<(c=o?j(r,h):d[u])&&(r[c]=!(s[c]=h))}}else v=xt(v===s?v.splice(f,v.length):v),o?o(null,s,v,l):P.apply(s,v)}))}function Et(t){for(var e,n,o,r=t.length,s=i.relative[t[0].type],a=s||i.relative[" "],l=s?1:0,u=_t((function(t){return t===e}),a,!0),h=_t((function(t){return-1<j(e,t)}),a,!0),d=[function(t,n,i){var o=!s&&(i||n!==c)||((e=n).nodeType?u(t,n,i):h(t,n,i));return e=null,o}];l<r;l++)if(n=i.relative[t[l].type])d=[_t(wt(d),n)];else{if((n=i.filter[t[l].type].apply(null,t[l].matches))[_]){for(o=++l;o<r&&!i.relative[t[o].type];o++);return Ct(1<l&&wt(d),1<l&&bt(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(F,"$1"),n,l<o&&Et(t.slice(l,o)),o<r&&Et(t=t.slice(o)),o<r&&bt(t))}d.push(n)}return wt(d)}return yt.prototype=i.filters=i.pseudos,i.setFilters=new yt,s=at.tokenize=function(t,e){var n,o,r,s,a,l,c,u=T[t+" "];if(u)return e?0:u.slice(0);for(a=t,l=[],c=i.preFilter;a;){for(s in n&&!(o=W.exec(a))||(o&&(a=a.slice(o[0].length)||a),l.push(r=[])),n=!1,(o=B.exec(a))&&(n=o.shift(),r.push({value:n,type:o[0].replace(F," ")}),a=a.slice(n.length)),i.filter)!(o=Q[s].exec(a))||c[s]&&!(o=c[s](o))||(n=o.shift(),r.push({value:n,type:s,matches:o}),a=a.slice(n.length));if(!n)break}return e?a.length:a?at.error(t):T(t,l).slice(0)},a=at.compile=function(t,e){var n,o,r,a,l,u,h=[],f=[],m=S[t+" "];if(!m){for(e||(e=s(t)),n=e.length;n--;)(m=Et(e[n]))[_]?h.push(m):f.push(m);(m=S(t,(o=f,a=0<(r=h).length,l=0<o.length,u=function(t,e,n,s,u){var h,f,m,v=0,y="0",b=t&&[],_=[],w=c,C=t||l&&i.find.TAG("*",u),E=x+=null==w?1:Math.random()||.1,T=C.length;for(u&&(c=e==p||e||u);y!==T&&null!=(h=C[y]);y++){if(l&&h){for(f=0,e||h.ownerDocument==p||(d(h),n=!g);m=o[f++];)if(m(h,e||p,n)){s.push(h);break}u&&(x=E)}a&&((h=!m&&h)&&v--,t&&b.push(h))}if(v+=y,a&&y!==v){for(f=0;m=r[f++];)m(b,_,e,n);if(t){if(0<v)for(;y--;)b[y]||_[y]||(_[y]=k.call(s));_=xt(_)}P.apply(s,_),u&&!t&&0<_.length&&1<v+r.length&&at.uniqueSort(s)}return u&&(x=E,c=w),b},a?ct(u):u))).selector=t}return m},l=at.select=function(t,e,n,o){var r,l,c,u,h,d="function"==typeof t&&t,p=!o&&s(t=d.selector||t);if(n=n||[],1===p.length){if(2<(l=p[0]=p[0].slice(0)).length&&"ID"===(c=l[0]).type&&9===e.nodeType&&g&&i.relative[l[1].type]){if(!(e=(i.find.ID(c.matches[0].replace(et,nt),e)||[])[0]))return n;d&&(e=e.parentNode),t=t.slice(l.shift().value.length)}for(r=Q.needsContext.test(t)?0:l.length;r--&&(c=l[r],!i.relative[u=c.type]);)if((h=i.find[u])&&(o=h(c.matches[0].replace(et,nt),tt.test(l[0].type)&&vt(e.parentNode)||e))){if(l.splice(r,1),!(t=o.length&&bt(l)))return P.apply(n,o),n;break}}return(d||a(t,p))(o,e,!g,n,!e||tt.test(t)&&vt(e.parentNode)||e),n},n.sortStable=_.split("").sort(A).join("")===_,n.detectDuplicates=!!h,d(),n.sortDetached=ut((function(t){return 1&t.compareDocumentPosition(p.createElement("fieldset"))})),ut((function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")}))||ht("type|href|height|width",(function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)})),n.attributes&&ut((function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")}))||ht("value",(function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue})),ut((function(t){return null==t.getAttribute("disabled")}))||ht($,(function(t,e,n){var i;if(!n)return!0===t[e]?e.toLowerCase():(i=t.getAttributeNode(e))&&i.specified?i.value:null})),at}(t);w.find=C,w.expr=C.selectors,w.expr[":"]=w.expr.pseudos,w.uniqueSort=w.unique=C.uniqueSort,w.text=C.getText,w.isXMLDoc=C.isXML,w.contains=C.contains,w.escapeSelector=C.escape;var E=function(t,e,n){for(var i=[],o=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&w(t).is(n))break;i.push(t)}return i},T=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},S=w.expr.match.needsContext;function D(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var A=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function I(t,e,n){return f(e)?w.grep(t,(function(t,i){return!!e.call(t,i,t)!==n})):e.nodeType?w.grep(t,(function(t){return t===e!==n})):"string"!=typeof e?w.grep(t,(function(t){return-1<a.call(e,t)!==n})):w.filter(e,t,n)}w.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?w.find.matchesSelector(i,t)?[i]:[]:w.find.matches(t,w.grep(e,(function(t){return 1===t.nodeType})))},w.fn.extend({find:function(t){var e,n,i=this.length,o=this;if("string"!=typeof t)return this.pushStack(w(t).filter((function(){for(e=0;e<i;e++)if(w.contains(o[e],this))return!0})));for(n=this.pushStack([]),e=0;e<i;e++)w.find(t,o[e],n);return 1<i?w.uniqueSort(n):n},filter:function(t){return this.pushStack(I(this,t||[],!1))},not:function(t){return this.pushStack(I(this,t||[],!0))},is:function(t){return!!I(this,"string"==typeof t&&S.test(t)?w(t):t||[],!1).length}});var L,k=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(w.fn.init=function(t,e,n){var i,o;if(!t)return this;if(n=n||L,"string"==typeof t){if(!(i="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:k.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof w?e[0]:e,w.merge(this,w.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:m,!0)),A.test(i[1])&&w.isPlainObject(e))for(i in e)f(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}return(o=m.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):f(t)?void 0!==n.ready?n.ready(t):t(w):w.makeArray(t,this)}).prototype=w.fn,L=w(m);var O=/^(?:parents|prev(?:Until|All))/,P={children:!0,contents:!0,next:!0,prev:!0};function N(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}w.fn.extend({has:function(t){var e=w(t,this),n=e.length;return this.filter((function(){for(var t=0;t<n;t++)if(w.contains(this,e[t]))return!0}))},closest:function(t,e){var n,i=0,o=this.length,r=[],s="string"!=typeof t&&w(t);if(!S.test(t))for(;i<o;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&w.find.matchesSelector(n,t))){r.push(n);break}return this.pushStack(1<r.length?w.uniqueSort(r):r)},index:function(t){return t?"string"==typeof t?a.call(w(t),this[0]):a.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),w.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return E(t,"parentNode")},parentsUntil:function(t,e,n){return E(t,"parentNode",n)},next:function(t){return N(t,"nextSibling")},prev:function(t){return N(t,"previousSibling")},nextAll:function(t){return E(t,"nextSibling")},prevAll:function(t){return E(t,"previousSibling")},nextUntil:function(t,e,n){return E(t,"nextSibling",n)},prevUntil:function(t,e,n){return E(t,"previousSibling",n)},siblings:function(t){return T((t.parentNode||{}).firstChild,t)},children:function(t){return T(t.firstChild)},contents:function(t){return null!=t.contentDocument&&i(t.contentDocument)?t.contentDocument:(D(t,"template")&&(t=t.content||t),w.merge([],t.childNodes))}},(function(t,e){w.fn[t]=function(n,i){var o=w.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(o=w.filter(i,o)),1<this.length&&(P[t]||w.uniqueSort(o),O.test(t)&&o.reverse()),this.pushStack(o)}}));var j=/[^\x20\t\r\n\f]+/g;function $(t){return t}function M(t){throw t}function z(t,e,n,i){var o;try{t&&f(o=t.promise)?o.call(t).done(e).fail(n):t&&f(o=t.then)?o.call(t,e,n):e.apply(void 0,[t].slice(i))}catch(t){n.apply(void 0,[t])}}w.Callbacks=function(t){var e,n;t="string"==typeof t?(e=t,n={},w.each(e.match(j)||[],(function(t,e){n[e]=!0})),n):w.extend({},t);var i,o,r,s,a=[],l=[],c=-1,u=function(){for(s=s||t.once,r=i=!0;l.length;c=-1)for(o=l.shift();++c<a.length;)!1===a[c].apply(o[0],o[1])&&t.stopOnFalse&&(c=a.length,o=!1);t.memory||(o=!1),i=!1,s&&(a=o?[]:"")},h={add:function(){return a&&(o&&!i&&(c=a.length-1,l.push(o)),function e(n){w.each(n,(function(n,i){f(i)?t.unique&&h.has(i)||a.push(i):i&&i.length&&"string"!==b(i)&&e(i)}))}(arguments),o&&!i&&u()),this},remove:function(){return w.each(arguments,(function(t,e){for(var n;-1<(n=w.inArray(e,a,n));)a.splice(n,1),n<=c&&c--})),this},has:function(t){return t?-1<w.inArray(t,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return s=l=[],a=o="",this},disabled:function(){return!a},lock:function(){return s=l=[],o||i||(a=o=""),this},locked:function(){return!!s},fireWith:function(t,e){return s||(e=[t,(e=e||[]).slice?e.slice():e],l.push(e),i||u()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!r}};return h},w.extend({Deferred:function(e){var n=[["notify","progress",w.Callbacks("memory"),w.Callbacks("memory"),2],["resolve","done",w.Callbacks("once memory"),w.Callbacks("once memory"),0,"resolved"],["reject","fail",w.Callbacks("once memory"),w.Callbacks("once memory"),1,"rejected"]],i="pending",o={state:function(){return i},always:function(){return r.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return w.Deferred((function(e){w.each(n,(function(n,i){var o=f(t[i[4]])&&t[i[4]];r[i[1]]((function(){var t=o&&o.apply(this,arguments);t&&f(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[i[0]+"With"](this,o?[t]:arguments)}))})),t=null})).promise()},then:function(e,i,o){var r=0;function s(e,n,i,o){return function(){var a=this,l=arguments,c=function(){var t,c;if(!(e<r)){if((t=i.apply(a,l))===n.promise())throw new TypeError("Thenable self-resolution");c=t&&("object"==typeof t||"function"==typeof t)&&t.then,f(c)?o?c.call(t,s(r,n,$,o),s(r,n,M,o)):(r++,c.call(t,s(r,n,$,o),s(r,n,M,o),s(r,n,$,n.notifyWith))):(i!==$&&(a=void 0,l=[t]),(o||n.resolveWith)(a,l))}},u=o?c:function(){try{c()}catch(t){w.Deferred.exceptionHook&&w.Deferred.exceptionHook(t,u.stackTrace),r<=e+1&&(i!==M&&(a=void 0,l=[t]),n.rejectWith(a,l))}};e?u():(w.Deferred.getStackHook&&(u.stackTrace=w.Deferred.getStackHook()),t.setTimeout(u))}}return w.Deferred((function(t){n[0][3].add(s(0,t,f(o)?o:$,t.notifyWith)),n[1][3].add(s(0,t,f(e)?e:$)),n[2][3].add(s(0,t,f(i)?i:M))})).promise()},promise:function(t){return null!=t?w.extend(t,o):o}},r={};return w.each(n,(function(t,e){var s=e[2],a=e[5];o[e[1]]=s.add,a&&s.add((function(){i=a}),n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),s.add(e[3].fire),r[e[0]]=function(){return r[e[0]+"With"](this===r?void 0:this,arguments),this},r[e[0]+"With"]=s.fireWith})),o.promise(r),e&&e.call(r,r),r},when:function(t){var e=arguments.length,n=e,i=Array(n),r=o.call(arguments),s=w.Deferred(),a=function(t){return function(n){i[t]=this,r[t]=1<arguments.length?o.call(arguments):n,--e||s.resolveWith(i,r)}};if(e<=1&&(z(t,s.done(a(n)).resolve,s.reject,!e),"pending"===s.state()||f(r[n]&&r[n].then)))return s.then();for(;n--;)z(r[n],a(n),s.reject);return s.promise()}});var H=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;w.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&H.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},w.readyException=function(e){t.setTimeout((function(){throw e}))};var R=w.Deferred();function q(){m.removeEventListener("DOMContentLoaded",q),t.removeEventListener("load",q),w.ready()}w.fn.ready=function(t){return R.then(t).catch((function(t){w.readyException(t)})),this},w.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--w.readyWait:w.isReady)||(w.isReady=!0)!==t&&0<--w.readyWait||R.resolveWith(m,[w])}}),w.ready.then=R.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?t.setTimeout(w.ready):(m.addEventListener("DOMContentLoaded",q),t.addEventListener("load",q));var F=function(t,e,n,i,o,r,s){var a=0,l=t.length,c=null==n;if("object"===b(n))for(a in o=!0,n)F(t,e,a,n[a],!0,r,s);else if(void 0!==i&&(o=!0,f(i)||(s=!0),c&&(s?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(w(t),n)})),e))for(;a<l;a++)e(t[a],n,s?i:i.call(t[a],a,e(t[a],n)));return o?t:c?e.call(t):l?e(t[0],n):r},W=/^-ms-/,B=/-([a-z])/g;function U(t,e){return e.toUpperCase()}function Y(t){return t.replace(W,"ms-").replace(B,U)}var X=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function Q(){this.expando=w.expando+Q.uid++}Q.uid=1,Q.prototype={cache:function(t){var e=t[this.expando];return e||(e={},X(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var i,o=this.cache(t);if("string"==typeof e)o[Y(e)]=n;else for(i in e)o[Y(i)]=e[i];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][Y(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,i=t[this.expando];if(void 0!==i){if(void 0!==e){n=(e=Array.isArray(e)?e.map(Y):(e=Y(e))in i?[e]:e.match(j)||[]).length;for(;n--;)delete i[e[n]]}(void 0===e||w.isEmptyObject(i))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!w.isEmptyObject(e)}};var V=new Q,K=new Q,Z=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,G=/[A-Z]/g;function J(t,e,n){var i,o;if(void 0===n&&1===t.nodeType)if(i="data-"+e.replace(G,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(i))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:Z.test(o)?JSON.parse(o):o)}catch(t){}K.set(t,e,n)}else n=void 0;return n}w.extend({hasData:function(t){return K.hasData(t)||V.hasData(t)},data:function(t,e,n){return K.access(t,e,n)},removeData:function(t,e){K.remove(t,e)},_data:function(t,e,n){return V.access(t,e,n)},_removeData:function(t,e){V.remove(t,e)}}),w.fn.extend({data:function(t,e){var n,i,o,r=this[0],s=r&&r.attributes;if(void 0===t){if(this.length&&(o=K.get(r),1===r.nodeType&&!V.get(r,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(i=s[n].name).indexOf("data-")&&(i=Y(i.slice(5)),J(r,i,o[i]));V.set(r,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each((function(){K.set(this,t)})):F(this,(function(e){var n;if(r&&void 0===e)return void 0!==(n=K.get(r,t))||void 0!==(n=J(r,t))?n:void 0;this.each((function(){K.set(this,t,e)}))}),null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each((function(){K.remove(this,t)}))}}),w.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=V.get(t,e),n&&(!i||Array.isArray(n)?i=V.access(t,e,w.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=w.queue(t,e),i=n.length,o=n.shift(),r=w._queueHooks(t,e);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===e&&n.unshift("inprogress"),delete r.stop,o.call(t,(function(){w.dequeue(t,e)}),r)),!i&&r&&r.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return V.get(t,n)||V.access(t,n,{empty:w.Callbacks("once memory").add((function(){V.remove(t,[e+"queue",n])}))})}}),w.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?w.queue(this[0],t):void 0===e?this:this.each((function(){var n=w.queue(this,t,e);w._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&w.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){w.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,o=w.Deferred(),r=this,s=this.length,a=function(){--i||o.resolveWith(r,[r])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";s--;)(n=V.get(r[s],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),o.promise(e)}});var tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+tt+")([a-z%]*)$","i"),nt=["Top","Right","Bottom","Left"],it=m.documentElement,ot=function(t){return w.contains(t.ownerDocument,t)},rt={composed:!0};it.getRootNode&&(ot=function(t){return w.contains(t.ownerDocument,t)||t.getRootNode(rt)===t.ownerDocument});var st=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&ot(t)&&"none"===w.css(t,"display")};function at(t,e,n,i){var o,r,s=20,a=i?function(){return i.cur()}:function(){return w.css(t,e,"")},l=a(),c=n&&n[3]||(w.cssNumber[e]?"":"px"),u=t.nodeType&&(w.cssNumber[e]||"px"!==c&&+l)&&et.exec(w.css(t,e));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)w.style(t,e,u+c),(1-r)*(1-(r=a()/l||.5))<=0&&(s=0),u/=r;u*=2,w.style(t,e,u+c),n=n||[]}return n&&(u=+u||+l||0,o=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=o)),o}var lt={};function ct(t,e){for(var n,i,o,r,s,a,l,c=[],u=0,h=t.length;u<h;u++)(i=t[u]).style&&(n=i.style.display,e?("none"===n&&(c[u]=V.get(i,"display")||null,c[u]||(i.style.display="")),""===i.style.display&&st(i)&&(c[u]=(l=s=r=void 0,s=(o=i).ownerDocument,a=o.nodeName,(l=lt[a])||(r=s.body.appendChild(s.createElement(a)),l=w.css(r,"display"),r.parentNode.removeChild(r),"none"===l&&(l="block"),lt[a]=l)))):"none"!==n&&(c[u]="none",V.set(i,"display",n)));for(u=0;u<h;u++)null!=c[u]&&(t[u].style.display=c[u]);return t}w.fn.extend({show:function(){return ct(this,!0)},hide:function(){return ct(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){st(this)?w(this).show():w(this).hide()}))}});var ut,ht,dt=/^(?:checkbox|radio)$/i,pt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ft=/^$|^module$|\/(?:java|ecma)script/i;ut=m.createDocumentFragment().appendChild(m.createElement("div")),(ht=m.createElement("input")).setAttribute("type","radio"),ht.setAttribute("checked","checked"),ht.setAttribute("name","t"),ut.appendChild(ht),p.checkClone=ut.cloneNode(!0).cloneNode(!0).lastChild.checked,ut.innerHTML="<textarea>x</textarea>",p.noCloneChecked=!!ut.cloneNode(!0).lastChild.defaultValue,ut.innerHTML="<option></option>",p.option=!!ut.lastChild;var gt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function mt(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&D(t,e)?w.merge([t],n):n}function vt(t,e){for(var n=0,i=t.length;n<i;n++)V.set(t[n],"globalEval",!e||V.get(e[n],"globalEval"))}gt.tbody=gt.tfoot=gt.colgroup=gt.caption=gt.thead,gt.th=gt.td,p.option||(gt.optgroup=gt.option=[1,"<select multiple='multiple'>","</select>"]);var yt=/<|&#?\w+;/;function bt(t,e,n,i,o){for(var r,s,a,l,c,u,h=e.createDocumentFragment(),d=[],p=0,f=t.length;p<f;p++)if((r=t[p])||0===r)if("object"===b(r))w.merge(d,r.nodeType?[r]:r);else if(yt.test(r)){for(s=s||h.appendChild(e.createElement("div")),a=(pt.exec(r)||["",""])[1].toLowerCase(),l=gt[a]||gt._default,s.innerHTML=l[1]+w.htmlPrefilter(r)+l[2],u=l[0];u--;)s=s.lastChild;w.merge(d,s.childNodes),(s=h.firstChild).textContent=""}else d.push(e.createTextNode(r));for(h.textContent="",p=0;r=d[p++];)if(i&&-1<w.inArray(r,i))o&&o.push(r);else if(c=ot(r),s=mt(h.appendChild(r),"script"),c&&vt(s),n)for(u=0;r=s[u++];)ft.test(r.type||"")&&n.push(r);return h}var _t=/^([^.]*)(?:\.(.+)|)/;function wt(){return!0}function xt(){return!1}function Ct(t,e){return t===function(){try{return m.activeElement}catch(t){}}()==("focus"===e)}function Et(t,e,n,i,o,r){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(i=i||n,n=void 0),e)Et(t,a,n,i,e[a],r);return t}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=xt;else if(!o)return t;return 1===r&&(s=o,(o=function(t){return w().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=w.guid++)),t.each((function(){w.event.add(this,e,o,i,n)}))}function Tt(t,e,n){n?(V.set(t,e,!1),w.event.add(t,e,{namespace:!1,handler:function(t){var i,r,s=V.get(this,e);if(1&t.isTrigger&&this[e]){if(s.length)(w.event.special[e]||{}).delegateType&&t.stopPropagation();else if(s=o.call(arguments),V.set(this,e,s),i=n(this,e),this[e](),s!==(r=V.get(this,e))||i?V.set(this,e,!1):r={},s!==r)return t.stopImmediatePropagation(),t.preventDefault(),r&&r.value}else s.length&&(V.set(this,e,{value:w.event.trigger(w.extend(s[0],w.Event.prototype),s.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===V.get(t,e)&&w.event.add(t,e,wt)}w.event={global:{},add:function(t,e,n,i,o){var r,s,a,l,c,u,h,d,p,f,g,m=V.get(t);if(X(t))for(n.handler&&(n=(r=n).handler,o=r.selector),o&&w.find.matchesSelector(it,o),n.guid||(n.guid=w.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(e){return void 0!==w&&w.event.triggered!==e.type?w.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(j)||[""]).length;c--;)p=g=(a=_t.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),p&&(h=w.event.special[p]||{},p=(o?h.delegateType:h.bindType)||p,h=w.event.special[p]||{},u=w.extend({type:p,origType:g,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&w.expr.match.needsContext.test(o),namespace:f.join(".")},r),(d=l[p])||((d=l[p]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(t,i,f,s)||t.addEventListener&&t.addEventListener(p,s)),h.add&&(h.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),o?d.splice(d.delegateCount++,0,u):d.push(u),w.event.global[p]=!0)},remove:function(t,e,n,i,o){var r,s,a,l,c,u,h,d,p,f,g,m=V.hasData(t)&&V.get(t);if(m&&(l=m.events)){for(c=(e=(e||"").match(j)||[""]).length;c--;)if(p=g=(a=_t.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),p){for(h=w.event.special[p]||{},d=l[p=(i?h.delegateType:h.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=r=d.length;r--;)u=d[r],!o&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(d.splice(r,1),u.selector&&d.delegateCount--,h.remove&&h.remove.call(t,u));s&&!d.length&&(h.teardown&&!1!==h.teardown.call(t,f,m.handle)||w.removeEvent(t,p,m.handle),delete l[p])}else for(p in l)w.event.remove(t,p+e[c],n,i,!0);w.isEmptyObject(l)&&V.remove(t,"handle events")}},dispatch:function(t){var e,n,i,o,r,s,a=new Array(arguments.length),l=w.event.fix(t),c=(V.get(this,"events")||Object.create(null))[l.type]||[],u=w.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(s=w.event.handlers.call(this,l,c),e=0;(o=s[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,n=0;(r=o.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==r.namespace&&!l.rnamespace.test(r.namespace)||(l.handleObj=r,l.data=r.data,void 0!==(i=((w.event.special[r.origType]||{}).handle||r.handler).apply(o.elem,a))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,i,o,r,s,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(r=[],s={},n=0;n<l;n++)void 0===s[o=(i=e[n]).selector+" "]&&(s[o]=i.needsContext?-1<w(o,this).index(c):w.find(o,this,null,[c]).length),s[o]&&r.push(i);r.length&&a.push({elem:c,handlers:r})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(w.Event.prototype,t,{enumerable:!0,configurable:!0,get:f(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[w.expando]?t:new w.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return dt.test(e.type)&&e.click&&D(e,"input")&&Tt(e,"click",wt),!1},trigger:function(t){var e=this||t;return dt.test(e.type)&&e.click&&D(e,"input")&&Tt(e,"click"),!0},_default:function(t){var e=t.target;return dt.test(e.type)&&e.click&&D(e,"input")&&V.get(e,"click")||D(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},w.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},w.Event=function(t,e){if(!(this instanceof w.Event))return new w.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?wt:xt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&w.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:xt,isPropagationStopped:xt,isImmediatePropagationStopped:xt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=wt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=wt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=wt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},w.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},w.event.addProp),w.each({focus:"focusin",blur:"focusout"},(function(t,e){w.event.special[t]={setup:function(){return Tt(this,t,Ct),!1},trigger:function(){return Tt(this,t),!0},_default:function(){return!0},delegateType:e}})),w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){w.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=t.relatedTarget,o=t.handleObj;return i&&(i===this||w.contains(this,i))||(t.type=o.origType,n=o.handler.apply(this,arguments),t.type=e),n}}})),w.fn.extend({on:function(t,e,n,i){return Et(this,t,e,n,i)},one:function(t,e,n,i){return Et(this,t,e,n,i,1)},off:function(t,e,n){var i,o;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,w(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=xt),this.each((function(){w.event.remove(this,t,n,e)}))}});var St=/<script|<style|<link/i,Dt=/checked\s*(?:[^=]|=\s*.checked.)/i,At=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function It(t,e){return D(t,"table")&&D(11!==e.nodeType?e:e.firstChild,"tr")&&w(t).children("tbody")[0]||t}function Lt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function kt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Ot(t,e){var n,i,o,r,s,a;if(1===e.nodeType){if(V.hasData(t)&&(a=V.get(t).events))for(o in V.remove(e,"handle events"),a)for(n=0,i=a[o].length;n<i;n++)w.event.add(e,o,a[o][n]);K.hasData(t)&&(r=K.access(t),s=w.extend({},r),K.set(e,s))}}function Pt(t,e,n,i){e=r(e);var o,s,a,l,c,u,h=0,d=t.length,g=d-1,m=e[0],v=f(m);if(v||1<d&&"string"==typeof m&&!p.checkClone&&Dt.test(m))return t.each((function(o){var r=t.eq(o);v&&(e[0]=m.call(this,o,r.html())),Pt(r,e,n,i)}));if(d&&(s=(o=bt(e,t[0].ownerDocument,!1,t,i)).firstChild,1===o.childNodes.length&&(o=s),s||i)){for(l=(a=w.map(mt(o,"script"),Lt)).length;h<d;h++)c=o,h!==g&&(c=w.clone(c,!0,!0),l&&w.merge(a,mt(c,"script"))),n.call(t[h],c,h);if(l)for(u=a[a.length-1].ownerDocument,w.map(a,kt),h=0;h<l;h++)c=a[h],ft.test(c.type||"")&&!V.access(c,"globalEval")&&w.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?w._evalUrl&&!c.noModule&&w._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):y(c.textContent.replace(At,""),c,u))}return t}function Nt(t,e,n){for(var i,o=e?w.filter(e,t):t,r=0;null!=(i=o[r]);r++)n||1!==i.nodeType||w.cleanData(mt(i)),i.parentNode&&(n&&ot(i)&&vt(mt(i,"script")),i.parentNode.removeChild(i));return t}w.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var i,o,r,s,a,l,c,u=t.cloneNode(!0),h=ot(t);if(!(p.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||w.isXMLDoc(t)))for(s=mt(u),i=0,o=(r=mt(t)).length;i<o;i++)a=r[i],"input"===(c=(l=s[i]).nodeName.toLowerCase())&&dt.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(n)for(r=r||mt(t),s=s||mt(u),i=0,o=r.length;i<o;i++)Ot(r[i],s[i]);else Ot(t,u);return 0<(s=mt(u,"script")).length&&vt(s,!h&&mt(t,"script")),u},cleanData:function(t){for(var e,n,i,o=w.event.special,r=0;void 0!==(n=t[r]);r++)if(X(n)){if(e=n[V.expando]){if(e.events)for(i in e.events)o[i]?w.event.remove(n,i):w.removeEvent(n,i,e.handle);n[V.expando]=void 0}n[K.expando]&&(n[K.expando]=void 0)}}}),w.fn.extend({detach:function(t){return Nt(this,t,!0)},remove:function(t){return Nt(this,t)},text:function(t){return F(this,(function(t){return void 0===t?w.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return Pt(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||It(this,t).appendChild(t)}))},prepend:function(){return Pt(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=It(this,t);e.insertBefore(t,e.firstChild)}}))},before:function(){return Pt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return Pt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(w.cleanData(mt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return w.clone(this,t,e)}))},html:function(t){return F(this,(function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!St.test(t)&&!gt[(pt.exec(t)||["",""])[1].toLowerCase()]){t=w.htmlPrefilter(t);try{for(;n<i;n++)1===(e=this[n]||{}).nodeType&&(w.cleanData(mt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return Pt(this,arguments,(function(e){var n=this.parentNode;w.inArray(this,t)<0&&(w.cleanData(mt(this)),n&&n.replaceChild(e,this))}),t)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){w.fn[t]=function(t){for(var n,i=[],o=w(t),r=o.length-1,a=0;a<=r;a++)n=a===r?this:this.clone(!0),w(o[a])[e](n),s.apply(i,n.get());return this.pushStack(i)}}));var jt=new RegExp("^("+tt+")(?!px)[a-z%]+$","i"),$t=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)},Mt=function(t,e,n){var i,o,r={};for(o in e)r[o]=t.style[o],t.style[o]=e[o];for(o in i=n.call(t),e)t.style[o]=r[o];return i},zt=new RegExp(nt.join("|"),"i");function Ht(t,e,n){var i,o,r,s,a=t.style;return(n=n||$t(t))&&(""!==(s=n.getPropertyValue(e)||n[e])||ot(t)||(s=w.style(t,e)),!p.pixelBoxStyles()&&jt.test(s)&&zt.test(e)&&(i=a.width,o=a.minWidth,r=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=o,a.maxWidth=r)),void 0!==s?s+"":s}function Rt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",it.appendChild(c).appendChild(u);var e=t.getComputedStyle(u);i="1%"!==e.top,l=12===n(e.marginLeft),u.style.right="60%",s=36===n(e.right),o=36===n(e.width),u.style.position="absolute",r=12===n(u.offsetWidth/3),it.removeChild(c),u=null}}function n(t){return Math.round(parseFloat(t))}var i,o,r,s,a,l,c=m.createElement("div"),u=m.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",p.clearCloneStyle="content-box"===u.style.backgroundClip,w.extend(p,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),r},reliableTrDimensions:function(){var e,n,i,o;return null==a&&(e=m.createElement("table"),n=m.createElement("tr"),i=m.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",n.style.cssText="border:1px solid",n.style.height="1px",i.style.height="9px",i.style.display="block",it.appendChild(e).appendChild(n).appendChild(i),o=t.getComputedStyle(n),a=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===n.offsetHeight,it.removeChild(e)),a}}))}();var qt=["Webkit","Moz","ms"],Ft=m.createElement("div").style,Wt={};function Bt(t){return w.cssProps[t]||Wt[t]||(t in Ft?t:Wt[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=qt.length;n--;)if((t=qt[n]+e)in Ft)return t}(t)||t)}var Ut=/^(none|table(?!-c[ea]).+)/,Yt=/^--/,Xt={position:"absolute",visibility:"hidden",display:"block"},Qt={letterSpacing:"0",fontWeight:"400"};function Vt(t,e,n){var i=et.exec(e);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):e}function Kt(t,e,n,i,o,r){var s="width"===e?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=w.css(t,n+nt[s],!0,o)),i?("content"===n&&(l-=w.css(t,"padding"+nt[s],!0,o)),"margin"!==n&&(l-=w.css(t,"border"+nt[s]+"Width",!0,o))):(l+=w.css(t,"padding"+nt[s],!0,o),"padding"!==n?l+=w.css(t,"border"+nt[s]+"Width",!0,o):a+=w.css(t,"border"+nt[s]+"Width",!0,o));return!i&&0<=r&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-r-l-a-.5))||0),l}function Zt(t,e,n){var i=$t(t),o=(!p.boxSizingReliable()||n)&&"border-box"===w.css(t,"boxSizing",!1,i),r=o,s=Ht(t,e,i),a="offset"+e[0].toUpperCase()+e.slice(1);if(jt.test(s)){if(!n)return s;s="auto"}return(!p.boxSizingReliable()&&o||!p.reliableTrDimensions()&&D(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===w.css(t,"display",!1,i))&&t.getClientRects().length&&(o="border-box"===w.css(t,"boxSizing",!1,i),(r=a in t)&&(s=t[a])),(s=parseFloat(s)||0)+Kt(t,e,n||(o?"border":"content"),r,i,s)+"px"}function Gt(t,e,n,i,o){return new Gt.prototype.init(t,e,n,i,o)}w.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=Ht(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,r,s,a=Y(e),l=Yt.test(e),c=t.style;if(l||(e=Bt(a)),s=w.cssHooks[e]||w.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(o=s.get(t,!1,i))?o:c[e];"string"==(r=typeof n)&&(o=et.exec(n))&&o[1]&&(n=at(t,e,o),r="number"),null!=n&&n==n&&("number"!==r||l||(n+=o&&o[3]||(w.cssNumber[a]?"":"px")),p.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,i))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,i){var o,r,s,a=Y(e);return Yt.test(e)||(e=Bt(a)),(s=w.cssHooks[e]||w.cssHooks[a])&&"get"in s&&(o=s.get(t,!0,n)),void 0===o&&(o=Ht(t,e,i)),"normal"===o&&e in Qt&&(o=Qt[e]),""===n||n?(r=parseFloat(o),!0===n||isFinite(r)?r||0:o):o}}),w.each(["height","width"],(function(t,e){w.cssHooks[e]={get:function(t,n,i){if(n)return!Ut.test(w.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?Zt(t,e,i):Mt(t,Xt,(function(){return Zt(t,e,i)}))},set:function(t,n,i){var o,r=$t(t),s=!p.scrollboxSize()&&"absolute"===r.position,a=(s||i)&&"border-box"===w.css(t,"boxSizing",!1,r),l=i?Kt(t,e,i,a,r):0;return a&&s&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(r[e])-Kt(t,e,"border",!1,r)-.5)),l&&(o=et.exec(n))&&"px"!==(o[3]||"px")&&(t.style[e]=n,n=w.css(t,e)),Vt(0,n,l)}}})),w.cssHooks.marginLeft=Rt(p.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(Ht(t,"marginLeft"))||t.getBoundingClientRect().left-Mt(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),w.each({margin:"",padding:"",border:"Width"},(function(t,e){w.cssHooks[t+e]={expand:function(n){for(var i=0,o={},r="string"==typeof n?n.split(" "):[n];i<4;i++)o[t+nt[i]+e]=r[i]||r[i-2]||r[0];return o}},"margin"!==t&&(w.cssHooks[t+e].set=Vt)})),w.fn.extend({css:function(t,e){return F(this,(function(t,e,n){var i,o,r={},s=0;if(Array.isArray(e)){for(i=$t(t),o=e.length;s<o;s++)r[e[s]]=w.css(t,e[s],!1,i);return r}return void 0!==n?w.style(t,e,n):w.css(t,e)}),t,e,1<arguments.length)}}),((w.Tween=Gt).prototype={constructor:Gt,init:function(t,e,n,i,o,r){this.elem=t,this.prop=n,this.easing=o||w.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=r||(w.cssNumber[n]?"":"px")},cur:function(){var t=Gt.propHooks[this.prop];return t&&t.get?t.get(this):Gt.propHooks._default.get(this)},run:function(t){var e,n=Gt.propHooks[this.prop];return this.options.duration?this.pos=e=w.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Gt.propHooks._default.set(this),this}}).init.prototype=Gt.prototype,(Gt.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=w.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){w.fx.step[t.prop]?w.fx.step[t.prop](t):1!==t.elem.nodeType||!w.cssHooks[t.prop]&&null==t.elem.style[Bt(t.prop)]?t.elem[t.prop]=t.now:w.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=Gt.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},w.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},w.fx=Gt.prototype.init,w.fx.step={};var Jt,te,ee,ne,ie=/^(?:toggle|show|hide)$/,oe=/queueHooks$/;function re(){te&&(!1===m.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(re):t.setTimeout(re,w.fx.interval),w.fx.tick())}function se(){return t.setTimeout((function(){Jt=void 0})),Jt=Date.now()}function ae(t,e){var n,i=0,o={height:t};for(e=e?1:0;i<4;i+=2-e)o["margin"+(n=nt[i])]=o["padding"+n]=t;return e&&(o.opacity=o.width=t),o}function le(t,e,n){for(var i,o=(ce.tweeners[e]||[]).concat(ce.tweeners["*"]),r=0,s=o.length;r<s;r++)if(i=o[r].call(n,e,t))return i}function ce(t,e,n){var i,o,r=0,s=ce.prefilters.length,a=w.Deferred().always((function(){delete l.elem})),l=function(){if(o)return!1;for(var e=Jt||se(),n=Math.max(0,c.startTime+c.duration-e),i=1-(n/c.duration||0),r=0,s=c.tweens.length;r<s;r++)c.tweens[r].run(i);return a.notifyWith(t,[c,i,n]),i<1&&s?n:(s||a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},n),originalProperties:e,originalOptions:n,startTime:Jt||se(),duration:n.duration,tweens:[],createTween:function(e,n){var i=w.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(o)return this;for(o=!0;n<i;n++)c.tweens[n].run(1);return e?(a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c,e])):a.rejectWith(t,[c,e]),this}}),u=c.props;for(function(t,e){var n,i,o,r,s;for(n in t)if(o=e[i=Y(n)],r=t[n],Array.isArray(r)&&(o=r[1],r=t[n]=r[0]),n!==i&&(t[i]=r,delete t[n]),(s=w.cssHooks[i])&&"expand"in s)for(n in r=s.expand(r),delete t[i],r)n in t||(t[n]=r[n],e[n]=o);else e[i]=o}(u,c.opts.specialEasing);r<s;r++)if(i=ce.prefilters[r].call(c,t,u,c.opts))return f(i.stop)&&(w._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return w.map(u,le,c),f(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),w.fx.timer(w.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}w.Animation=w.extend(ce,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return at(n.elem,t,et.exec(e),n),n}]},tweener:function(t,e){f(t)?(e=t,t=["*"]):t=t.match(j);for(var n,i=0,o=t.length;i<o;i++)n=t[i],ce.tweeners[n]=ce.tweeners[n]||[],ce.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var i,o,r,s,a,l,c,u,h="width"in e||"height"in e,d=this,p={},f=t.style,g=t.nodeType&&st(t),m=V.get(t,"fxshow");for(i in n.queue||(null==(s=w._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always((function(){d.always((function(){s.unqueued--,w.queue(t,"fx").length||s.empty.fire()}))}))),e)if(o=e[i],ie.test(o)){if(delete e[i],r=r||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[i])continue;g=!0}p[i]=m&&m[i]||w.style(t,i)}if((l=!w.isEmptyObject(e))||!w.isEmptyObject(p))for(i in h&&1===t.nodeType&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=m&&m.display)&&(c=V.get(t,"display")),"none"===(u=w.css(t,"display"))&&(c?u=c:(ct([t],!0),c=t.style.display||c,u=w.css(t,"display"),ct([t]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===w.css(t,"float")&&(l||(d.done((function(){f.display=c})),null==c&&(u=f.display,c="none"===u?"":u)),f.display="inline-block")),n.overflow&&(f.overflow="hidden",d.always((function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}))),l=!1,p)l||(m?"hidden"in m&&(g=m.hidden):m=V.access(t,"fxshow",{display:c}),r&&(m.hidden=!g),g&&ct([t],!0),d.done((function(){for(i in g||ct([t]),V.remove(t,"fxshow"),p)w.style(t,i,p[i])}))),l=le(g?m[i]:0,i,d),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?ce.prefilters.unshift(t):ce.prefilters.push(t)}}),w.speed=function(t,e,n){var i=t&&"object"==typeof t?w.extend({},t):{complete:n||!n&&e||f(t)&&t,duration:t,easing:n&&e||e&&!f(e)&&e};return w.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in w.fx.speeds?i.duration=w.fx.speeds[i.duration]:i.duration=w.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){f(i.old)&&i.old.call(this),i.queue&&w.dequeue(this,i.queue)},i},w.fn.extend({fadeTo:function(t,e,n,i){return this.filter(st).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var o=w.isEmptyObject(t),r=w.speed(e,n,i),s=function(){var e=ce(this,w.extend({},t),r);(o||V.get(this,"finish"))&&e.stop(!0)};return s.finish=s,o||!1===r.queue?this.each(s):this.queue(r.queue,s)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,o=null!=t&&t+"queueHooks",r=w.timers,s=V.get(this);if(o)s[o]&&s[o].stop&&i(s[o]);else for(o in s)s[o]&&s[o].stop&&oe.test(o)&&i(s[o]);for(o=r.length;o--;)r[o].elem!==this||null!=t&&r[o].queue!==t||(r[o].anim.stop(n),e=!1,r.splice(o,1));!e&&n||w.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,n=V.get(this),i=n[t+"queue"],o=n[t+"queueHooks"],r=w.timers,s=i?i.length:0;for(n.finish=!0,w.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=r.length;e--;)r[e].elem===this&&r[e].queue===t&&(r[e].anim.stop(!0),r.splice(e,1));for(e=0;e<s;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish}))}}),w.each(["toggle","show","hide"],(function(t,e){var n=w.fn[e];w.fn[e]=function(t,i,o){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(ae(e,!0),t,i,o)}})),w.each({slideDown:ae("show"),slideUp:ae("hide"),slideToggle:ae("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){w.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}})),w.timers=[],w.fx.tick=function(){var t,e=0,n=w.timers;for(Jt=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||w.fx.stop(),Jt=void 0},w.fx.timer=function(t){w.timers.push(t),w.fx.start()},w.fx.interval=13,w.fx.start=function(){te||(te=!0,re())},w.fx.stop=function(){te=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(e,n){return e=w.fx&&w.fx.speeds[e]||e,n=n||"fx",this.queue(n,(function(n,i){var o=t.setTimeout(n,e);i.stop=function(){t.clearTimeout(o)}}))},ee=m.createElement("input"),ne=m.createElement("select").appendChild(m.createElement("option")),ee.type="checkbox",p.checkOn=""!==ee.value,p.optSelected=ne.selected,(ee=m.createElement("input")).value="t",ee.type="radio",p.radioValue="t"===ee.value;var ue,he=w.expr.attrHandle;w.fn.extend({attr:function(t,e){return F(this,w.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each((function(){w.removeAttr(this,t)}))}}),w.extend({attr:function(t,e,n){var i,o,r=t.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===t.getAttribute?w.prop(t,e,n):(1===r&&w.isXMLDoc(t)||(o=w.attrHooks[e.toLowerCase()]||(w.expr.match.bool.test(e)?ue:void 0)),void 0!==n?null===n?void w.removeAttr(t,e):o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:(t.setAttribute(e,n+""),n):o&&"get"in o&&null!==(i=o.get(t,e))?i:null==(i=w.find.attr(t,e))?void 0:i)},attrHooks:{type:{set:function(t,e){if(!p.radioValue&&"radio"===e&&D(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i=0,o=e&&e.match(j);if(o&&1===t.nodeType)for(;n=o[i++];)t.removeAttribute(n)}}),ue={set:function(t,e,n){return!1===e?w.removeAttr(t,n):t.setAttribute(n,n),n}},w.each(w.expr.match.bool.source.match(/\w+/g),(function(t,e){var n=he[e]||w.find.attr;he[e]=function(t,e,i){var o,r,s=e.toLowerCase();return i||(r=he[s],he[s]=o,o=null!=n(t,e,i)?s:null,he[s]=r),o}}));var de=/^(?:input|select|textarea|button)$/i,pe=/^(?:a|area)$/i;function fe(t){return(t.match(j)||[]).join(" ")}function ge(t){return t.getAttribute&&t.getAttribute("class")||""}function me(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(j)||[]}w.fn.extend({prop:function(t,e){return F(this,w.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each((function(){delete this[w.propFix[t]||t]}))}}),w.extend({prop:function(t,e,n){var i,o,r=t.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&w.isXMLDoc(t)||(e=w.propFix[e]||e,o=w.propHooks[e]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:t[e]=n:o&&"get"in o&&null!==(i=o.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=w.find.attr(t,"tabindex");return e?parseInt(e,10):de.test(t.nodeName)||pe.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),p.optSelected||(w.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){w.propFix[this.toLowerCase()]=this})),w.fn.extend({addClass:function(t){var e,n,i,o,r,s,a,l=0;if(f(t))return this.each((function(e){w(this).addClass(t.call(this,e,ge(this)))}));if((e=me(t)).length)for(;n=this[l++];)if(o=ge(n),i=1===n.nodeType&&" "+fe(o)+" "){for(s=0;r=e[s++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");o!==(a=fe(i))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,i,o,r,s,a,l=0;if(f(t))return this.each((function(e){w(this).removeClass(t.call(this,e,ge(this)))}));if(!arguments.length)return this.attr("class","");if((e=me(t)).length)for(;n=this[l++];)if(o=ge(n),i=1===n.nodeType&&" "+fe(o)+" "){for(s=0;r=e[s++];)for(;-1<i.indexOf(" "+r+" ");)i=i.replace(" "+r+" "," ");o!==(a=fe(i))&&n.setAttribute("class",a)}return this},toggleClass:function(t,e){var n=typeof t,i="string"===n||Array.isArray(t);return"boolean"==typeof e&&i?e?this.addClass(t):this.removeClass(t):f(t)?this.each((function(n){w(this).toggleClass(t.call(this,n,ge(this),e),e)})):this.each((function(){var e,o,r,s;if(i)for(o=0,r=w(this),s=me(t);e=s[o++];)r.hasClass(e)?r.removeClass(e):r.addClass(e);else void 0!==t&&"boolean"!==n||((e=ge(this))&&V.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===t?"":V.get(this,"__className__")||""))}))},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+fe(ge(n))+" ").indexOf(e))return!0;return!1}});var ve=/\r/g;w.fn.extend({val:function(t){var e,n,i,o=this[0];return arguments.length?(i=f(t),this.each((function(n){var o;1===this.nodeType&&(null==(o=i?t.call(this,n,w(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=w.map(o,(function(t){return null==t?"":t+""}))),(e=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))}))):o?(e=w.valHooks[o.type]||w.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(ve,""):null==n?"":n:void 0}}),w.extend({valHooks:{option:{get:function(t){var e=w.find.attr(t,"value");return null!=e?e:fe(w.text(t))}},select:{get:function(t){var e,n,i,o=t.options,r=t.selectedIndex,s="select-one"===t.type,a=s?null:[],l=s?r+1:o.length;for(i=r<0?l:s?r:0;i<l;i++)if(((n=o[i]).selected||i===r)&&!n.disabled&&(!n.parentNode.disabled||!D(n.parentNode,"optgroup"))){if(e=w(n).val(),s)return e;a.push(e)}return a},set:function(t,e){for(var n,i,o=t.options,r=w.makeArray(e),s=o.length;s--;)((i=o[s]).selected=-1<w.inArray(w.valHooks.option.get(i),r))&&(n=!0);return n||(t.selectedIndex=-1),r}}}}),w.each(["radio","checkbox"],(function(){w.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<w.inArray(w(t).val(),e)}},p.checkOn||(w.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})})),p.focusin="onfocusin"in t;var ye=/^(?:focusinfocus|focusoutblur)$/,be=function(t){t.stopPropagation()};w.extend(w.event,{trigger:function(e,n,i,o){var r,s,a,l,c,h,d,p,v=[i||m],y=u.call(e,"type")?e.type:e,b=u.call(e,"namespace")?e.namespace.split("."):[];if(s=p=a=i=i||m,3!==i.nodeType&&8!==i.nodeType&&!ye.test(y+w.event.triggered)&&(-1<y.indexOf(".")&&(y=(b=y.split(".")).shift(),b.sort()),c=y.indexOf(":")<0&&"on"+y,(e=e[w.expando]?e:new w.Event(y,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=b.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=null==n?[e]:w.makeArray(n,[e]),d=w.event.special[y]||{},o||!d.trigger||!1!==d.trigger.apply(i,n))){if(!o&&!d.noBubble&&!g(i)){for(l=d.delegateType||y,ye.test(l+y)||(s=s.parentNode);s;s=s.parentNode)v.push(s),a=s;a===(i.ownerDocument||m)&&v.push(a.defaultView||a.parentWindow||t)}for(r=0;(s=v[r++])&&!e.isPropagationStopped();)p=s,e.type=1<r?l:d.bindType||y,(h=(V.get(s,"events")||Object.create(null))[e.type]&&V.get(s,"handle"))&&h.apply(s,n),(h=c&&s[c])&&h.apply&&X(s)&&(e.result=h.apply(s,n),!1===e.result&&e.preventDefault());return e.type=y,o||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(v.pop(),n)||!X(i)||c&&f(i[y])&&!g(i)&&((a=i[c])&&(i[c]=null),w.event.triggered=y,e.isPropagationStopped()&&p.addEventListener(y,be),i[y](),e.isPropagationStopped()&&p.removeEventListener(y,be),w.event.triggered=void 0,a&&(i[c]=a)),e.result}},simulate:function(t,e,n){var i=w.extend(new w.Event,n,{type:t,isSimulated:!0});w.event.trigger(i,null,e)}}),w.fn.extend({trigger:function(t,e){return this.each((function(){w.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var n=this[0];if(n)return w.event.trigger(t,e,n,!0)}}),p.focusin||w.each({focus:"focusin",blur:"focusout"},(function(t,e){var n=function(t){w.event.simulate(e,t.target,w.event.fix(t))};w.event.special[e]={setup:function(){var i=this.ownerDocument||this.document||this,o=V.access(i,e);o||i.addEventListener(t,n,!0),V.access(i,e,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,o=V.access(i,e)-1;o?V.access(i,e,o):(i.removeEventListener(t,n,!0),V.remove(i,e))}}}));var _e=t.location,we={guid:Date.now()},xe=/\?/;w.parseXML=function(e){var n,i;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){}return i=n&&n.getElementsByTagName("parsererror")[0],n&&!i||w.error("Invalid XML: "+(i?w.map(i.childNodes,(function(t){return t.textContent})).join("\n"):e)),n};var Ce=/\[\]$/,Ee=/\r?\n/g,Te=/^(?:submit|button|image|reset|file)$/i,Se=/^(?:input|select|textarea|keygen)/i;function De(t,e,n,i){var o;if(Array.isArray(e))w.each(e,(function(e,o){n||Ce.test(t)?i(t,o):De(t+"["+("object"==typeof o&&null!=o?e:"")+"]",o,n,i)}));else if(n||"object"!==b(e))i(t,e);else for(o in e)De(t+"["+o+"]",e[o],n,i)}w.param=function(t,e){var n,i=[],o=function(t,e){var n=f(e)?e():e;i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!w.isPlainObject(t))w.each(t,(function(){o(this.name,this.value)}));else for(n in t)De(n,t[n],e,o);return i.join("&")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=w.prop(this,"elements");return t?w.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!w(this).is(":disabled")&&Se.test(this.nodeName)&&!Te.test(t)&&(this.checked||!dt.test(t))})).map((function(t,e){var n=w(this).val();return null==n?null:Array.isArray(n)?w.map(n,(function(t){return{name:e.name,value:t.replace(Ee,"\r\n")}})):{name:e.name,value:n.replace(Ee,"\r\n")}})).get()}});var Ae=/%20/g,Ie=/#.*$/,Le=/([?&])_=[^&]*/,ke=/^(.*?):[ \t]*([^\r\n]*)$/gm,Oe=/^(?:GET|HEAD)$/,Pe=/^\/\//,Ne={},je={},$e="*/".concat("*"),Me=m.createElement("a");function ze(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,o=0,r=e.toLowerCase().match(j)||[];if(f(n))for(;i=r[o++];)"+"===i[0]?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function He(t,e,n,i){var o={},r=t===je;function s(a){var l;return o[a]=!0,w.each(t[a]||[],(function(t,a){var c=a(e,n,i);return"string"!=typeof c||r||o[c]?r?!(l=c):void 0:(e.dataTypes.unshift(c),s(c),!1)})),l}return s(e.dataTypes[0])||!o["*"]&&s("*")}function Re(t,e){var n,i,o=w.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((o[n]?t:i||(i={}))[n]=e[n]);return i&&w.extend(!0,t,i),t}Me.href=_e.href,w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:_e.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(_e.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":$e,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Re(Re(t,w.ajaxSettings),e):Re(w.ajaxSettings,t)},ajaxPrefilter:ze(Ne),ajaxTransport:ze(je),ajax:function(e,n){"object"==typeof e&&(n=e,e=void 0),n=n||{};var i,o,r,s,a,l,c,u,h,d,p=w.ajaxSetup({},n),f=p.context||p,g=p.context&&(f.nodeType||f.jquery)?w(f):w.event,v=w.Deferred(),y=w.Callbacks("once memory"),b=p.statusCode||{},_={},x={},C="canceled",E={readyState:0,getResponseHeader:function(t){var e;if(c){if(!s)for(s={};e=ke.exec(r);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?r:null},setRequestHeader:function(t,e){return null==c&&(t=x[t.toLowerCase()]=x[t.toLowerCase()]||t,_[t]=e),this},overrideMimeType:function(t){return null==c&&(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)E.always(t[E.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||C;return i&&i.abort(e),T(0,e),this}};if(v.promise(E),p.url=((e||p.url||_e.href)+"").replace(Pe,_e.protocol+"//"),p.type=n.method||n.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(j)||[""],null==p.crossDomain){l=m.createElement("a");try{l.href=p.url,l.href=l.href,p.crossDomain=Me.protocol+"//"+Me.host!=l.protocol+"//"+l.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=w.param(p.data,p.traditional)),He(Ne,p,n,E),c)return E;for(h in(u=w.event&&p.global)&&0==w.active++&&w.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Oe.test(p.type),o=p.url.replace(Ie,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Ae,"+")):(d=p.url.slice(o.length),p.data&&(p.processData||"string"==typeof p.data)&&(o+=(xe.test(o)?"&":"?")+p.data,delete p.data),!1===p.cache&&(o=o.replace(Le,"$1"),d=(xe.test(o)?"&":"?")+"_="+we.guid+++d),p.url=o+d),p.ifModified&&(w.lastModified[o]&&E.setRequestHeader("If-Modified-Since",w.lastModified[o]),w.etag[o]&&E.setRequestHeader("If-None-Match",w.etag[o])),(p.data&&p.hasContent&&!1!==p.contentType||n.contentType)&&E.setRequestHeader("Content-Type",p.contentType),E.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+$e+"; q=0.01":""):p.accepts["*"]),p.headers)E.setRequestHeader(h,p.headers[h]);if(p.beforeSend&&(!1===p.beforeSend.call(f,E,p)||c))return E.abort();if(C="abort",y.add(p.complete),E.done(p.success),E.fail(p.error),i=He(je,p,n,E)){if(E.readyState=1,u&&g.trigger("ajaxSend",[E,p]),c)return E;p.async&&0<p.timeout&&(a=t.setTimeout((function(){E.abort("timeout")}),p.timeout));try{c=!1,i.send(_,T)}catch(e){if(c)throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,n,s,l){var h,d,m,_,x,C=n;c||(c=!0,a&&t.clearTimeout(a),i=void 0,r=l||"",E.readyState=0<e?4:0,h=200<=e&&e<300||304===e,s&&(_=function(t,e,n){for(var i,o,r,s,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(o in a)if(a[o]&&a[o].test(i)){l.unshift(o);break}if(l[0]in n)r=l[0];else{for(o in n){if(!l[0]||t.converters[o+" "+l[0]]){r=o;break}s||(s=o)}r=r||s}if(r)return r!==l[0]&&l.unshift(r),n[r]}(p,E,s)),!h&&-1<w.inArray("script",p.dataTypes)&&w.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),_=function(t,e,n,i){var o,r,s,a,l,c={},u=t.dataTypes.slice();if(u[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(r=u.shift();r;)if(t.responseFields[r]&&(n[t.responseFields[r]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=r,r=u.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(s=c[l+" "+r]||c["* "+r]))for(o in c)if((a=o.split(" "))[1]===r&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[o]:!0!==c[o]&&(r=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+l+" to "+r}}}return{state:"success",data:e}}(p,_,E,h),h?(p.ifModified&&((x=E.getResponseHeader("Last-Modified"))&&(w.lastModified[o]=x),(x=E.getResponseHeader("etag"))&&(w.etag[o]=x)),204===e||"HEAD"===p.type?C="nocontent":304===e?C="notmodified":(C=_.state,d=_.data,h=!(m=_.error))):(m=C,!e&&C||(C="error",e<0&&(e=0))),E.status=e,E.statusText=(n||C)+"",h?v.resolveWith(f,[d,C,E]):v.rejectWith(f,[E,C,m]),E.statusCode(b),b=void 0,u&&g.trigger(h?"ajaxSuccess":"ajaxError",[E,p,h?d:m]),y.fireWith(f,[E,C]),u&&(g.trigger("ajaxComplete",[E,p]),--w.active||w.event.trigger("ajaxStop")))}return E},getJSON:function(t,e,n){return w.get(t,e,n,"json")},getScript:function(t,e){return w.get(t,void 0,e,"script")}}),w.each(["get","post"],(function(t,e){w[e]=function(t,n,i,o){return f(n)&&(o=o||i,i=n,n=void 0),w.ajax(w.extend({url:t,type:e,dataType:o,data:n,success:i},w.isPlainObject(t)&&t))}})),w.ajaxPrefilter((function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),w._evalUrl=function(t,e,n){return w.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){w.globalEval(t,e,n)}})},w.fn.extend({wrapAll:function(t){var e;return this[0]&&(f(t)&&(t=t.call(this[0])),e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return f(t)?this.each((function(e){w(this).wrapInner(t.call(this,e))})):this.each((function(){var e=w(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)}))},wrap:function(t){var e=f(t);return this.each((function(n){w(this).wrapAll(e?t.call(this,n):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){w(this).replaceWith(this.childNodes)})),this}}),w.expr.pseudos.hidden=function(t){return!w.expr.pseudos.visible(t)},w.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},w.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var qe={0:200,1223:204},Fe=w.ajaxSettings.xhr();p.cors=!!Fe&&"withCredentials"in Fe,p.ajax=Fe=!!Fe,w.ajaxTransport((function(e){var n,i;if(p.cors||Fe&&!e.crossDomain)return{send:function(o,r){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)a.setRequestHeader(s,o[s]);n=function(t){return function(){n&&(n=i=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?r(0,"error"):r(a.status,a.statusText):r(qe[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),i=a.onerror=a.ontimeout=n("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout((function(){n&&i()}))},n=n("abort");try{a.send(e.hasContent&&e.data||null)}catch(o){if(n)throw o}},abort:function(){n&&n()}}})),w.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return w.globalEval(t),t}}}),w.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),w.ajaxTransport("script",(function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(i,o){e=w("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&o("error"===t.type?404:200,t.type)}),m.head.appendChild(e[0])},abort:function(){n&&n()}}}));var We,Be=[],Ue=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Be.pop()||w.expando+"_"+we.guid++;return this[t]=!0,t}}),w.ajaxPrefilter("json jsonp",(function(e,n,i){var o,r,s,a=!1!==e.jsonp&&(Ue.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ue.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=f(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Ue,"$1"+o):!1!==e.jsonp&&(e.url+=(xe.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return s||w.error(o+" was not called"),s[0]},e.dataTypes[0]="json",r=t[o],t[o]=function(){s=arguments},i.always((function(){void 0===r?w(t).removeProp(o):t[o]=r,e[o]&&(e.jsonpCallback=n.jsonpCallback,Be.push(o)),s&&f(r)&&r(s[0]),s=r=void 0})),"script"})),p.createHTMLDocument=((We=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===We.childNodes.length),w.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(p.createHTMLDocument?((i=(e=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,e.head.appendChild(i)):e=m),r=!n&&[],(o=A.exec(t))?[e.createElement(o[1])]:(o=bt([t],e,r),r&&r.length&&w(r).remove(),w.merge([],o.childNodes)));var i,o,r},w.fn.load=function(t,e,n){var i,o,r,s=this,a=t.indexOf(" ");return-1<a&&(i=fe(t.slice(a)),t=t.slice(0,a)),f(e)?(n=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<s.length&&w.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done((function(t){r=arguments,s.html(i?w("<div>").append(w.parseHTML(t)).find(i):t)})).always(n&&function(t,e){s.each((function(){n.apply(this,r||[t.responseText,e,t])}))}),this},w.expr.pseudos.animated=function(t){return w.grep(w.timers,(function(e){return t===e.elem})).length},w.offset={setOffset:function(t,e,n){var i,o,r,s,a,l,c=w.css(t,"position"),u=w(t),h={};"static"===c&&(t.style.position="relative"),a=u.offset(),r=w.css(t,"top"),l=w.css(t,"left"),("absolute"===c||"fixed"===c)&&-1<(r+l).indexOf("auto")?(s=(i=u.position()).top,o=i.left):(s=parseFloat(r)||0,o=parseFloat(l)||0),f(e)&&(e=e.call(t,n,w.extend({},a))),null!=e.top&&(h.top=e.top-a.top+s),null!=e.left&&(h.left=e.left-a.left+o),"using"in e?e.using.call(t,h):u.css(h)}},w.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each((function(e){w.offset.setOffset(this,t,e)}));var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,i=this[0],o={top:0,left:0};if("fixed"===w.css(i,"position"))e=i.getBoundingClientRect();else{for(e=this.offset(),n=i.ownerDocument,t=i.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===w.css(t,"position");)t=t.parentNode;t&&t!==i&&1===t.nodeType&&((o=w(t).offset()).top+=w.css(t,"borderTopWidth",!0),o.left+=w.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-w.css(i,"marginTop",!0),left:e.left-o.left-w.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===w.css(t,"position");)t=t.offsetParent;return t||it}))}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var n="pageYOffset"===e;w.fn[t]=function(i){return F(this,(function(t,i,o){var r;if(g(t)?r=t:9===t.nodeType&&(r=t.defaultView),void 0===o)return r?r[e]:t[i];r?r.scrollTo(n?r.pageXOffset:o,n?o:r.pageYOffset):t[i]=o}),t,i,arguments.length)}})),w.each(["top","left"],(function(t,e){w.cssHooks[e]=Rt(p.pixelPosition,(function(t,n){if(n)return n=Ht(t,e),jt.test(n)?w(t).position()[e]+"px":n}))})),w.each({Height:"height",Width:"width"},(function(t,e){w.each({padding:"inner"+t,content:e,"":"outer"+t},(function(n,i){w.fn[i]=function(o,r){var s=arguments.length&&(n||"boolean"!=typeof o),a=n||(!0===o||!0===r?"margin":"border");return F(this,(function(e,n,o){var r;return g(e)?0===i.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+t],r["scroll"+t],e.body["offset"+t],r["offset"+t],r["client"+t])):void 0===o?w.css(e,n,a):w.style(e,n,o,a)}),e,s?o:void 0,s)}}))})),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){w.fn[e]=function(t){return this.on(e,t)}})),w.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),w.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){w.fn[e]=function(t,n){return 0<arguments.length?this.on(e,null,t,n):this.trigger(e)}}));var Ye=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;w.proxy=function(t,e){var n,i,r;if("string"==typeof e&&(n=t[e],e=t,t=n),f(t))return i=o.call(arguments,2),(r=function(){return t.apply(e||this,i.concat(o.call(arguments)))}).guid=t.guid=t.guid||w.guid++,r},w.holdReady=function(t){t?w.readyWait++:w.ready(!0)},w.isArray=Array.isArray,w.parseJSON=JSON.parse,w.nodeName=D,w.isFunction=f,w.isWindow=g,w.camelCase=Y,w.type=b,w.now=Date.now,w.isNumeric=function(t){var e=w.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},w.trim=function(t){return null==t?"":(t+"").replace(Ye,"")},"function"==typeof define&&define.amd&&define("jquery",[],(function(){return w}));var Xe=t.jQuery,Qe=t.$;return w.noConflict=function(e){return t.$===w&&(t.$=Qe),e&&t.jQuery===w&&(t.jQuery=Xe),w},void 0===e&&(t.jQuery=t.$=w),w})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Popper=e()}(this,(function(){"use strict";function t(t){return t&&"[object Function]"==={}.toString.call(t)}function e(t,e){if(1!==t.nodeType)return[];var n=getComputedStyle(t,null);return e?n[e]:n}function n(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function i(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var o=e(t),r=o.overflow,s=o.overflowX,a=o.overflowY;return/(auto|scroll|overlay)/.test(r+a+s)?t:i(n(t))}function o(t){if(!t)return document.documentElement;for(var n=K(10)?document.body:null,i=t.offsetParent;i===n&&t.nextElementSibling;)i=(t=t.nextElementSibling).offsetParent;var r=i&&i.nodeName;return r&&"BODY"!==r&&"HTML"!==r?-1!==["TD","TABLE"].indexOf(i.nodeName)&&"static"===e(i,"position")?o(i):i:t?t.ownerDocument.documentElement:document.documentElement}function r(t){return null===t.parentNode?t:r(t.parentNode)}function s(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?t:e,a=n?e:t,l=document.createRange();l.setStart(i,0),l.setEnd(a,0);var c=l.commonAncestorContainer;if(t!==c&&e!==c||i.contains(a))return function(t){var e=t.nodeName;return"BODY"!==e&&("HTML"===e||o(t.firstElementChild)===t)}(c)?c:o(c);var u=r(t);return u.host?s(u.host,e):s(t,r(e).host)}function a(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"top",n="top"===e?"scrollTop":"scrollLeft",i=t.nodeName;if("BODY"===i||"HTML"===i){var o=t.ownerDocument.documentElement,r=t.ownerDocument.scrollingElement||o;return r[n]}return t[n]}function l(t,e){var n=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=a(e,"top"),o=a(e,"left"),r=n?-1:1;return t.top+=i*r,t.bottom+=i*r,t.left+=o*r,t.right+=o*r,t}function c(t,e){var n="x"===e?"Left":"Top",i="Left"==n?"Right":"Bottom";return parseFloat(t["border"+n+"Width"],10)+parseFloat(t["border"+i+"Width"],10)}function u(t,e,n,i){return W(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],K(10)?n["offset"+t]+i["margin"+("Height"===t?"Top":"Left")]+i["margin"+("Height"===t?"Bottom":"Right")]:0)}function h(){var t=document.body,e=document.documentElement,n=K(10)&&getComputedStyle(e);return{height:u("Height",t,e,n),width:u("Width",t,e,n)}}function d(t){return tt({},t,{right:t.left+t.width,bottom:t.top+t.height})}function p(t){var n={};try{if(K(10)){n=t.getBoundingClientRect();var i=a(t,"top"),o=a(t,"left");n.top+=i,n.left+=o,n.bottom+=i,n.right+=o}else n=t.getBoundingClientRect()}catch(t){}var r={left:n.left,top:n.top,width:n.right-n.left,height:n.bottom-n.top},s="HTML"===t.nodeName?h():{},l=s.width||t.clientWidth||r.right-r.left,u=s.height||t.clientHeight||r.bottom-r.top,p=t.offsetWidth-l,f=t.offsetHeight-u;if(p||f){var g=e(t);p-=c(g,"x"),f-=c(g,"y"),r.width-=p,r.height-=f}return d(r)}function f(t,n){var o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],r=K(10),s="HTML"===n.nodeName,a=p(t),c=p(n),u=i(t),h=e(n),f=parseFloat(h.borderTopWidth,10),g=parseFloat(h.borderLeftWidth,10);o&&"HTML"===n.nodeName&&(c.top=W(c.top,0),c.left=W(c.left,0));var m=d({top:a.top-c.top-f,left:a.left-c.left-g,width:a.width,height:a.height});if(m.marginTop=0,m.marginLeft=0,!r&&s){var v=parseFloat(h.marginTop,10),y=parseFloat(h.marginLeft,10);m.top-=f-v,m.bottom-=f-v,m.left-=g-y,m.right-=g-y,m.marginTop=v,m.marginLeft=y}return(r&&!o?n.contains(u):n===u&&"BODY"!==u.nodeName)&&(m=l(m,n)),m}function g(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=t.ownerDocument.documentElement,i=f(t,n),o=W(n.clientWidth,window.innerWidth||0),r=W(n.clientHeight,window.innerHeight||0),s=e?0:a(n),l=e?0:a(n,"left"),c={top:s-i.top+i.marginTop,left:l-i.left+i.marginLeft,width:o,height:r};return d(c)}function m(t){var i=t.nodeName;return"BODY"!==i&&"HTML"!==i&&("fixed"===e(t,"position")||m(n(t)))}function v(t){if(!t||!t.parentElement||K())return document.documentElement;for(var n=t.parentElement;n&&"none"===e(n,"transform");)n=n.parentElement;return n||document.documentElement}function y(t,e,o,r){var a=4<arguments.length&&void 0!==arguments[4]&&arguments[4],l={top:0,left:0},c=a?v(t):s(t,e);if("viewport"===r)l=g(c,a);else{var u;"scrollParent"===r?"BODY"===(u=i(n(e))).nodeName&&(u=t.ownerDocument.documentElement):u="window"===r?t.ownerDocument.documentElement:r;var d=f(u,c,a);if("HTML"!==u.nodeName||m(c))l=d;else{var p=h(),y=p.height,b=p.width;l.top+=d.top-d.marginTop,l.bottom=y+d.top,l.left+=d.left-d.marginLeft,l.right=b+d.left}}return l.left+=o,l.top+=o,l.right-=o,l.bottom-=o,l}function b(t){return t.width*t.height}function _(t,e,n,i,o){var r=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var s=y(n,i,r,o),a={top:{width:s.width,height:e.top-s.top},right:{width:s.right-e.right,height:s.height},bottom:{width:s.width,height:s.bottom-e.bottom},left:{width:e.left-s.left,height:s.height}},l=Object.keys(a).map((function(t){return tt({key:t},a[t],{area:b(a[t])})})).sort((function(t,e){return e.area-t.area})),c=l.filter((function(t){var e=t.width,i=t.height;return e>=n.clientWidth&&i>=n.clientHeight})),u=0<c.length?c[0].key:l[0].key,h=t.split("-")[1];return u+(h?"-"+h:"")}function w(t,e,n){var i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,o=i?v(e):s(e,n);return f(n,o,i)}function x(t){var e=getComputedStyle(t),n=parseFloat(e.marginTop)+parseFloat(e.marginBottom),i=parseFloat(e.marginLeft)+parseFloat(e.marginRight);return{width:t.offsetWidth+i,height:t.offsetHeight+n}}function C(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,(function(t){return e[t]}))}function E(t,e,n){n=n.split("-")[0];var i=x(t),o={width:i.width,height:i.height},r=-1!==["right","left"].indexOf(n),s=r?"top":"left",a=r?"left":"top",l=r?"height":"width",c=r?"width":"height";return o[s]=e[s]+e[l]/2-i[l]/2,o[a]=n===a?e[a]-i[c]:e[C(a)],o}function T(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function S(e,n,i){var o=void 0===i?e:e.slice(0,function(t,e,n){if(Array.prototype.findIndex)return t.findIndex((function(t){return t[e]===n}));var i=T(t,(function(t){return t[e]===n}));return t.indexOf(i)}(e,"name",i));return o.forEach((function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=e.function||e.fn;e.enabled&&t(i)&&(n.offsets.popper=d(n.offsets.popper),n.offsets.reference=d(n.offsets.reference),n=i(n,e))})),n}function D(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=w(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=_(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=E(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=S(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}function A(t,e){return t.some((function(t){var n=t.name;return t.enabled&&n===e}))}function I(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),i=0;i<e.length;i++){var o=e[i],r=o?""+o+n:t;if(void 0!==document.body.style[r])return r}return null}function L(){return this.state.isDestroyed=!0,A(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[I("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function k(t){var e=t.ownerDocument;return e?e.defaultView:window}function O(t,e,n,o){var r="BODY"===t.nodeName,s=r?t.ownerDocument.defaultView:t;s.addEventListener(e,n,{passive:!0}),r||O(i(s.parentNode),e,n,o),o.push(s)}function P(t,e,n,o){n.updateBound=o,k(t).addEventListener("resize",n.updateBound,{passive:!0});var r=i(t);return O(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function N(){this.state.eventsEnabled||(this.state=P(this.reference,this.options,this.state,this.scheduleUpdate))}function j(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=function(t,e){return k(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach((function(t){t.removeEventListener("scroll",e.updateBound)})),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e}(this.reference,this.state))}function $(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function M(t,e){Object.keys(e).forEach((function(n){var i="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&$(e[n])&&(i="px"),t.style[n]=e[n]+i}))}function z(t,e,n){var i=T(t,(function(t){return t.name===e})),o=!!i&&t.some((function(t){return t.name===n&&t.enabled&&t.order<i.order}));if(!o){var r="`"+e+"`";console.warn("`"+n+"` modifier is required by "+r+" modifier in order to work, be sure to include it before "+r+"!")}return o}function H(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=nt.indexOf(t),i=nt.slice(n+1).concat(nt.slice(0,n));return e?i.reverse():i}function R(t,e,n,i){var o=[0,0],r=-1!==["right","left"].indexOf(i),s=t.split(/(\+|\-)/).map((function(t){return t.trim()})),a=s.indexOf(T(s,(function(t){return-1!==t.search(/,|\s/)})));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,c=-1===a?[s]:[s.slice(0,a).concat([s[a].split(l)[0]]),[s[a].split(l)[1]].concat(s.slice(a+1))];return c=c.map((function(t,i){var o=(1===i?!r:r)?"height":"width",s=!1;return t.reduce((function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,s=!0,t):s?(t[t.length-1]+=e,s=!1,t):t.concat(e)}),[]).map((function(t){return function(t,e,n,i){var o=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+o[1],s=o[2];if(!r)return t;if(0===s.indexOf("%")){return d("%p"===s?n:i)[e]/100*r}return"vh"===s||"vw"===s?("vh"===s?W(document.documentElement.clientHeight,window.innerHeight||0):W(document.documentElement.clientWidth,window.innerWidth||0))/100*r:r}(t,o,e,n)}))})),c.forEach((function(t,e){t.forEach((function(n,i){$(n)&&(o[e]+=n*("-"===t[i-1]?-1:1))}))})),o}for(var q=Math.min,F=Math.floor,W=Math.max,B="undefined"!=typeof window&&"undefined"!=typeof document,U=["Edge","Trident","Firefox"],Y=0,X=0;X<U.length;X+=1)if(B&&0<=navigator.userAgent.indexOf(U[X])){Y=1;break}var Q=B&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then((function(){e=!1,t()})))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout((function(){e=!1,t()}),Y))}},V={},K=function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"all";return t=t.toString(),V.hasOwnProperty(t)||("11"===t?V[t]=-1!==navigator.userAgent.indexOf("Trident"):"10"===t?V[t]=-1!==navigator.appVersion.indexOf("MSIE 10"):"all"===t&&(V[t]=-1!==navigator.userAgent.indexOf("Trident")||-1!==navigator.userAgent.indexOf("MSIE")),V.all=V.all||Object.keys(V).some((function(t){return V[t]}))),V[t]},Z=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},G=function(){function t(t,e){for(var n,i=0;i<e.length;i++)(n=e[i]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}(),J=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},tt=Object.assign||function(t){for(var e,n=1;n<arguments.length;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},et=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],nt=et.slice(3),it="flip",ot="clockwise",rt="counterclockwise",st=function(){function e(n,i){var o=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Z(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(o.update)},this.update=Q(this.update.bind(this)),this.options=tt({},e.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=n&&n.jquery?n[0]:n,this.popper=i&&i.jquery?i[0]:i,this.options.modifiers={},Object.keys(tt({},e.Defaults.modifiers,r.modifiers)).forEach((function(t){o.options.modifiers[t]=tt({},e.Defaults.modifiers[t]||{},r.modifiers?r.modifiers[t]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(t){return tt({name:t},o.options.modifiers[t])})).sort((function(t,e){return t.order-e.order})),this.modifiers.forEach((function(e){e.enabled&&t(e.onLoad)&&e.onLoad(o.reference,o.popper,o.options,e,o.state)})),this.update();var s=this.options.eventsEnabled;s&&this.enableEventListeners(),this.state.eventsEnabled=s}return G(e,[{key:"update",value:function(){return D.call(this)}},{key:"destroy",value:function(){return L.call(this)}},{key:"enableEventListeners",value:function(){return N.call(this)}},{key:"disableEventListeners",value:function(){return j.call(this)}}]),e}();return st.Utils=("undefined"==typeof window?global:window).PopperUtils,st.placements=et,st.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,n=e.split("-")[0],i=e.split("-")[1];if(i){var o=t.offsets,r=o.reference,s=o.popper,a=-1!==["bottom","top"].indexOf(n),l=a?"left":"top",c=a?"width":"height",u={start:J({},l,r[l]),end:J({},l,r[l]+r[c]-s[c])};t.offsets.popper=tt({},s,u[i])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var n,i=e.offset,o=t.placement,r=t.offsets,s=r.popper,a=r.reference,l=o.split("-")[0];return n=$(+i)?[+i,0]:R(i,s,a,l),"left"===l?(s.top+=n[0],s.left-=n[1]):"right"===l?(s.top+=n[0],s.left+=n[1]):"top"===l?(s.left+=n[0],s.top-=n[1]):"bottom"===l&&(s.left+=n[0],s.top+=n[1]),t.popper=s,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var n=e.boundariesElement||o(t.instance.popper);t.instance.reference===n&&(n=o(n));var i=y(t.instance.popper,t.instance.reference,e.padding,n,t.positionFixed);e.boundaries=i;var r=e.priority,s=t.offsets.popper,a={primary:function(t){var n=s[t];return s[t]<i[t]&&!e.escapeWithReference&&(n=W(s[t],i[t])),J({},t,n)},secondary:function(t){var n="right"===t?"left":"top",o=s[n];return s[t]>i[t]&&!e.escapeWithReference&&(o=q(s[n],i[t]-("right"===t?s.width:s.height))),J({},n,o)}};return r.forEach((function(t){var e=-1===["left","top"].indexOf(t)?"secondary":"primary";s=tt({},s,a[e](t))})),t.offsets.popper=s,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,i=e.reference,o=t.placement.split("-")[0],r=F,s=-1!==["top","bottom"].indexOf(o),a=s?"right":"bottom",l=s?"left":"top",c=s?"width":"height";return n[a]<r(i[l])&&(t.offsets.popper[l]=r(i[l])-n[c]),n[l]>r(i[a])&&(t.offsets.popper[l]=r(i[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,n){var i;if(!z(t.instance.modifiers,"arrow","keepTogether"))return t;var o=n.element;if("string"==typeof o){if(!(o=t.instance.popper.querySelector(o)))return t}else if(!t.instance.popper.contains(o))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var r=t.placement.split("-")[0],s=t.offsets,a=s.popper,l=s.reference,c=-1!==["left","right"].indexOf(r),u=c?"height":"width",h=c?"Top":"Left",p=h.toLowerCase(),f=c?"left":"top",g=c?"bottom":"right",m=x(o)[u];l[g]-m<a[p]&&(t.offsets.popper[p]-=a[p]-(l[g]-m)),l[p]+m>a[g]&&(t.offsets.popper[p]+=l[p]+m-a[g]),t.offsets.popper=d(t.offsets.popper);var v=l[p]+l[u]/2-m/2,y=e(t.instance.popper),b=parseFloat(y["margin"+h],10),_=parseFloat(y["border"+h+"Width"],10),w=v-t.offsets.popper[p]-b-_;return w=W(q(a[u]-m,w),0),t.arrowElement=o,t.offsets.arrow=(J(i={},p,Math.round(w)),J(i,f,""),i),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(A(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var n=y(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement,t.positionFixed),i=t.placement.split("-")[0],o=C(i),r=t.placement.split("-")[1]||"",s=[];switch(e.behavior){case it:s=[i,o];break;case ot:s=H(i);break;case rt:s=H(i,!0);break;default:s=e.behavior}return s.forEach((function(a,l){if(i!==a||s.length===l+1)return t;i=t.placement.split("-")[0],o=C(i);var c=t.offsets.popper,u=t.offsets.reference,h=F,d="left"===i&&h(c.right)>h(u.left)||"right"===i&&h(c.left)<h(u.right)||"top"===i&&h(c.bottom)>h(u.top)||"bottom"===i&&h(c.top)<h(u.bottom),p=h(c.left)<h(n.left),f=h(c.right)>h(n.right),g=h(c.top)<h(n.top),m=h(c.bottom)>h(n.bottom),v="left"===i&&p||"right"===i&&f||"top"===i&&g||"bottom"===i&&m,y=-1!==["top","bottom"].indexOf(i),b=!!e.flipVariations&&(y&&"start"===r&&p||y&&"end"===r&&f||!y&&"start"===r&&g||!y&&"end"===r&&m);(d||v||b)&&(t.flipped=!0,(d||v)&&(i=s[l+1]),b&&(r=function(t){return"end"===t?"start":"start"===t?"end":t}(r)),t.placement=i+(r?"-"+r:""),t.offsets.popper=tt({},t.offsets.popper,E(t.instance.popper,t.offsets.reference,t.placement)),t=S(t.instance.modifiers,t,"flip"))})),t},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],i=t.offsets,o=i.popper,r=i.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return o[s?"left":"top"]=r[n]-(a?o[s?"width":"height"]:0),t.placement=C(e),t.offsets.popper=d(o),t}},hide:{order:800,enabled:!0,fn:function(t){if(!z(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=T(t.instance.modifiers,(function(t){return"preventOverflow"===t.name})).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,i=e.y,r=t.offsets.popper,s=T(t.instance.modifiers,(function(t){return"applyStyle"===t.name})).gpuAcceleration;void 0!==s&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a,l,c=void 0===s?e.gpuAcceleration:s,u=p(o(t.instance.popper)),h={position:r.position},d={left:F(r.left),top:F(r.top),bottom:F(r.bottom),right:F(r.right)},f="bottom"===n?"top":"bottom",g="right"===i?"left":"right",m=I("transform");if(l="bottom"==f?-u.height+d.bottom:d.top,a="right"==g?-u.width+d.right:d.left,c&&m)h[m]="translate3d("+a+"px, "+l+"px, 0)",h[f]=0,h[g]=0,h.willChange="transform";else{var v="bottom"==f?-1:1,y="right"==g?-1:1;h[f]=l*v,h[g]=a*y,h.willChange=f+", "+g}var b={"x-placement":t.placement};return t.attributes=tt({},b,t.attributes),t.styles=tt({},h,t.styles),t.arrowStyles=tt({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){return M(t.instance.popper,t.styles),function(t,e){Object.keys(e).forEach((function(n){!1===e[n]?t.removeAttribute(n):t.setAttribute(n,e[n])}))}(t.instance.popper,t.attributes),t.arrowElement&&Object.keys(t.arrowStyles).length&&M(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,i,o){var r=w(o,e,t,n.positionFixed),s=_(n.placement,r,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",s),M(e,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},st})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e((t=t||self).bootstrap={},t.jQuery,t.Popper)}(this,(function(t,e,n){"use strict";function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function o(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){var i,o,r;i=t,r=n[o=e],o in i?Object.defineProperty(i,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):i[o]=r})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}e=e&&e.hasOwnProperty("default")?e.default:e,n=n&&n.hasOwnProperty("default")?n.default:n;var a="transitionend";var l={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e=t.getAttribute("data-target");if(!e||"#"===e){var n=t.getAttribute("href");e=n&&"#"!==n?n.trim():""}try{return document.querySelector(e)?e:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var n=e(t).css("transition-duration"),i=e(t).css("transition-delay"),o=parseFloat(n),r=parseFloat(i);return o||r?(n=n.split(",")[0],i=i.split(",")[0],1e3*(parseFloat(n)+parseFloat(i))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){e(t).trigger(a)},supportsTransitionEnd:function(){return Boolean(a)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var o=n[i],r=e[i],s=r&&l.isElement(r)?"element":(a=r,{}.toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase());if(!new RegExp(o).test(s))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+s+'" but expected type "'+o+'".')}var a},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?l.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===e)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=e.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};l.jQueryDetection(),e.fn.emulateTransitionEnd=function(t){var n=this,i=!1;return e(this).one(l.TRANSITION_END,(function(){i=!0})),setTimeout((function(){i||l.triggerTransitionEnd(n)}),t),this},e.event.special[l.TRANSITION_END]={bindType:a,delegateType:a,handle:function(t){if(e(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var c="alert",u="bs.alert",h="."+u,d=e.fn[c],p={CLOSE:"close"+h,CLOSED:"closed"+h,CLICK_DATA_API:"click"+h+".data-api"},f=function(){function t(t){this._element=t}var n=t.prototype;return n.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},n.dispose=function(){e.removeData(this._element,u),this._element=null},n._getRootElement=function(t){var n=l.getSelectorFromElement(t),i=!1;return n&&(i=document.querySelector(n)),i||e(t).closest(".alert")[0]},n._triggerCloseEvent=function(t){var n=e.Event(p.CLOSE);return e(t).trigger(n),n},n._removeElement=function(t){var n=this;if(e(t).removeClass("show"),e(t).hasClass("fade")){var i=l.getTransitionDurationFromElement(t);e(t).one(l.TRANSITION_END,(function(e){return n._destroyElement(t,e)})).emulateTransitionEnd(i)}else this._destroyElement(t)},n._destroyElement=function(t){e(t).detach().trigger(p.CLOSED).remove()},t._jQueryInterface=function(n){return this.each((function(){var i=e(this),o=i.data(u);o||(o=new t(this),i.data(u,o)),"close"===n&&o[n](this)}))},t._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),t}();e(document).on(p.CLICK_DATA_API,'[data-dismiss="alert"]',f._handleDismiss(new f)),e.fn[c]=f._jQueryInterface,e.fn[c].Constructor=f,e.fn[c].noConflict=function(){return e.fn[c]=d,f._jQueryInterface};var g="button",m="bs.button",v="."+m,y=".data-api",b=e.fn[g],_="active",w='[data-toggle^="button"]',x='input:not([type="hidden"])',C=".btn",E={CLICK_DATA_API:"click"+v+y,FOCUS_BLUR_DATA_API:"focus"+v+y+" blur"+v+y,LOAD_DATA_API:"load"+v+y},T=function(){function t(t){this._element=t}var n=t.prototype;return n.toggle=function(){var t=!0,n=!0,i=e(this._element).closest('[data-toggle="buttons"]')[0];if(i){var o=this._element.querySelector(x);if(o){if("radio"===o.type)if(o.checked&&this._element.classList.contains(_))t=!1;else{var r=i.querySelector(".active");r&&e(r).removeClass(_)}else"checkbox"===o.type?"LABEL"===this._element.tagName&&o.checked===this._element.classList.contains(_)&&(t=!1):t=!1;t&&(o.checked=!this._element.classList.contains(_),e(o).trigger("change")),o.focus(),n=!1}}this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(_)),t&&e(this._element).toggleClass(_))},n.dispose=function(){e.removeData(this._element,m),this._element=null},t._jQueryInterface=function(n){return this.each((function(){var i=e(this).data(m);i||(i=new t(this),e(this).data(m,i)),"toggle"===n&&i[n]()}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),t}();e(document).on(E.CLICK_DATA_API,w,(function(t){var n=t.target;if(e(n).hasClass("btn")||(n=e(n).closest(C)[0]),!n||n.hasAttribute("disabled")||n.classList.contains("disabled"))t.preventDefault();else{var i=n.querySelector(x);if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void t.preventDefault();T._jQueryInterface.call(e(n),"toggle")}})).on(E.FOCUS_BLUR_DATA_API,w,(function(t){var n=e(t.target).closest(C)[0];e(n).toggleClass("focus",/^focus(in)?$/.test(t.type))})),e(window).on(E.LOAD_DATA_API,(function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,n=t.length;e<n;e++){var i=t[e],o=i.querySelector(x);o.checked||o.hasAttribute("checked")?i.classList.add(_):i.classList.remove(_)}for(var r=0,s=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;r<s;r++){var a=t[r];"true"===a.getAttribute("aria-pressed")?a.classList.add(_):a.classList.remove(_)}})),e.fn[g]=T._jQueryInterface,e.fn[g].Constructor=T,e.fn[g].noConflict=function(){return e.fn[g]=b,T._jQueryInterface};var S="carousel",D="bs.carousel",A="."+D,I=".data-api",L=e.fn[S],k={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},O={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},P="next",N="prev",j={SLIDE:"slide"+A,SLID:"slid"+A,KEYDOWN:"keydown"+A,MOUSEENTER:"mouseenter"+A,MOUSELEAVE:"mouseleave"+A,TOUCHSTART:"touchstart"+A,TOUCHMOVE:"touchmove"+A,TOUCHEND:"touchend"+A,POINTERDOWN:"pointerdown"+A,POINTERUP:"pointerup"+A,DRAG_START:"dragstart"+A,LOAD_DATA_API:"load"+A+I,CLICK_DATA_API:"click"+A+I},$="active",M=".active.carousel-item",z={TOUCH:"touch",PEN:"pen"},H=function(){function t(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var n=t.prototype;return n.next=function(){this._isSliding||this._slide(P)},n.nextWhenVisible=function(){!document.hidden&&e(this._element).is(":visible")&&"hidden"!==e(this._element).css("visibility")&&this.next()},n.prev=function(){this._isSliding||this._slide(N)},n.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(l.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},n.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(t){var n=this;this._activeElement=this._element.querySelector(M);var i=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)e(this._element).one(j.SLID,(function(){return n.to(t)}));else{if(i===t)return this.pause(),void this.cycle();var o=i<t?P:N;this._slide(o,this._items[t])}},n.dispose=function(){e(this._element).off(A),e.removeData(this._element,D),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},n._getConfig=function(t){return t=s({},k,{},t),l.typeCheckConfig(S,t,O),t},n._handleSwipe=function(){var t=Math.abs(this.touchDeltaX);if(!(t<=40)){var e=t/this.touchDeltaX;(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next()}},n._addEventListeners=function(){var t=this;this._config.keyboard&&e(this._element).on(j.KEYDOWN,(function(e){return t._keydown(e)})),"hover"===this._config.pause&&e(this._element).on(j.MOUSEENTER,(function(e){return t.pause(e)})).on(j.MOUSELEAVE,(function(e){return t.cycle(e)})),this._config.touch&&this._addTouchEventListeners()},n._addTouchEventListeners=function(){var t=this;if(this._touchSupported){var n=function(e){t._pointerEvent&&z[e.originalEvent.pointerType.toUpperCase()]?t.touchStartX=e.originalEvent.clientX:t._pointerEvent||(t.touchStartX=e.originalEvent.touches[0].clientX)},i=function(e){t._pointerEvent&&z[e.originalEvent.pointerType.toUpperCase()]&&(t.touchDeltaX=e.originalEvent.clientX-t.touchStartX),t._handleSwipe(),"hover"===t._config.pause&&(t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout((function(e){return t.cycle(e)}),500+t._config.interval))};e(this._element.querySelectorAll(".carousel-item img")).on(j.DRAG_START,(function(t){return t.preventDefault()})),this._pointerEvent?(e(this._element).on(j.POINTERDOWN,(function(t){return n(t)})),e(this._element).on(j.POINTERUP,(function(t){return i(t)})),this._element.classList.add("pointer-event")):(e(this._element).on(j.TOUCHSTART,(function(t){return n(t)})),e(this._element).on(j.TOUCHMOVE,(function(e){return function(e){e.originalEvent.touches&&1<e.originalEvent.touches.length?t.touchDeltaX=0:t.touchDeltaX=e.originalEvent.touches[0].clientX-t.touchStartX}(e)})),e(this._element).on(j.TOUCHEND,(function(t){return i(t)})))}},n._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},n._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(t)},n._getItemByDirection=function(t,e){var n=t===P,i=t===N,o=this._getItemIndex(e),r=this._items.length-1;if((i&&0===o||n&&o===r)&&!this._config.wrap)return e;var s=(o+(t===N?-1:1))%this._items.length;return-1==s?this._items[this._items.length-1]:this._items[s]},n._triggerSlideEvent=function(t,n){var i=this._getItemIndex(t),o=this._getItemIndex(this._element.querySelector(M)),r=e.Event(j.SLIDE,{relatedTarget:t,direction:n,from:o,to:i});return e(this._element).trigger(r),r},n._setActiveIndicatorElement=function(t){if(this._indicatorsElement){var n=[].slice.call(this._indicatorsElement.querySelectorAll(".active"));e(n).removeClass($);var i=this._indicatorsElement.children[this._getItemIndex(t)];i&&e(i).addClass($)}},n._slide=function(t,n){var i,o,r,s=this,a=this._element.querySelector(M),c=this._getItemIndex(a),u=n||a&&this._getItemByDirection(t,a),h=this._getItemIndex(u),d=Boolean(this._interval);if(r=t===P?(i="carousel-item-left",o="carousel-item-next","left"):(i="carousel-item-right",o="carousel-item-prev","right"),u&&e(u).hasClass($))this._isSliding=!1;else if(!this._triggerSlideEvent(u,r).isDefaultPrevented()&&a&&u){this._isSliding=!0,d&&this.pause(),this._setActiveIndicatorElement(u);var p=e.Event(j.SLID,{relatedTarget:u,direction:r,from:c,to:h});if(e(this._element).hasClass("slide")){e(u).addClass(o),l.reflow(u),e(a).addClass(i),e(u).addClass(i);var f=parseInt(u.getAttribute("data-interval"),10);f?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=f):this._config.interval=this._config.defaultInterval||this._config.interval;var g=l.getTransitionDurationFromElement(a);e(a).one(l.TRANSITION_END,(function(){e(u).removeClass(i+" "+o).addClass($),e(a).removeClass($+" "+o+" "+i),s._isSliding=!1,setTimeout((function(){return e(s._element).trigger(p)}),0)})).emulateTransitionEnd(g)}else e(a).removeClass($),e(u).addClass($),this._isSliding=!1,e(this._element).trigger(p);d&&this.cycle()}},t._jQueryInterface=function(n){return this.each((function(){var i=e(this).data(D),o=s({},k,{},e(this).data());"object"==typeof n&&(o=s({},o,{},n));var r="string"==typeof n?n:o.slide;if(i||(i=new t(this,o),e(this).data(D,i)),"number"==typeof n)i.to(n);else if("string"==typeof r){if(void 0===i[r])throw new TypeError('No method named "'+r+'"');i[r]()}else o.interval&&o.ride&&(i.pause(),i.cycle())}))},t._dataApiClickHandler=function(n){var i=l.getSelectorFromElement(this);if(i){var o=e(i)[0];if(o&&e(o).hasClass("carousel")){var r=s({},e(o).data(),{},e(this).data()),a=this.getAttribute("data-slide-to");a&&(r.interval=!1),t._jQueryInterface.call(e(o),r),a&&e(o).data(D).to(a),n.preventDefault()}}},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return k}}]),t}();e(document).on(j.CLICK_DATA_API,"[data-slide], [data-slide-to]",H._dataApiClickHandler),e(window).on(j.LOAD_DATA_API,(function(){for(var t=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),n=0,i=t.length;n<i;n++){var o=e(t[n]);H._jQueryInterface.call(o,o.data())}})),e.fn[S]=H._jQueryInterface,e.fn[S].Constructor=H,e.fn[S].noConflict=function(){return e.fn[S]=L,H._jQueryInterface};var R="collapse",q="bs.collapse",F="."+q,W=e.fn[R],B={toggle:!0,parent:""},U={toggle:"boolean",parent:"(string|element)"},Y={SHOW:"show"+F,SHOWN:"shown"+F,HIDE:"hide"+F,HIDDEN:"hidden"+F,CLICK_DATA_API:"click"+F+".data-api"},X="show",Q="collapse",V="collapsing",K="collapsed",Z="width",G='[data-toggle="collapse"]',J=function(){function t(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll(G)),i=0,o=n.length;i<o;i++){var r=n[i],s=l.getSelectorFromElement(r),a=[].slice.call(document.querySelectorAll(s)).filter((function(e){return e===t}));null!==s&&0<a.length&&(this._selector=s,this._triggerArray.push(r))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var n=t.prototype;return n.toggle=function(){e(this._element).hasClass(X)?this.hide():this.show()},n.show=function(){var n,i,o=this;if(!(this._isTransitioning||e(this._element).hasClass(X)||(this._parent&&0===(n=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter((function(t){return"string"==typeof o._config.parent?t.getAttribute("data-parent")===o._config.parent:t.classList.contains(Q)}))).length&&(n=null),n&&(i=e(n).not(this._selector).data(q))&&i._isTransitioning))){var r=e.Event(Y.SHOW);if(e(this._element).trigger(r),!r.isDefaultPrevented()){n&&(t._jQueryInterface.call(e(n).not(this._selector),"hide"),i||e(n).data(q,null));var s=this._getDimension();e(this._element).removeClass(Q).addClass(V),this._element.style[s]=0,this._triggerArray.length&&e(this._triggerArray).removeClass(K).attr("aria-expanded",!0),this.setTransitioning(!0);var a="scroll"+(s[0].toUpperCase()+s.slice(1)),c=l.getTransitionDurationFromElement(this._element);e(this._element).one(l.TRANSITION_END,(function(){e(o._element).removeClass(V).addClass(Q).addClass(X),o._element.style[s]="",o.setTransitioning(!1),e(o._element).trigger(Y.SHOWN)})).emulateTransitionEnd(c),this._element.style[s]=this._element[a]+"px"}}},n.hide=function(){var t=this;if(!this._isTransitioning&&e(this._element).hasClass(X)){var n=e.Event(Y.HIDE);if(e(this._element).trigger(n),!n.isDefaultPrevented()){var i=this._getDimension();this._element.style[i]=this._element.getBoundingClientRect()[i]+"px",l.reflow(this._element),e(this._element).addClass(V).removeClass(Q).removeClass(X);var o=this._triggerArray.length;if(0<o)for(var r=0;r<o;r++){var s=this._triggerArray[r],a=l.getSelectorFromElement(s);null!==a&&(e([].slice.call(document.querySelectorAll(a))).hasClass(X)||e(s).addClass(K).attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[i]="";var c=l.getTransitionDurationFromElement(this._element);e(this._element).one(l.TRANSITION_END,(function(){t.setTransitioning(!1),e(t._element).removeClass(V).addClass(Q).trigger(Y.HIDDEN)})).emulateTransitionEnd(c)}}},n.setTransitioning=function(t){this._isTransitioning=t},n.dispose=function(){e.removeData(this._element,q),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},n._getConfig=function(t){return(t=s({},B,{},t)).toggle=Boolean(t.toggle),l.typeCheckConfig(R,t,U),t},n._getDimension=function(){return e(this._element).hasClass(Z)?Z:"height"},n._getParent=function(){var n,i=this;l.isElement(this._config.parent)?(n=this._config.parent,void 0!==this._config.parent.jquery&&(n=this._config.parent[0])):n=document.querySelector(this._config.parent);var o='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',r=[].slice.call(n.querySelectorAll(o));return e(r).each((function(e,n){i._addAriaAndCollapsedClass(t._getTargetFromElement(n),[n])})),n},n._addAriaAndCollapsedClass=function(t,n){var i=e(t).hasClass(X);n.length&&e(n).toggleClass(K,!i).attr("aria-expanded",i)},t._getTargetFromElement=function(t){var e=l.getSelectorFromElement(t);return e?document.querySelector(e):null},t._jQueryInterface=function(n){return this.each((function(){var i=e(this),o=i.data(q),r=s({},B,{},i.data(),{},"object"==typeof n&&n?n:{});if(!o&&r.toggle&&/show|hide/.test(n)&&(r.toggle=!1),o||(o=new t(this,r),i.data(q,o)),"string"==typeof n){if(void 0===o[n])throw new TypeError('No method named "'+n+'"');o[n]()}}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return B}}]),t}();e(document).on(Y.CLICK_DATA_API,G,(function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var n=e(this),i=l.getSelectorFromElement(this),o=[].slice.call(document.querySelectorAll(i));e(o).each((function(){var t=e(this),i=t.data(q)?"toggle":n.data();J._jQueryInterface.call(t,i)}))})),e.fn[R]=J._jQueryInterface,e.fn[R].Constructor=J,e.fn[R].noConflict=function(){return e.fn[R]=W,J._jQueryInterface};var tt="dropdown",et="bs.dropdown",nt="."+et,it=".data-api",ot=e.fn[tt],rt=new RegExp("38|40|27"),st={HIDE:"hide"+nt,HIDDEN:"hidden"+nt,SHOW:"show"+nt,SHOWN:"shown"+nt,CLICK:"click"+nt,CLICK_DATA_API:"click"+nt+it,KEYDOWN_DATA_API:"keydown"+nt+it,KEYUP_DATA_API:"keyup"+nt+it},at="disabled",lt="show",ct="dropdown-menu-right",ut='[data-toggle="dropdown"]',ht=".dropdown-menu",dt={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},pt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},ft=function(){function t(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var i=t.prototype;return i.toggle=function(){if(!this._element.disabled&&!e(this._element).hasClass(at)){var n=e(this._menu).hasClass(lt);t._clearMenus(),n||this.show(!0)}},i.show=function(i){if(void 0===i&&(i=!1),!(this._element.disabled||e(this._element).hasClass(at)||e(this._menu).hasClass(lt))){var o={relatedTarget:this._element},r=e.Event(st.SHOW,o),s=t._getParentFromElement(this._element);if(e(s).trigger(r),!r.isDefaultPrevented()){if(!this._inNavbar&&i){if(void 0===n)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var a=this._element;"parent"===this._config.reference?a=s:l.isElement(this._config.reference)&&(a=this._config.reference,void 0!==this._config.reference.jquery&&(a=this._config.reference[0])),"scrollParent"!==this._config.boundary&&e(s).addClass("position-static"),this._popper=new n(a,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===e(s).closest(".navbar-nav").length&&e(document.body).children().on("mouseover",null,e.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),e(this._menu).toggleClass(lt),e(s).toggleClass(lt).trigger(e.Event(st.SHOWN,o))}}},i.hide=function(){if(!this._element.disabled&&!e(this._element).hasClass(at)&&e(this._menu).hasClass(lt)){var n={relatedTarget:this._element},i=e.Event(st.HIDE,n),o=t._getParentFromElement(this._element);e(o).trigger(i),i.isDefaultPrevented()||(this._popper&&this._popper.destroy(),e(this._menu).toggleClass(lt),e(o).toggleClass(lt).trigger(e.Event(st.HIDDEN,n)))}},i.dispose=function(){e.removeData(this._element,et),e(this._element).off(nt),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},i.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},i._addEventListeners=function(){var t=this;e(this._element).on(st.CLICK,(function(e){e.preventDefault(),e.stopPropagation(),t.toggle()}))},i._getConfig=function(t){return t=s({},this.constructor.Default,{},e(this._element).data(),{},t),l.typeCheckConfig(tt,t,this.constructor.DefaultType),t},i._getMenuElement=function(){if(!this._menu){var e=t._getParentFromElement(this._element);e&&(this._menu=e.querySelector(ht))}return this._menu},i._getPlacement=function(){var t=e(this._element.parentNode),n="bottom-start";return t.hasClass("dropup")?(n="top-start",e(this._menu).hasClass(ct)&&(n="top-end")):t.hasClass("dropright")?n="right-start":t.hasClass("dropleft")?n="left-start":e(this._menu).hasClass(ct)&&(n="bottom-end"),n},i._detectNavbar=function(){return 0<e(this._element).closest(".navbar").length},i._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,{},t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},i._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),s({},t,{},this._config.popperConfig)},t._jQueryInterface=function(n){return this.each((function(){var i=e(this).data(et);if(i||(i=new t(this,"object"==typeof n?n:null),e(this).data(et,i)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}}))},t._clearMenus=function(n){if(!n||3!==n.which&&("keyup"!==n.type||9===n.which))for(var i=[].slice.call(document.querySelectorAll(ut)),o=0,r=i.length;o<r;o++){var s=t._getParentFromElement(i[o]),a=e(i[o]).data(et),l={relatedTarget:i[o]};if(n&&"click"===n.type&&(l.clickEvent=n),a){var c=a._menu;if(e(s).hasClass(lt)&&!(n&&("click"===n.type&&/input|textarea/i.test(n.target.tagName)||"keyup"===n.type&&9===n.which)&&e.contains(s,n.target))){var u=e.Event(st.HIDE,l);e(s).trigger(u),u.isDefaultPrevented()||("ontouchstart"in document.documentElement&&e(document.body).children().off("mouseover",null,e.noop),i[o].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),e(c).removeClass(lt),e(s).removeClass(lt).trigger(e.Event(st.HIDDEN,l)))}}}},t._getParentFromElement=function(t){var e,n=l.getSelectorFromElement(t);return n&&(e=document.querySelector(n)),e||t.parentNode},t._dataApiKeydownHandler=function(n){if((/input|textarea/i.test(n.target.tagName)?!(32===n.which||27!==n.which&&(40!==n.which&&38!==n.which||e(n.target).closest(ht).length)):rt.test(n.which))&&(n.preventDefault(),n.stopPropagation(),!this.disabled&&!e(this).hasClass(at))){var i=t._getParentFromElement(this),o=e(i).hasClass(lt);if(o||27!==n.which)if(o&&(!o||27!==n.which&&32!==n.which)){var r=[].slice.call(i.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter((function(t){return e(t).is(":visible")}));if(0!==r.length){var s=r.indexOf(n.target);38===n.which&&0<s&&s--,40===n.which&&s<r.length-1&&s++,s<0&&(s=0),r[s].focus()}}else{if(27===n.which){var a=i.querySelector(ut);e(a).trigger("focus")}e(this).trigger("click")}}},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return dt}},{key:"DefaultType",get:function(){return pt}}]),t}();e(document).on(st.KEYDOWN_DATA_API,ut,ft._dataApiKeydownHandler).on(st.KEYDOWN_DATA_API,ht,ft._dataApiKeydownHandler).on(st.CLICK_DATA_API+" "+st.KEYUP_DATA_API,ft._clearMenus).on(st.CLICK_DATA_API,ut,(function(t){t.preventDefault(),t.stopPropagation(),ft._jQueryInterface.call(e(this),"toggle")})).on(st.CLICK_DATA_API,".dropdown form",(function(t){t.stopPropagation()})),e.fn[tt]=ft._jQueryInterface,e.fn[tt].Constructor=ft,e.fn[tt].noConflict=function(){return e.fn[tt]=ot,ft._jQueryInterface};var gt="modal",mt="bs.modal",vt="."+mt,yt=e.fn[gt],bt={backdrop:!0,keyboard:!0,focus:!0,show:!0},_t={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},wt={HIDE:"hide"+vt,HIDE_PREVENTED:"hidePrevented"+vt,HIDDEN:"hidden"+vt,SHOW:"show"+vt,SHOWN:"shown"+vt,FOCUSIN:"focusin"+vt,RESIZE:"resize"+vt,CLICK_DISMISS:"click.dismiss"+vt,KEYDOWN_DISMISS:"keydown.dismiss"+vt,MOUSEUP_DISMISS:"mouseup.dismiss"+vt,MOUSEDOWN_DISMISS:"mousedown.dismiss"+vt,CLICK_DATA_API:"click"+vt+".data-api"},xt="modal-open",Ct="fade",Et="show",Tt="modal-static",St=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Dt=".sticky-top",At=function(){function t(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var n=t.prototype;return n.toggle=function(t){return this._isShown?this.hide():this.show(t)},n.show=function(t){var n=this;if(!this._isShown&&!this._isTransitioning){e(this._element).hasClass(Ct)&&(this._isTransitioning=!0);var i=e.Event(wt.SHOW,{relatedTarget:t});e(this._element).trigger(i),this._isShown||i.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),e(this._element).on(wt.CLICK_DISMISS,'[data-dismiss="modal"]',(function(t){return n.hide(t)})),e(this._dialog).on(wt.MOUSEDOWN_DISMISS,(function(){e(n._element).one(wt.MOUSEUP_DISMISS,(function(t){e(t.target).is(n._element)&&(n._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return n._showElement(t)})))}},n.hide=function(t){var n=this;if(t&&t.preventDefault(),this._isShown&&!this._isTransitioning){var i=e.Event(wt.HIDE);if(e(this._element).trigger(i),this._isShown&&!i.isDefaultPrevented()){this._isShown=!1;var o=e(this._element).hasClass(Ct);if(o&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),e(document).off(wt.FOCUSIN),e(this._element).removeClass(Et),e(this._element).off(wt.CLICK_DISMISS),e(this._dialog).off(wt.MOUSEDOWN_DISMISS),o){var r=l.getTransitionDurationFromElement(this._element);e(this._element).one(l.TRANSITION_END,(function(t){return n._hideModal(t)})).emulateTransitionEnd(r)}else this._hideModal()}}},n.dispose=function(){[window,this._element,this._dialog].forEach((function(t){return e(t).off(vt)})),e(document).off(wt.FOCUSIN),e.removeData(this._element,mt),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(t){return t=s({},bt,{},t),l.typeCheckConfig(gt,t,_t),t},n._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var n=e.Event(wt.HIDE_PREVENTED);if(e(this._element).trigger(n),n.defaultPrevented)return;this._element.classList.add(Tt);var i=l.getTransitionDurationFromElement(this._element);e(this._element).one(l.TRANSITION_END,(function(){t._element.classList.remove(Tt)})).emulateTransitionEnd(i),this._element.focus()}else this.hide()},n._showElement=function(t){var n=this,i=e(this._element).hasClass(Ct),o=this._dialog?this._dialog.querySelector(".modal-body"):null;function r(){n._config.focus&&n._element.focus(),n._isTransitioning=!1,e(n._element).trigger(s)}this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),e(this._dialog).hasClass("modal-dialog-scrollable")&&o?o.scrollTop=0:this._element.scrollTop=0,i&&l.reflow(this._element),e(this._element).addClass(Et),this._config.focus&&this._enforceFocus();var s=e.Event(wt.SHOWN,{relatedTarget:t});if(i){var a=l.getTransitionDurationFromElement(this._dialog);e(this._dialog).one(l.TRANSITION_END,r).emulateTransitionEnd(a)}else r()},n._enforceFocus=function(){var t=this;e(document).off(wt.FOCUSIN).on(wt.FOCUSIN,(function(n){document!==n.target&&t._element!==n.target&&0===e(t._element).has(n.target).length&&t._element.focus()}))},n._setEscapeEvent=function(){var t=this;this._isShown&&this._config.keyboard?e(this._element).on(wt.KEYDOWN_DISMISS,(function(e){27===e.which&&t._triggerBackdropTransition()})):this._isShown||e(this._element).off(wt.KEYDOWN_DISMISS)},n._setResizeEvent=function(){var t=this;this._isShown?e(window).on(wt.RESIZE,(function(e){return t.handleUpdate(e)})):e(window).off(wt.RESIZE)},n._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop((function(){e(document.body).removeClass(xt),t._resetAdjustments(),t._resetScrollbar(),e(t._element).trigger(wt.HIDDEN)}))},n._removeBackdrop=function(){this._backdrop&&(e(this._backdrop).remove(),this._backdrop=null)},n._showBackdrop=function(t){var n=this,i=e(this._element).hasClass(Ct)?Ct:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",i&&this._backdrop.classList.add(i),e(this._backdrop).appendTo(document.body),e(this._element).on(wt.CLICK_DISMISS,(function(t){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:t.target===t.currentTarget&&n._triggerBackdropTransition()})),i&&l.reflow(this._backdrop),e(this._backdrop).addClass(Et),!t)return;if(!i)return void t();var o=l.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(l.TRANSITION_END,t).emulateTransitionEnd(o)}else if(!this._isShown&&this._backdrop){e(this._backdrop).removeClass(Et);var r=function(){n._removeBackdrop(),t&&t()};if(e(this._element).hasClass(Ct)){var s=l.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(l.TRANSITION_END,r).emulateTransitionEnd(s)}else r()}else t&&t()},n._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},n._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var t=this;if(this._isBodyOverflowing){var n=[].slice.call(document.querySelectorAll(St)),i=[].slice.call(document.querySelectorAll(Dt));e(n).each((function(n,i){var o=i.style.paddingRight,r=e(i).css("padding-right");e(i).data("padding-right",o).css("padding-right",parseFloat(r)+t._scrollbarWidth+"px")})),e(i).each((function(n,i){var o=i.style.marginRight,r=e(i).css("margin-right");e(i).data("margin-right",o).css("margin-right",parseFloat(r)-t._scrollbarWidth+"px")}));var o=document.body.style.paddingRight,r=e(document.body).css("padding-right");e(document.body).data("padding-right",o).css("padding-right",parseFloat(r)+this._scrollbarWidth+"px")}e(document.body).addClass(xt)},n._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(St));e(t).each((function(t,n){var i=e(n).data("padding-right");e(n).removeData("padding-right"),n.style.paddingRight=i||""}));var n=[].slice.call(document.querySelectorAll(""+Dt));e(n).each((function(t,n){var i=e(n).data("margin-right");void 0!==i&&e(n).css("margin-right",i).removeData("margin-right")}));var i=e(document.body).data("padding-right");e(document.body).removeData("padding-right"),document.body.style.paddingRight=i||""},n._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},t._jQueryInterface=function(n,i){return this.each((function(){var o=e(this).data(mt),r=s({},bt,{},e(this).data(),{},"object"==typeof n&&n?n:{});if(o||(o=new t(this,r),e(this).data(mt,o)),"string"==typeof n){if(void 0===o[n])throw new TypeError('No method named "'+n+'"');o[n](i)}else r.show&&o.show(i)}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return bt}}]),t}();e(document).on(wt.CLICK_DATA_API,'[data-toggle="modal"]',(function(t){var n,i=this,o=l.getSelectorFromElement(this);o&&(n=document.querySelector(o));var r=e(n).data(mt)?"toggle":s({},e(n).data(),{},e(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var a=e(n).one(wt.SHOW,(function(t){t.isDefaultPrevented()||a.one(wt.HIDDEN,(function(){e(i).is(":visible")&&i.focus()}))}));At._jQueryInterface.call(e(n),r,this)})),e.fn[gt]=At._jQueryInterface,e.fn[gt].Constructor=At,e.fn[gt].noConflict=function(){return e.fn[gt]=yt,At._jQueryInterface};var It=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],Lt=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,kt=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function Ot(t,e,n){if(0===t.length)return t;if(n&&"function"==typeof n)return n(t);for(var i=(new window.DOMParser).parseFromString(t,"text/html"),o=Object.keys(e),r=[].slice.call(i.body.querySelectorAll("*")),s=function(t){var n=r[t],i=n.nodeName.toLowerCase();if(-1===o.indexOf(n.nodeName.toLowerCase()))return n.parentNode.removeChild(n),"continue";var s=[].slice.call(n.attributes),a=[].concat(e["*"]||[],e[i]||[]);s.forEach((function(t){!function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===It.indexOf(n)||Boolean(t.nodeValue.match(Lt)||t.nodeValue.match(kt));for(var i=e.filter((function(t){return t instanceof RegExp})),o=0,r=i.length;o<r;o++)if(n.match(i[o]))return!0;return!1}(t,a)&&n.removeAttribute(t.nodeName)}))},a=0,l=r.length;a<l;a++)s(a);return i.body.innerHTML}var Pt="tooltip",Nt="bs.tooltip",jt="."+Nt,$t=e.fn[Pt],Mt="bs-tooltip",zt=new RegExp("(^|\\s)"+Mt+"\\S+","g"),Ht=["sanitize","whiteList","sanitizeFn"],Rt={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},qt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Ft={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},Wt="show",Bt="out",Ut={HIDE:"hide"+jt,HIDDEN:"hidden"+jt,SHOW:"show"+jt,SHOWN:"shown"+jt,INSERTED:"inserted"+jt,CLICK:"click"+jt,FOCUSIN:"focusin"+jt,FOCUSOUT:"focusout"+jt,MOUSEENTER:"mouseenter"+jt,MOUSELEAVE:"mouseleave"+jt},Yt="fade",Xt="show",Qt="hover",Vt="focus",Kt=function(){function t(t,e){if(void 0===n)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var i=t.prototype;return i.enable=function(){this._isEnabled=!0},i.disable=function(){this._isEnabled=!1},i.toggleEnabled=function(){this._isEnabled=!this._isEnabled},i.toggle=function(t){if(this._isEnabled)if(t){var n=this.constructor.DATA_KEY,i=e(t.currentTarget).data(n);i||(i=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(n,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)}else{if(e(this.getTipElement()).hasClass(Xt))return void this._leave(null,this);this._enter(null,this)}},i.dispose=function(){clearTimeout(this._timeout),e.removeData(this.element,this.constructor.DATA_KEY),e(this.element).off(this.constructor.EVENT_KEY),e(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&e(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},i.show=function(){var t=this;if("none"===e(this.element).css("display"))throw new Error("Please use show on visible elements");var i=e.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){e(this.element).trigger(i);var o=l.findShadowRoot(this.element),r=e.contains(null!==o?o:this.element.ownerDocument.documentElement,this.element);if(i.isDefaultPrevented()||!r)return;var s=this.getTipElement(),a=l.getUID(this.constructor.NAME);s.setAttribute("id",a),this.element.setAttribute("aria-describedby",a),this.setContent(),this.config.animation&&e(s).addClass(Yt);var c="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,u=this._getAttachment(c);this.addAttachmentClass(u);var h=this._getContainer();e(s).data(this.constructor.DATA_KEY,this),e.contains(this.element.ownerDocument.documentElement,this.tip)||e(s).appendTo(h),e(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new n(this.element,s,this._getPopperConfig(u)),e(s).addClass(Xt),"ontouchstart"in document.documentElement&&e(document.body).children().on("mouseover",null,e.noop);var d=function(){t.config.animation&&t._fixTransition();var n=t._hoverState;t._hoverState=null,e(t.element).trigger(t.constructor.Event.SHOWN),n===Bt&&t._leave(null,t)};if(e(this.tip).hasClass(Yt)){var p=l.getTransitionDurationFromElement(this.tip);e(this.tip).one(l.TRANSITION_END,d).emulateTransitionEnd(p)}else d()}},i.hide=function(t){function n(){i._hoverState!==Wt&&o.parentNode&&o.parentNode.removeChild(o),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),e(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),t&&t()}var i=this,o=this.getTipElement(),r=e.Event(this.constructor.Event.HIDE);if(e(this.element).trigger(r),!r.isDefaultPrevented()){if(e(o).removeClass(Xt),"ontouchstart"in document.documentElement&&e(document.body).children().off("mouseover",null,e.noop),this._activeTrigger.click=!1,this._activeTrigger[Vt]=!1,this._activeTrigger[Qt]=!1,e(this.tip).hasClass(Yt)){var s=l.getTransitionDurationFromElement(o);e(o).one(l.TRANSITION_END,n).emulateTransitionEnd(s)}else n();this._hoverState=""}},i.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},i.isWithContent=function(){return Boolean(this.getTitle())},i.addAttachmentClass=function(t){e(this.getTipElement()).addClass(Mt+"-"+t)},i.getTipElement=function(){return this.tip=this.tip||e(this.config.template)[0],this.tip},i.setContent=function(){var t=this.getTipElement();this.setElementContent(e(t.querySelectorAll(".tooltip-inner")),this.getTitle()),e(t).removeClass(Yt+" "+Xt)},i.setElementContent=function(t,n){"object"!=typeof n||!n.nodeType&&!n.jquery?this.config.html?(this.config.sanitize&&(n=Ot(n,this.config.whiteList,this.config.sanitizeFn)),t.html(n)):t.text(n):this.config.html?e(n).parent().is(t)||t.empty().append(n):t.text(e(n).text())},i.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},i._getPopperConfig=function(t){var e=this;return s({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}},{},this.config.popperConfig)},i._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,{},t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},i._getContainer=function(){return!1===this.config.container?document.body:l.isElement(this.config.container)?e(this.config.container):e(document).find(this.config.container)},i._getAttachment=function(t){return qt[t.toUpperCase()]},i._setListeners=function(){var t=this;this.config.trigger.split(" ").forEach((function(n){if("click"===n)e(t.element).on(t.constructor.Event.CLICK,t.config.selector,(function(e){return t.toggle(e)}));else if("manual"!==n){var i=n===Qt?t.constructor.Event.MOUSEENTER:t.constructor.Event.FOCUSIN,o=n===Qt?t.constructor.Event.MOUSELEAVE:t.constructor.Event.FOCUSOUT;e(t.element).on(i,t.config.selector,(function(e){return t._enter(e)})).on(o,t.config.selector,(function(e){return t._leave(e)}))}})),this._hideModalHandler=function(){t.element&&t.hide()},e(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=s({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},i._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},i._enter=function(t,n){var i=this.constructor.DATA_KEY;(n=n||e(t.currentTarget).data(i))||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(i,n)),t&&(n._activeTrigger["focusin"===t.type?Vt:Qt]=!0),e(n.getTipElement()).hasClass(Xt)||n._hoverState===Wt?n._hoverState=Wt:(clearTimeout(n._timeout),n._hoverState=Wt,n.config.delay&&n.config.delay.show?n._timeout=setTimeout((function(){n._hoverState===Wt&&n.show()}),n.config.delay.show):n.show())},i._leave=function(t,n){var i=this.constructor.DATA_KEY;(n=n||e(t.currentTarget).data(i))||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(i,n)),t&&(n._activeTrigger["focusout"===t.type?Vt:Qt]=!1),n._isWithActiveTrigger()||(clearTimeout(n._timeout),n._hoverState=Bt,n.config.delay&&n.config.delay.hide?n._timeout=setTimeout((function(){n._hoverState===Bt&&n.hide()}),n.config.delay.hide):n.hide())},i._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},i._getConfig=function(t){var n=e(this.element).data();return Object.keys(n).forEach((function(t){-1!==Ht.indexOf(t)&&delete n[t]})),"number"==typeof(t=s({},this.constructor.Default,{},n,{},"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),l.typeCheckConfig(Pt,t,this.constructor.DefaultType),t.sanitize&&(t.template=Ot(t.template,t.whiteList,t.sanitizeFn)),t},i._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},i._cleanTipClass=function(){var t=e(this.getTipElement()),n=t.attr("class").match(zt);null!==n&&n.length&&t.removeClass(n.join(""))},i._handlePopperPlacementChange=function(t){var e=t.instance;this.tip=e.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},i._fixTransition=function(){var t=this.getTipElement(),n=this.config.animation;null===t.getAttribute("x-placement")&&(e(t).removeClass(Yt),this.config.animation=!1,this.hide(),this.show(),this.config.animation=n)},t._jQueryInterface=function(n){return this.each((function(){var i=e(this).data(Nt),o="object"==typeof n&&n;if((i||!/dispose|hide/.test(n))&&(i||(i=new t(this,o),e(this).data(Nt,i)),"string"==typeof n)){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return Ft}},{key:"NAME",get:function(){return Pt}},{key:"DATA_KEY",get:function(){return Nt}},{key:"Event",get:function(){return Ut}},{key:"EVENT_KEY",get:function(){return jt}},{key:"DefaultType",get:function(){return Rt}}]),t}();e.fn[Pt]=Kt._jQueryInterface,e.fn[Pt].Constructor=Kt,e.fn[Pt].noConflict=function(){return e.fn[Pt]=$t,Kt._jQueryInterface};var Zt="popover",Gt="bs.popover",Jt="."+Gt,te=e.fn[Zt],ee="bs-popover",ne=new RegExp("(^|\\s)"+ee+"\\S+","g"),ie=s({},Kt.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),oe=s({},Kt.DefaultType,{content:"(string|element|function)"}),re={HIDE:"hide"+Jt,HIDDEN:"hidden"+Jt,SHOW:"show"+Jt,SHOWN:"shown"+Jt,INSERTED:"inserted"+Jt,CLICK:"click"+Jt,FOCUSIN:"focusin"+Jt,FOCUSOUT:"focusout"+Jt,MOUSEENTER:"mouseenter"+Jt,MOUSELEAVE:"mouseleave"+Jt},se=function(t){function n(){return t.apply(this,arguments)||this}!function(t,e){t.prototype=Object.create(e.prototype),(t.prototype.constructor=t).__proto__=e}(n,t);var i=n.prototype;return i.isWithContent=function(){return this.getTitle()||this._getContent()},i.addAttachmentClass=function(t){e(this.getTipElement()).addClass(ee+"-"+t)},i.getTipElement=function(){return this.tip=this.tip||e(this.config.template)[0],this.tip},i.setContent=function(){var t=e(this.getTipElement());this.setElementContent(t.find(".popover-header"),this.getTitle());var n=this._getContent();"function"==typeof n&&(n=n.call(this.element)),this.setElementContent(t.find(".popover-body"),n),t.removeClass("fade show")},i._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},i._cleanTipClass=function(){var t=e(this.getTipElement()),n=t.attr("class").match(ne);null!==n&&0<n.length&&t.removeClass(n.join(""))},n._jQueryInterface=function(t){return this.each((function(){var i=e(this).data(Gt),o="object"==typeof t?t:null;if((i||!/dispose|hide/.test(t))&&(i||(i=new n(this,o),e(this).data(Gt,i)),"string"==typeof t)){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}}))},o(n,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return ie}},{key:"NAME",get:function(){return Zt}},{key:"DATA_KEY",get:function(){return Gt}},{key:"Event",get:function(){return re}},{key:"EVENT_KEY",get:function(){return Jt}},{key:"DefaultType",get:function(){return oe}}]),n}(Kt);e.fn[Zt]=se._jQueryInterface,e.fn[Zt].Constructor=se,e.fn[Zt].noConflict=function(){return e.fn[Zt]=te,se._jQueryInterface};var ae="scrollspy",le="bs.scrollspy",ce="."+le,ue=e.fn[ae],he={offset:10,method:"auto",target:""},de={offset:"number",method:"string",target:"(string|element)"},pe={ACTIVATE:"activate"+ce,SCROLL:"scroll"+ce,LOAD_DATA_API:"load"+ce+".data-api"},fe="active",ge=".nav, .list-group",me=".nav-link",ve=".list-group-item",ye="position",be=function(){function t(t,n){var i=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(n),this._selector=this._config.target+" "+me+","+this._config.target+" "+ve+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,e(this._scrollElement).on(pe.SCROLL,(function(t){return i._process(t)})),this.refresh(),this._process()}var n=t.prototype;return n.refresh=function(){var t=this,n=this._scrollElement===this._scrollElement.window?"offset":ye,i="auto"===this._config.method?n:this._config.method,o=i===ye?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map((function(t){var n,r=l.getSelectorFromElement(t);if(r&&(n=document.querySelector(r)),n){var s=n.getBoundingClientRect();if(s.width||s.height)return[e(n)[i]().top+o,r]}return null})).filter((function(t){return t})).sort((function(t,e){return t[0]-e[0]})).forEach((function(e){t._offsets.push(e[0]),t._targets.push(e[1])}))},n.dispose=function(){e.removeData(this._element,le),e(this._scrollElement).off(ce),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},n._getConfig=function(t){if("string"!=typeof(t=s({},he,{},"object"==typeof t&&t?t:{})).target){var n=e(t.target).attr("id");n||(n=l.getUID(ae),e(t.target).attr("id",n)),t.target="#"+n}return l.typeCheckConfig(ae,t,de),t},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),n<=t){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;)this._activeTarget!==this._targets[o]&&t>=this._offsets[o]&&(void 0===this._offsets[o+1]||t<this._offsets[o+1])&&this._activate(this._targets[o])}},n._activate=function(t){this._activeTarget=t,this._clear();var n=this._selector.split(",").map((function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'})),i=e([].slice.call(document.querySelectorAll(n.join(","))));i.hasClass("dropdown-item")?(i.closest(".dropdown").find(".dropdown-toggle").addClass(fe),i.addClass(fe)):(i.addClass(fe),i.parents(ge).prev(me+", "+ve).addClass(fe),i.parents(ge).prev(".nav-item").children(me).addClass(fe)),e(this._scrollElement).trigger(pe.ACTIVATE,{relatedTarget:t})},n._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter((function(t){return t.classList.contains(fe)})).forEach((function(t){return t.classList.remove(fe)}))},t._jQueryInterface=function(n){return this.each((function(){var i=e(this).data(le);if(i||(i=new t(this,"object"==typeof n&&n),e(this).data(le,i)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return he}}]),t}();e(window).on(pe.LOAD_DATA_API,(function(){for(var t=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),n=t.length;n--;){var i=e(t[n]);be._jQueryInterface.call(i,i.data())}})),e.fn[ae]=be._jQueryInterface,e.fn[ae].Constructor=be,e.fn[ae].noConflict=function(){return e.fn[ae]=ue,be._jQueryInterface};var _e="bs.tab",we="."+_e,xe=e.fn.tab,Ce={HIDE:"hide"+we,HIDDEN:"hidden"+we,SHOW:"show"+we,SHOWN:"shown"+we,CLICK_DATA_API:"click"+we+".data-api"},Ee="active",Te="fade",Se="show",De=".active",Ae="> li > .active",Ie=function(){function t(t){this._element=t}var n=t.prototype;return n.show=function(){var t=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&e(this._element).hasClass(Ee)||e(this._element).hasClass("disabled"))){var n,i,o=e(this._element).closest(".nav, .list-group")[0],r=l.getSelectorFromElement(this._element);if(o){var s="UL"===o.nodeName||"OL"===o.nodeName?Ae:De;i=(i=e.makeArray(e(o).find(s)))[i.length-1]}var a=e.Event(Ce.HIDE,{relatedTarget:this._element}),c=e.Event(Ce.SHOW,{relatedTarget:i});if(i&&e(i).trigger(a),e(this._element).trigger(c),!c.isDefaultPrevented()&&!a.isDefaultPrevented()){r&&(n=document.querySelector(r)),this._activate(this._element,o);var u=function(){var n=e.Event(Ce.HIDDEN,{relatedTarget:t._element}),o=e.Event(Ce.SHOWN,{relatedTarget:i});e(i).trigger(n),e(t._element).trigger(o)};n?this._activate(n,n.parentNode,u):u()}}},n.dispose=function(){e.removeData(this._element,_e),this._element=null},n._activate=function(t,n,i){function o(){return r._transitionComplete(t,s,i)}var r=this,s=(!n||"UL"!==n.nodeName&&"OL"!==n.nodeName?e(n).children(De):e(n).find(Ae))[0],a=i&&s&&e(s).hasClass(Te);if(s&&a){var c=l.getTransitionDurationFromElement(s);e(s).removeClass(Se).one(l.TRANSITION_END,o).emulateTransitionEnd(c)}else o()},n._transitionComplete=function(t,n,i){if(n){e(n).removeClass(Ee);var o=e(n.parentNode).find("> .dropdown-menu .active")[0];o&&e(o).removeClass(Ee),"tab"===n.getAttribute("role")&&n.setAttribute("aria-selected",!1)}if(e(t).addClass(Ee),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),l.reflow(t),t.classList.contains(Te)&&t.classList.add(Se),t.parentNode&&e(t.parentNode).hasClass("dropdown-menu")){var r=e(t).closest(".dropdown")[0];if(r){var s=[].slice.call(r.querySelectorAll(".dropdown-toggle"));e(s).addClass(Ee)}t.setAttribute("aria-expanded",!0)}i&&i()},t._jQueryInterface=function(n){return this.each((function(){var i=e(this),o=i.data(_e);if(o||(o=new t(this),i.data(_e,o)),"string"==typeof n){if(void 0===o[n])throw new TypeError('No method named "'+n+'"');o[n]()}}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),t}();e(document).on(Ce.CLICK_DATA_API,'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',(function(t){t.preventDefault(),Ie._jQueryInterface.call(e(this),"show")})),e.fn.tab=Ie._jQueryInterface,e.fn.tab.Constructor=Ie,e.fn.tab.noConflict=function(){return e.fn.tab=xe,Ie._jQueryInterface};var Le="toast",ke="bs.toast",Oe="."+ke,Pe=e.fn[Le],Ne={CLICK_DISMISS:"click.dismiss"+Oe,HIDE:"hide"+Oe,HIDDEN:"hidden"+Oe,SHOW:"show"+Oe,SHOWN:"shown"+Oe},je="hide",$e="show",Me="showing",ze={animation:"boolean",autohide:"boolean",delay:"number"},He={animation:!0,autohide:!0,delay:500},Re=function(){function t(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}var n=t.prototype;return n.show=function(){var t=this,n=e.Event(Ne.SHOW);if(e(this._element).trigger(n),!n.isDefaultPrevented()){this._config.animation&&this._element.classList.add("fade");var i=function(){t._element.classList.remove(Me),t._element.classList.add($e),e(t._element).trigger(Ne.SHOWN),t._config.autohide&&(t._timeout=setTimeout((function(){t.hide()}),t._config.delay))};if(this._element.classList.remove(je),l.reflow(this._element),this._element.classList.add(Me),this._config.animation){var o=l.getTransitionDurationFromElement(this._element);e(this._element).one(l.TRANSITION_END,i).emulateTransitionEnd(o)}else i()}},n.hide=function(){if(this._element.classList.contains($e)){var t=e.Event(Ne.HIDE);e(this._element).trigger(t),t.isDefaultPrevented()||this._close()}},n.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains($e)&&this._element.classList.remove($e),e(this._element).off(Ne.CLICK_DISMISS),e.removeData(this._element,ke),this._element=null,this._config=null},n._getConfig=function(t){return t=s({},He,{},e(this._element).data(),{},"object"==typeof t&&t?t:{}),l.typeCheckConfig(Le,t,this.constructor.DefaultType),t},n._setListeners=function(){var t=this;e(this._element).on(Ne.CLICK_DISMISS,'[data-dismiss="toast"]',(function(){return t.hide()}))},n._close=function(){function t(){n._element.classList.add(je),e(n._element).trigger(Ne.HIDDEN)}var n=this;if(this._element.classList.remove($e),this._config.animation){var i=l.getTransitionDurationFromElement(this._element);e(this._element).one(l.TRANSITION_END,t).emulateTransitionEnd(i)}else t()},t._jQueryInterface=function(n){return this.each((function(){var i=e(this),o=i.data(ke);if(o||(o=new t(this,"object"==typeof n&&n),i.data(ke,o)),"string"==typeof n){if(void 0===o[n])throw new TypeError('No method named "'+n+'"');o[n](this)}}))},o(t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"DefaultType",get:function(){return ze}},{key:"Default",get:function(){return He}}]),t}();e.fn[Le]=Re._jQueryInterface,e.fn[Le].Constructor=Re,e.fn[Le].noConflict=function(){return e.fn[Le]=Pe,Re._jQueryInterface},t.Alert=f,t.Button=T,t.Carousel=H,t.Collapse=J,t.Dropdown=ft,t.Modal=At,t.Popover=se,t.Scrollspy=be,t.Tab=Ie,t.Toast=Re,t.Tooltip=Kt,t.Util=l,Object.defineProperty(t,"__esModule",{value:!0})})),function(t,e,n,i){function o(e,n){this.settings=null,this.options=t.extend({},o.Defaults,n),this.$element=t(e),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},t.each(["onResize","onThrottledResize"],t.proxy((function(e,n){this._handlers[n]=t.proxy(this[n],this)}),this)),t.each(o.Plugins,t.proxy((function(t,e){this._plugins[t.charAt(0).toLowerCase()+t.slice(1)]=new e(this)}),this)),t.each(o.Workers,t.proxy((function(e,n){this._pipe.push({filter:n.filter,run:t.proxy(n.run,this)})}),this)),this.setup(),this.initialize()}o.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:e,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},o.Width={Default:"default",Inner:"inner",Outer:"outer"},o.Type={Event:"event",State:"state"},o.Plugins={},o.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(t){t.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(t){var e=this.settings.margin||"",n=!this.settings.autoWidth,i=this.settings.rtl,o={width:"auto","margin-left":i?e:"","margin-right":i?"":e};!n&&this.$stage.children().css(o),t.css=o}},{filter:["width","items","settings"],run:function(t){var e=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,n=null,i=this._items.length,o=!this.settings.autoWidth,r=[];for(t.items={merge:!1,width:e};i--;)n=this._mergers[i],n=this.settings.mergeFit&&Math.min(n,this.settings.items)||n,t.items.merge=n>1||t.items.merge,r[i]=o?e*n:this._items[i].width();this._widths=r}},{filter:["items","settings"],run:function(){var e=[],n=this._items,i=this.settings,o=Math.max(2*i.items,4),r=2*Math.ceil(n.length/2),s=i.loop&&n.length?i.rewind?o:Math.max(o,r):0,a="",l="";for(s/=2;s>0;)e.push(this.normalize(e.length/2,!0)),a+=n[e[e.length-1]][0].outerHTML,e.push(this.normalize(n.length-1-(e.length-1)/2,!0)),l=n[e[e.length-1]][0].outerHTML+l,s-=1;this._clones=e,t(a).addClass("cloned").appendTo(this.$stage),t(l).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var t=this.settings.rtl?1:-1,e=this._clones.length+this._items.length,n=-1,i=0,o=0,r=[];++n<e;)i=r[n-1]||0,o=this._widths[this.relative(n)]+this.settings.margin,r.push(i+o*t);this._coordinates=r}},{filter:["width","items","settings"],run:function(){var t=this.settings.stagePadding,e=this._coordinates,n={width:Math.ceil(Math.abs(e[e.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.$stage.css(n)}},{filter:["width","items","settings"],run:function(t){var e=this._coordinates.length,n=!this.settings.autoWidth,i=this.$stage.children();if(n&&t.items.merge)for(;e--;)t.css.width=this._widths[this.relative(e)],i.eq(e).css(t.css);else n&&(t.css.width=t.items.width,i.css(t.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(t){t.current=t.current?this.$stage.children().index(t.current):0,t.current=Math.max(this.minimum(),Math.min(this.maximum(),t.current)),this.reset(t.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){var t,e,n,i,o=this.settings.rtl?1:-1,r=2*this.settings.stagePadding,s=this.coordinates(this.current())+r,a=s+this.width()*o,l=[];for(n=0,i=this._coordinates.length;n<i;n++)t=this._coordinates[n-1]||0,e=Math.abs(this._coordinates[n])+r*o,(this.op(t,"<=",s)&&this.op(t,">",a)||this.op(e,"<",s)&&this.op(e,">",a))&&l.push(n);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+l.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],o.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=t("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(t("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},o.prototype.initializeItems=function(){var e=this.$element.find(".owl-item");if(e.length)return this._items=e.get().map((function(e){return t(e)})),this._mergers=this._items.map((function(){return 1})),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)},o.prototype.initialize=function(){var t,e,n;(this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading"))&&(t=this.$element.find("img"),e=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:i,n=this.$element.children(e).width(),t.length&&n<=0&&this.preloadAutoWidthImages(t));this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},o.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},o.prototype.setup=function(){var e=this.viewport(),n=this.options.responsive,i=-1,o=null;n?(t.each(n,(function(t){t<=e&&t>i&&(i=Number(t))})),"function"==typeof(o=t.extend({},this.options,n[i])).stagePadding&&(o.stagePadding=o.stagePadding()),delete o.responsive,o.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):o=t.extend({},this.options),this.trigger("change",{property:{name:"settings",value:o}}),this._breakpoint=i,this.settings=o,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},o.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},o.prototype.prepare=function(e){var n=this.trigger("prepare",{content:e});return n.data||(n.data=t("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(e)),this.trigger("prepared",{content:n.data}),n.data},o.prototype.update=function(){for(var e=0,n=this._pipe.length,i=t.proxy((function(t){return this[t]}),this._invalidated),o={};e<n;)(this._invalidated.all||t.grep(this._pipe[e].filter,i).length>0)&&this._pipe[e].run(o),e++;this._invalidated={},!this.is("valid")&&this.enter("valid")},o.prototype.width=function(t){switch(t=t||o.Width.Default){case o.Width.Inner:case o.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},o.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},o.prototype.onThrottledResize=function(){e.clearTimeout(this.resizeTimer),this.resizeTimer=e.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},o.prototype.onResize=function(){return!!this._items.length&&(this._width!==this.$element.width()&&(!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))))},o.prototype.registerEventHandlers=function(){t.support.transition&&this.$stage.on(t.support.transition.end+".owl.core",t.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(e,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",t.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",(function(){return!1}))),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",t.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",t.proxy(this.onDragEnd,this)))},o.prototype.onDragStart=function(e){var i=null;3!==e.which&&(t.support.transform?i={x:(i=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","))[16===i.length?12:4],y:i[16===i.length?13:5]}:(i=this.$stage.position(),i={x:this.settings.rtl?i.left+this.$stage.width()-this.width()+this.settings.margin:i.left,y:i.top}),this.is("animating")&&(t.support.transform?this.animate(i.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===e.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=t(e.target),this._drag.stage.start=i,this._drag.stage.current=i,this._drag.pointer=this.pointer(e),t(n).on("mouseup.owl.core touchend.owl.core",t.proxy(this.onDragEnd,this)),t(n).one("mousemove.owl.core touchmove.owl.core",t.proxy((function(e){var i=this.difference(this._drag.pointer,this.pointer(e));t(n).on("mousemove.owl.core touchmove.owl.core",t.proxy(this.onDragMove,this)),Math.abs(i.x)<Math.abs(i.y)&&this.is("valid")||(e.preventDefault(),this.enter("dragging"),this.trigger("drag"))}),this)))},o.prototype.onDragMove=function(t){var e=null,n=null,i=null,o=this.difference(this._drag.pointer,this.pointer(t)),r=this.difference(this._drag.stage.start,o);this.is("dragging")&&(t.preventDefault(),this.settings.loop?(e=this.coordinates(this.minimum()),n=this.coordinates(this.maximum()+1)-e,r.x=((r.x-e)%n+n)%n+e):(e=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),n=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),i=this.settings.pullDrag?-1*o.x/5:0,r.x=Math.max(Math.min(r.x,e+i),n+i)),this._drag.stage.current=r,this.animate(r.x))},o.prototype.onDragEnd=function(e){var i=this.difference(this._drag.pointer,this.pointer(e)),o=this._drag.stage.current,r=i.x>0^this.settings.rtl?"left":"right";t(n).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==i.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(o.x,0!==i.x?r:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=r,(Math.abs(i.x)>3||(new Date).getTime()-this._drag.time>300)&&this._drag.target.one("click.owl.core",(function(){return!1}))),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},o.prototype.closest=function(e,n){var o=-1,r=this.width(),s=this.coordinates();return this.settings.freeDrag||t.each(s,t.proxy((function(t,a){return"left"===n&&e>a-30&&e<a+30?o=t:"right"===n&&e>a-r-30&&e<a-r+30?o=t+1:this.op(e,"<",a)&&this.op(e,">",s[t+1]!==i?s[t+1]:a-r)&&(o="left"===n?t+1:t),-1===o}),this)),this.settings.loop||(this.op(e,">",s[this.minimum()])?o=e=this.minimum():this.op(e,"<",s[this.maximum()])&&(o=e=this.maximum())),o},o.prototype.animate=function(e){var n=this.speed()>0;this.is("animating")&&this.onTransitionEnd(),n&&(this.enter("animating"),this.trigger("translate")),t.support.transform3d&&t.support.transition?this.$stage.css({transform:"translate3d("+e+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):n?this.$stage.animate({left:e+"px"},this.speed(),this.settings.fallbackEasing,t.proxy(this.onTransitionEnd,this)):this.$stage.css({left:e+"px"})},o.prototype.is=function(t){return this._states.current[t]&&this._states.current[t]>0},o.prototype.current=function(t){if(t===i)return this._current;if(0===this._items.length)return i;if(t=this.normalize(t),this._current!==t){var e=this.trigger("change",{property:{name:"position",value:t}});e.data!==i&&(t=this.normalize(e.data)),this._current=t,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current},o.prototype.invalidate=function(e){return"string"===t.type(e)&&(this._invalidated[e]=!0,this.is("valid")&&this.leave("valid")),t.map(this._invalidated,(function(t,e){return e}))},o.prototype.reset=function(t){(t=this.normalize(t))!==i&&(this._speed=0,this._current=t,this.suppress(["translate","translated"]),this.animate(this.coordinates(t)),this.release(["translate","translated"]))},o.prototype.normalize=function(t,e){var n=this._items.length,o=e?0:this._clones.length;return!this.isNumeric(t)||n<1?t=i:(t<0||t>=n+o)&&(t=((t-o/2)%n+n)%n+o/2),t},o.prototype.relative=function(t){return t-=this._clones.length/2,this.normalize(t,!0)},o.prototype.maximum=function(t){var e,n,i,o=this.settings,r=this._coordinates.length;if(o.loop)r=this._clones.length/2+this._items.length-1;else if(o.autoWidth||o.merge){if(e=this._items.length)for(n=this._items[--e].width(),i=this.$element.width();e--&&!((n+=this._items[e].width()+this.settings.margin)>i););r=e+1}else r=o.center?this._items.length-1:this._items.length-o.items;return t&&(r-=this._clones.length/2),Math.max(r,0)},o.prototype.minimum=function(t){return t?0:this._clones.length/2},o.prototype.items=function(t){return t===i?this._items.slice():(t=this.normalize(t,!0),this._items[t])},o.prototype.mergers=function(t){return t===i?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])},o.prototype.clones=function(e){var n=this._clones.length/2,o=n+this._items.length,r=function(t){return t%2==0?o+t/2:n-(t+1)/2};return e===i?t.map(this._clones,(function(t,e){return r(e)})):t.map(this._clones,(function(t,n){return t===e?r(n):null}))},o.prototype.speed=function(t){return t!==i&&(this._speed=t),this._speed},o.prototype.coordinates=function(e){var n,o=1,r=e-1;return e===i?t.map(this._coordinates,t.proxy((function(t,e){return this.coordinates(e)}),this)):(this.settings.center?(this.settings.rtl&&(o=-1,r=e+1),n=this._coordinates[e],n+=(this.width()-n+(this._coordinates[r]||0))/2*o):n=this._coordinates[r]||0,n=Math.ceil(n))},o.prototype.duration=function(t,e,n){return 0===n?0:Math.min(Math.max(Math.abs(e-t),1),6)*Math.abs(n||this.settings.smartSpeed)},o.prototype.to=function(t,e){var n=this.current(),i=null,o=t-this.relative(n),r=(o>0)-(o<0),s=this._items.length,a=this.minimum(),l=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(o)>s/2&&(o+=-1*r*s),(i=(((t=n+o)-a)%s+s)%s+a)!==t&&i-o<=l&&i-o>0&&(n=i-o,t=i,this.reset(n))):t=this.settings.rewind?(t%(l+=1)+l)%l:Math.max(a,Math.min(l,t)),this.speed(this.duration(n,t,e)),this.current(t),this.isVisible()&&this.update()},o.prototype.next=function(t){t=t||!1,this.to(this.relative(this.current())+1,t)},o.prototype.prev=function(t){t=t||!1,this.to(this.relative(this.current())-1,t)},o.prototype.onTransitionEnd=function(t){if(t!==i&&(t.stopPropagation(),(t.target||t.srcElement||t.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},o.prototype.viewport=function(){var i;return this.options.responsiveBaseElement!==e?i=t(this.options.responsiveBaseElement).width():e.innerWidth?i=e.innerWidth:n.documentElement&&n.documentElement.clientWidth?i=n.documentElement.clientWidth:console.warn("Can not detect viewport width."),i},o.prototype.replace=function(e){this.$stage.empty(),this._items=[],e&&(e=e instanceof jQuery?e:t(e)),this.settings.nestedItemSelector&&(e=e.find("."+this.settings.nestedItemSelector)),e.filter((function(){return 1===this.nodeType})).each(t.proxy((function(t,e){e=this.prepare(e),this.$stage.append(e),this._items.push(e),this._mergers.push(1*e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)}),this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},o.prototype.add=function(e,n){var o=this.relative(this._current);n=n===i?this._items.length:this.normalize(n,!0),e=e instanceof jQuery?e:t(e),this.trigger("add",{content:e,position:n}),e=this.prepare(e),0===this._items.length||n===this._items.length?(0===this._items.length&&this.$stage.append(e),0!==this._items.length&&this._items[n-1].after(e),this._items.push(e),this._mergers.push(1*e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[n].before(e),this._items.splice(n,0,e),this._mergers.splice(n,0,1*e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[o]&&this.reset(this._items[o].index()),this.invalidate("items"),this.trigger("added",{content:e,position:n})},o.prototype.remove=function(t){(t=this.normalize(t,!0))!==i&&(this.trigger("remove",{content:this._items[t],position:t}),this._items[t].remove(),this._items.splice(t,1),this._mergers.splice(t,1),this.invalidate("items"),this.trigger("removed",{content:null,position:t}))},o.prototype.preloadAutoWidthImages=function(e){e.each(t.proxy((function(e,n){this.enter("pre-loading"),n=t(n),t(new Image).one("load",t.proxy((function(t){n.attr("src",t.target.src),n.css("opacity",1),this.leave("pre-loading"),!this.is("pre-loading")&&!this.is("initializing")&&this.refresh()}),this)).attr("src",n.attr("src")||n.attr("data-src")||n.attr("data-src-retina"))}),this))},o.prototype.destroy=function(){for(var i in this.$element.off(".owl.core"),this.$stage.off(".owl.core"),t(n).off(".owl.core"),!1!==this.settings.responsive&&(e.clearTimeout(this.resizeTimer),this.off(e,"resize",this._handlers.onThrottledResize)),this._plugins)this._plugins[i].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},o.prototype.op=function(t,e,n){var i=this.settings.rtl;switch(e){case"<":return i?t>n:t<n;case">":return i?t<n:t>n;case">=":return i?t<=n:t>=n;case"<=":return i?t>=n:t<=n}},o.prototype.on=function(t,e,n,i){t.addEventListener?t.addEventListener(e,n,i):t.attachEvent&&t.attachEvent("on"+e,n)},o.prototype.off=function(t,e,n,i){t.removeEventListener?t.removeEventListener(e,n,i):t.detachEvent&&t.detachEvent("on"+e,n)},o.prototype.trigger=function(e,n,i,r,s){var a={item:{count:this._items.length,index:this.current()}},l=t.camelCase(t.grep(["on",e,i],(function(t){return t})).join("-").toLowerCase()),c=t.Event([e,"owl",i||"carousel"].join(".").toLowerCase(),t.extend({relatedTarget:this},a,n));return this._supress[e]||(t.each(this._plugins,(function(t,e){e.onTrigger&&e.onTrigger(c)})),this.register({type:o.Type.Event,name:e}),this.$element.trigger(c),this.settings&&"function"==typeof this.settings[l]&&this.settings[l].call(this,c)),c},o.prototype.enter=function(e){t.each([e].concat(this._states.tags[e]||[]),t.proxy((function(t,e){this._states.current[e]===i&&(this._states.current[e]=0),this._states.current[e]++}),this))},o.prototype.leave=function(e){t.each([e].concat(this._states.tags[e]||[]),t.proxy((function(t,e){this._states.current[e]--}),this))},o.prototype.register=function(e){if(e.type===o.Type.Event){if(t.event.special[e.name]||(t.event.special[e.name]={}),!t.event.special[e.name].owl){var n=t.event.special[e.name]._default;t.event.special[e.name]._default=function(t){return!n||!n.apply||t.namespace&&-1!==t.namespace.indexOf("owl")?t.namespace&&t.namespace.indexOf("owl")>-1:n.apply(this,arguments)},t.event.special[e.name].owl=!0}}else e.type===o.Type.State&&(this._states.tags[e.name]?this._states.tags[e.name]=this._states.tags[e.name].concat(e.tags):this._states.tags[e.name]=e.tags,this._states.tags[e.name]=t.grep(this._states.tags[e.name],t.proxy((function(n,i){return t.inArray(n,this._states.tags[e.name])===i}),this)))},o.prototype.suppress=function(e){t.each(e,t.proxy((function(t,e){this._supress[e]=!0}),this))},o.prototype.release=function(e){t.each(e,t.proxy((function(t,e){delete this._supress[e]}),this))},o.prototype.pointer=function(t){var n={x:null,y:null};return(t=(t=t.originalEvent||t||e.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(n.x=t.pageX,n.y=t.pageY):(n.x=t.clientX,n.y=t.clientY),n},o.prototype.isNumeric=function(t){return!isNaN(parseFloat(t))},o.prototype.difference=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},t.fn.owlCarousel=function(e){var n=Array.prototype.slice.call(arguments,1);return this.each((function(){var i=t(this),r=i.data("owl.carousel");r||(r=new o(this,"object"==typeof e&&e),i.data("owl.carousel",r),t.each(["next","prev","to","destroy","refresh","replace","add","remove"],(function(e,n){r.register({type:o.Type.Event,name:n}),r.$element.on(n+".owl.carousel.core",t.proxy((function(t){t.namespace&&t.relatedTarget!==this&&(this.suppress([n]),r[n].apply(this,[].slice.call(arguments,1)),this.release([n]))}),r))}))),"string"==typeof e&&"_"!==e.charAt(0)&&r[e].apply(r,n)}))},t.fn.owlCarousel.Constructor=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=function(e){this._core=e,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoRefresh&&this.watch()}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers)};o.Defaults={autoRefresh:!0,autoRefreshInterval:500},o.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=e.setInterval(t.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},o.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())},o.prototype.destroy=function(){var t,n;for(t in e.clearInterval(this._interval),this._handlers)this._core.$element.off(t,this._handlers[t]);for(n in Object.getOwnPropertyNames(this))"function"!=typeof this[n]&&(this[n]=null)},t.fn.owlCarousel.Constructor.Plugins.AutoRefresh=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=function(e){this._core=e,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":t.proxy((function(e){if(e.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(e.property&&"position"==e.property.name||"initialized"==e.type)){var n=this._core.settings,i=n.center&&Math.ceil(n.items/2)||n.items,o=n.center&&-1*i||0,r=(e.property&&undefined!==e.property.value?e.property.value:this._core.current())+o,s=this._core.clones().length,a=t.proxy((function(t,e){this.load(e)}),this);for(n.lazyLoadEager>0&&(i+=n.lazyLoadEager,n.loop&&(r-=n.lazyLoadEager,i++));o++<i;)this.load(s/2+this._core.relative(r)),s&&t.each(this._core.clones(this._core.relative(r)),a),r++}}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers)};o.Defaults={lazyLoad:!1,lazyLoadEager:0},o.prototype.load=function(n){var i=this._core.$stage.children().eq(n),o=i&&i.find(".owl-lazy");!o||t.inArray(i.get(0),this._loaded)>-1||(o.each(t.proxy((function(n,i){var o,r=t(i),s=e.devicePixelRatio>1&&r.attr("data-src-retina")||r.attr("data-src")||r.attr("data-srcset");this._core.trigger("load",{element:r,url:s},"lazy"),r.is("img")?r.one("load.owl.lazy",t.proxy((function(){r.css("opacity",1),this._core.trigger("loaded",{element:r,url:s},"lazy")}),this)).attr("src",s):r.is("source")?r.one("load.owl.lazy",t.proxy((function(){this._core.trigger("loaded",{element:r,url:s},"lazy")}),this)).attr("srcset",s):((o=new Image).onload=t.proxy((function(){r.css({"background-image":'url("'+s+'")',opacity:"1"}),this._core.trigger("loaded",{element:r,url:s},"lazy")}),this),o.src=s)}),this)),this._loaded.push(i.get(0)))},o.prototype.destroy=function(){var t,e;for(t in this.handlers)this._core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.Lazy=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=function(n){this._core=n,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoHeight&&this.update()}),this),"changed.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoHeight&&"position"===t.property.name&&this.update()}),this),"loaded.owl.lazy":t.proxy((function(t){t.namespace&&this._core.settings.autoHeight&&t.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var i=this;t(e).on("load",(function(){i._core.settings.autoHeight&&i.update()})),t(e).resize((function(){i._core.settings.autoHeight&&(null!=i._intervalId&&clearTimeout(i._intervalId),i._intervalId=setTimeout((function(){i.update()}),250))}))};o.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},o.prototype.update=function(){var e=this._core._current,n=e+this._core.settings.items,i=this._core.settings.lazyLoad,o=this._core.$stage.children().toArray().slice(e,n),r=[],s=0;t.each(o,(function(e,n){r.push(t(n).height())})),(s=Math.max.apply(null,r))<=1&&i&&this._previousHeight&&(s=this._previousHeight),this._previousHeight=s,this._core.$stage.parent().height(s).addClass(this._core.settings.autoHeightClass)},o.prototype.destroy=function(){var t,e;for(t in this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.AutoHeight=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=function(e){this._core=e,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":t.proxy((function(t){t.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})}),this),"resize.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.video&&this.isInFullScreen()&&t.preventDefault()}),this),"refreshed.owl.carousel":t.proxy((function(t){t.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()}),this),"changed.owl.carousel":t.proxy((function(t){t.namespace&&"position"===t.property.name&&this._playing&&this.stop()}),this),"prepared.owl.carousel":t.proxy((function(e){if(e.namespace){var n=t(e.content).find(".owl-video");n.length&&(n.css("display","none"),this.fetch(n,t(e.content)))}}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",t.proxy((function(t){this.play(t)}),this))};o.Defaults={video:!1,videoHeight:!1,videoWidth:!1},o.prototype.fetch=function(t,e){var n=t.attr("data-vimeo-id")?"vimeo":t.attr("data-vzaar-id")?"vzaar":"youtube",i=t.attr("data-vimeo-id")||t.attr("data-youtube-id")||t.attr("data-vzaar-id"),o=t.attr("data-width")||this._core.settings.videoWidth,r=t.attr("data-height")||this._core.settings.videoHeight,s=t.attr("href");if(!s)throw new Error("Missing video URL.");if((i=s.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/))[3].indexOf("youtu")>-1)n="youtube";else if(i[3].indexOf("vimeo")>-1)n="vimeo";else{if(!(i[3].indexOf("vzaar")>-1))throw new Error("Video URL not supported.");n="vzaar"}i=i[6],this._videos[s]={type:n,id:i,width:o,height:r},e.attr("data-video",s),this.thumbnail(t,this._videos[s])},o.prototype.thumbnail=function(e,n){var i,o,r=n.width&&n.height?"width:"+n.width+"px;height:"+n.height+"px;":"",s=e.find("img"),a="src",l="",c=this._core.settings,u=function(n){'<div class="owl-video-play-icon"></div>',i=c.lazyLoad?t("<div/>",{class:"owl-video-tn "+l,srcType:n}):t("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+n+")"}),e.after(i),e.after('<div class="owl-video-play-icon"></div>')};if(e.wrap(t("<div/>",{class:"owl-video-wrapper",style:r})),this._core.settings.lazyLoad&&(a="data-src",l="owl-lazy"),s.length)return u(s.attr(a)),s.remove(),!1;"youtube"===n.type?(o="//img.youtube.com/vi/"+n.id+"/hqdefault.jpg",u(o)):"vimeo"===n.type?t.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+n.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t[0].thumbnail_large,u(o)}}):"vzaar"===n.type&&t.ajax({type:"GET",url:"//vzaar.com/api/videos/"+n.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t.framegrab_url,u(o)}})},o.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},o.prototype.play=function(e){var n,i=t(e.target).closest("."+this._core.settings.itemClass),o=this._videos[i.attr("data-video")],r=o.width||"100%",s=o.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),i=this._core.items(this._core.relative(i.index())),this._core.reset(i.index()),(n=t('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>')).attr("height",s),n.attr("width",r),"youtube"===o.type?n.attr("src","//www.youtube.com/embed/"+o.id+"?autoplay=1&rel=0&v="+o.id):"vimeo"===o.type?n.attr("src","//player.vimeo.com/video/"+o.id+"?autoplay=1"):"vzaar"===o.type&&n.attr("src","//view.vzaar.com/"+o.id+"/player?autoplay=true"),t(n).wrap('<div class="owl-video-frame" />').insertAfter(i.find(".owl-video")),this._playing=i.addClass("owl-video-playing"))},o.prototype.isInFullScreen=function(){var e=n.fullscreenElement||n.mozFullScreenElement||n.webkitFullscreenElement;return e&&t(e).parent().hasClass("owl-video-frame")},o.prototype.destroy=function(){var t,e;for(t in this._core.$element.off("click.owl.video"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.Video=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=function(e){this.core=e,this.core.options=t.extend({},o.Defaults,this.core.options),this.swapping=!0,this.previous=i,this.next=i,this.handlers={"change.owl.carousel":t.proxy((function(t){t.namespace&&"position"==t.property.name&&(this.previous=this.core.current(),this.next=t.property.value)}),this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":t.proxy((function(t){t.namespace&&(this.swapping="translated"==t.type)}),this),"translate.owl.carousel":t.proxy((function(t){t.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()}),this)},this.core.$element.on(this.handlers)};o.Defaults={animateOut:!1,animateIn:!1},o.prototype.swap=function(){if(1===this.core.settings.items&&t.support.animation&&t.support.transition){this.core.speed(0);var e,n=t.proxy(this.clear,this),i=this.core.$stage.children().eq(this.previous),o=this.core.$stage.children().eq(this.next),r=this.core.settings.animateIn,s=this.core.settings.animateOut;this.core.current()!==this.previous&&(s&&(e=this.core.coordinates(this.previous)-this.core.coordinates(this.next),i.one(t.support.animation.end,n).css({left:e+"px"}).addClass("animated owl-animated-out").addClass(s)),r&&o.one(t.support.animation.end,n).addClass("animated owl-animated-in").addClass(r))}},o.prototype.clear=function(e){t(e.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},o.prototype.destroy=function(){var t,e;for(t in this.handlers)this.core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.Animate=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=function(e){this._core=e,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":t.proxy((function(t){t.namespace&&"settings"===t.property.name?this._core.settings.autoplay?this.play():this.stop():t.namespace&&"position"===t.property.name&&this._paused&&(this._time=0)}),this),"initialized.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.autoplay&&this.play()}),this),"play.owl.autoplay":t.proxy((function(t,e,n){t.namespace&&this.play(e,n)}),this),"stop.owl.autoplay":t.proxy((function(t){t.namespace&&this.stop()}),this),"mouseover.owl.autoplay":t.proxy((function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()}),this),"mouseleave.owl.autoplay":t.proxy((function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()}),this),"touchstart.owl.core":t.proxy((function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()}),this),"touchend.owl.core":t.proxy((function(){this._core.settings.autoplayHoverPause&&this.play()}),this)},this._core.$element.on(this._handlers),this._core.options=t.extend({},o.Defaults,this._core.options)};o.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},o.prototype._next=function(i){this._call=e.setTimeout(t.proxy(this._next,this,i),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||n.hidden||this._core.next(i||this._core.settings.autoplaySpeed)},o.prototype.read=function(){return(new Date).getTime()-this._time},o.prototype.play=function(n,i){var o;this._core.is("rotating")||this._core.enter("rotating"),n=n||this._core.settings.autoplayTimeout,o=Math.min(this._time%(this._timeout||n),n),this._paused?(this._time=this.read(),this._paused=!1):e.clearTimeout(this._call),this._time+=this.read()%n-o,this._timeout=n,this._call=e.setTimeout(t.proxy(this._next,this,i),n-o)},o.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,e.clearTimeout(this._call),this._core.leave("rotating"))},o.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,e.clearTimeout(this._call))},o.prototype.destroy=function(){var t,e;for(t in this.stop(),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},t.fn.owlCarousel.Constructor.Plugins.autoplay=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){"use strict";var o=function(e){this._core=e,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":t.proxy((function(e){e.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+t(e.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")}),this),"added.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,0,this._templates.pop())}),this),"remove.owl.carousel":t.proxy((function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,1)}),this),"changed.owl.carousel":t.proxy((function(t){t.namespace&&"position"==t.property.name&&this.draw()}),this),"initialized.owl.carousel":t.proxy((function(t){t.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))}),this),"refreshed.owl.carousel":t.proxy((function(t){t.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this.$element.on(this._handlers)};o.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},o.prototype.initialize=function(){var e,n=this._core.settings;for(e in this._controls.$relative=(n.navContainer?t(n.navContainer):t("<div>").addClass(n.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=t("<"+n.navElement+">").addClass(n.navClass[0]).html(n.navText[0]).prependTo(this._controls.$relative).on("click",t.proxy((function(t){this.prev(n.navSpeed)}),this)),this._controls.$next=t("<"+n.navElement+">").addClass(n.navClass[1]).html(n.navText[1]).appendTo(this._controls.$relative).on("click",t.proxy((function(t){this.next(n.navSpeed)}),this)),n.dotsData||(this._templates=[t('<button role="button">').addClass(n.dotClass).append(t("<span>")).prop("outerHTML")]),this._controls.$absolute=(n.dotsContainer?t(n.dotsContainer):t("<div>").addClass(n.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",t.proxy((function(e){var i=t(e.target).parent().is(this._controls.$absolute)?t(e.target).index():t(e.target).parent().index();e.preventDefault(),this.to(i,n.dotsSpeed)}),this)),this._overrides)this._core[e]=t.proxy(this[e],this)},o.prototype.destroy=function(){var t,e,n,i,o;for(t in o=this._core.settings,this._handlers)this.$element.off(t,this._handlers[t]);for(e in this._controls)"$relative"===e&&o.navContainer?this._controls[e].html(""):this._controls[e].remove();for(i in this.overides)this._core[i]=this._overrides[i];for(n in Object.getOwnPropertyNames(this))"function"!=typeof this[n]&&(this[n]=null)},o.prototype.update=function(){var t,e,n=this._core.clones().length/2,i=n+this._core.items().length,o=this._core.maximum(!0),r=this._core.settings,s=r.center||r.autoWidth||r.dotsData?1:r.dotsEach||r.items;if("page"!==r.slideBy&&(r.slideBy=Math.min(r.slideBy,r.items)),r.dots||"page"==r.slideBy)for(this._pages=[],t=n,e=0,0;t<i;t++){if(e>=s||0===e){if(this._pages.push({start:Math.min(o,t-n),end:t-n+s-1}),Math.min(o,t-n)===o)break;e=0}e+=this._core.mergers(this._core.relative(t))}},o.prototype.draw=function(){var e,n=this._core.settings,i=this._core.items().length<=n.items,o=this._core.relative(this._core.current()),r=n.loop||n.rewind;this._controls.$relative.toggleClass("disabled",!n.nav||i),n.nav&&(this._controls.$previous.toggleClass("disabled",!r&&o<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!r&&o>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!n.dots||i),n.dots&&(e=this._pages.length-this._controls.$absolute.children().length,n.dotsData&&0!==e?this._controls.$absolute.html(this._templates.join("")):e>0?this._controls.$absolute.append(new Array(e+1).join(this._templates[0])):e<0&&this._controls.$absolute.children().slice(e).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(t.inArray(this.current(),this._pages)).addClass("active"))},o.prototype.onTrigger=function(e){var n=this._core.settings;e.page={index:t.inArray(this.current(),this._pages),count:this._pages.length,size:n&&(n.center||n.autoWidth||n.dotsData?1:n.dotsEach||n.items)}},o.prototype.current=function(){var e=this._core.relative(this._core.current());return t.grep(this._pages,t.proxy((function(t,n){return t.start<=e&&t.end>=e}),this)).pop()},o.prototype.getPosition=function(e){var n,i,o=this._core.settings;return"page"==o.slideBy?(n=t.inArray(this.current(),this._pages),i=this._pages.length,e?++n:--n,n=this._pages[(n%i+i)%i].start):(n=this._core.relative(this._core.current()),i=this._core.items().length,e?n+=o.slideBy:n-=o.slideBy),n},o.prototype.next=function(e){t.proxy(this._overrides.to,this._core)(this.getPosition(!0),e)},o.prototype.prev=function(e){t.proxy(this._overrides.to,this._core)(this.getPosition(!1),e)},o.prototype.to=function(e,n,i){var o;!i&&this._pages.length?(o=this._pages.length,t.proxy(this._overrides.to,this._core)(this._pages[(e%o+o)%o].start,n)):t.proxy(this._overrides.to,this._core)(e,n)},t.fn.owlCarousel.Constructor.Plugins.Navigation=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){"use strict";var o=function(n){this._core=n,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":t.proxy((function(n){n.namespace&&"URLHash"===this._core.settings.startPosition&&t(e).trigger("hashchange.owl.navigation")}),this),"prepared.owl.carousel":t.proxy((function(e){if(e.namespace){var n=t(e.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!n)return;this._hashes[n]=e.content}}),this),"changed.owl.carousel":t.proxy((function(n){if(n.namespace&&"position"===n.property.name){var i=this._core.items(this._core.relative(this._core.current())),o=t.map(this._hashes,(function(t,e){return t===i?e:null})).join();if(!o||e.location.hash.slice(1)===o)return;e.location.hash=o}}),this)},this._core.options=t.extend({},o.Defaults,this._core.options),this.$element.on(this._handlers),t(e).on("hashchange.owl.navigation",t.proxy((function(t){var n=e.location.hash.substring(1),i=this._core.$stage.children(),o=this._hashes[n]&&i.index(this._hashes[n]);undefined!==o&&o!==this._core.current()&&this._core.to(this._core.relative(o),!1,!0)}),this))};o.Defaults={URLhashListener:!1},o.prototype.destroy=function(){var n,i;for(n in t(e).off("hashchange.owl.navigation"),this._handlers)this._core.$element.off(n,this._handlers[n]);for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},t.fn.owlCarousel.Constructor.Plugins.Hash=o}(window.Zepto||window.jQuery,window,document),function(t,e,n,i){var o=t("<support>").get(0).style,r="Webkit Moz O ms".split(" "),s={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},a=function(){return!!u("transform")},l=function(){return!!u("perspective")},c=function(){return!!u("animation")};function u(e,n){var i=!1,s=e.charAt(0).toUpperCase()+e.slice(1);return t.each((e+" "+r.join(s+" ")+s).split(" "),(function(t,e){if(undefined!==o[e])return i=!n||e,!1})),i}function h(t){return u(t,!0)}(function(){return!!u("transition")})()&&(t.support.transition=new String(h("transition")),t.support.transition.end=s.transition.end[t.support.transition]),c()&&(t.support.animation=new String(h("animation")),t.support.animation.end=s.animation.end[t.support.animation]),a()&&(t.support.transform=new String(h("transform")),t.support.transform3d=l())}(window.Zepto||window.jQuery,window,document),function(){var t,e,n,i=function(t,e){return function(){return t.apply(e,arguments)}},o=[].indexOf||function(t){for(var e=0,n=this.length;n>e;e++)if(e in this&&this[e]===t)return e;return-1};e=function(){function t(){}return t.prototype.extend=function(t,e){var n,i;for(n in e)i=e[n],null==t[n]&&(t[n]=i);return t},t.prototype.isMobile=function(t){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)},t}(),n=this.WeakMap||this.MozWeakMap||(n=function(){function t(){this.keys=[],this.values=[]}return t.prototype.get=function(t){var e,n,i,o;for(e=n=0,i=(o=this.keys).length;i>n;e=++n)if(o[e]===t)return this.values[e]},t.prototype.set=function(t,e){var n,i,o,r;for(n=i=0,o=(r=this.keys).length;o>i;n=++i)if(r[n]===t)return void(this.values[n]=e);return this.keys.push(t),this.values.push(e)},t}()),t=this.MutationObserver||this.WebkitMutationObserver||this.MozMutationObserver||(t=function(){function t(){console.warn("MutationObserver is not supported by your browser."),console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content.")}return t.notSupported=!0,t.prototype.observe=function(){},t}()),this.WOW=function(){function r(t){null==t&&(t={}),this.scrollCallback=i(this.scrollCallback,this),this.scrollHandler=i(this.scrollHandler,this),this.start=i(this.start,this),this.scrolled=!0,this.config=this.util().extend(t,this.defaults),this.animationNameCache=new n}return r.prototype.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0},r.prototype.init=function(){var t;return this.element=window.document.documentElement,"interactive"===(t=document.readyState)||"complete"===t?this.start():document.addEventListener("DOMContentLoaded",this.start),this.finished=[]},r.prototype.start=function(){var e,n,i,o;if(this.stopped=!1,this.boxes=function(){var t,n,i,o;for(o=[],t=0,n=(i=this.element.querySelectorAll("."+this.config.boxClass)).length;n>t;t++)e=i[t],o.push(e);return o}.call(this),this.all=function(){var t,n,i,o;for(o=[],t=0,n=(i=this.boxes).length;n>t;t++)e=i[t],o.push(e);return o}.call(this),this.boxes.length)if(this.disabled())this.resetStyle();else{for(n=0,i=(o=this.boxes).length;i>n;n++)e=o[n],this.applyStyle(e,!0);window.addEventListener("scroll",this.scrollHandler,!1),window.addEventListener("resize",this.scrollHandler,!1),this.interval=setInterval(this.scrollCallback,50)}return this.config.live?new t(function(t){return function(e){var n,i,o,r,s;for(s=[],o=0,r=e.length;r>o;o++)i=e[o],s.push(function(){var t,e,o,r;for(r=[],t=0,e=(o=i.addedNodes||[]).length;e>t;t++)n=o[t],r.push(this.doSync(n));return r}.call(t));return s}}(this)).observe(document.body,{childList:!0,subtree:!0}):void 0},r.prototype.stop=function(){return this.stopped=!0,window.removeEventListener("scroll",this.scrollHandler,!1),window.removeEventListener("resize",this.scrollHandler,!1),null!=this.interval?clearInterval(this.interval):void 0},r.prototype.sync=function(){return t.notSupported?this.doSync(this.element):void 0},r.prototype.doSync=function(t){var e,n,i,r,s;if(!this.stopped){if(null==t&&(t=this.element),1!==t.nodeType)return;for(s=[],n=0,i=(r=(t=t.parentNode||t).querySelectorAll("."+this.config.boxClass)).length;i>n;n++)e=r[n],o.call(this.all,e)<0?(this.applyStyle(e,!0),this.boxes.push(e),this.all.push(e),s.push(this.scrolled=!0)):s.push(void 0);return s}},r.prototype.show=function(t){return this.applyStyle(t),t.className=t.className+" "+this.config.animateClass},r.prototype.applyStyle=function(t,e){var n,i,o;return i=t.getAttribute("data-wow-duration"),n=t.getAttribute("data-wow-delay"),o=t.getAttribute("data-wow-iteration"),this.animate(function(r){return function(){return r.customStyle(t,e,i,n,o)}}(this))},r.prototype.animate="requestAnimationFrame"in window?function(t){return window.requestAnimationFrame(t)}:function(t){return t()},r.prototype.resetStyle=function(){var t,e,n,i,o;for(o=[],e=0,n=(i=this.boxes).length;n>e;e++)t=i[e],o.push(t.setAttribute("style","visibility: visible;"));return o},r.prototype.customStyle=function(t,e,n,i,o){return e&&this.cacheAnimationName(t),t.style.visibility=e?"hidden":"visible",n&&this.vendorSet(t.style,{animationDuration:n}),i&&this.vendorSet(t.style,{animationDelay:i}),o&&this.vendorSet(t.style,{animationIterationCount:o}),this.vendorSet(t.style,{animationName:e?"none":this.cachedAnimationName(t)}),t},r.prototype.vendors=["moz","webkit"],r.prototype.vendorSet=function(t,e){var n,i,o,r;for(n in r=[],e)i=e[n],t[""+n]=i,r.push(function(){var e,r,s,a;for(a=[],e=0,r=(s=this.vendors).length;r>e;e++)o=s[e],a.push(t[""+o+n.charAt(0).toUpperCase()+n.substr(1)]=i);return a}.call(this));return r},r.prototype.vendorCSS=function(t,e){var n,i,o,r,s,a;for(n=(i=window.getComputedStyle(t)).getPropertyCSSValue(e),r=0,s=(a=this.vendors).length;s>r;r++)o=a[r],n=n||i.getPropertyCSSValue("-"+o+"-"+e);return n},r.prototype.animationName=function(t){var e;try{e=this.vendorCSS(t,"animation-name").cssText}catch(n){e=window.getComputedStyle(t).getPropertyValue("animation-name")}return"none"===e?"":e},r.prototype.cacheAnimationName=function(t){return this.animationNameCache.set(t,this.animationName(t))},r.prototype.cachedAnimationName=function(t){return this.animationNameCache.get(t)},r.prototype.scrollHandler=function(){return this.scrolled=!0},r.prototype.scrollCallback=function(){var t;return!this.scrolled||(this.scrolled=!1,this.boxes=function(){var e,n,i,o;for(o=[],e=0,n=(i=this.boxes).length;n>e;e++)(t=i[e])&&(this.isVisible(t)?this.show(t):o.push(t));return o}.call(this),this.boxes.length||this.config.live)?void 0:this.stop()},r.prototype.offsetTop=function(t){for(var e;void 0===t.offsetTop;)t=t.parentNode;for(e=t.offsetTop;t=t.offsetParent;)e+=t.offsetTop;return e},r.prototype.isVisible=function(t){var e,n,i,o,r;return n=t.getAttribute("data-wow-offset")||this.config.offset,o=(r=window.pageYOffset)+Math.min(this.element.clientHeight,innerHeight)-n,e=(i=this.offsetTop(t))+t.clientHeight,o>=i&&e>=r},r.prototype.util=function(){return null!=this._util?this._util:this._util=new e},r.prototype.disabled=function(){return!this.config.mobile&&this.util().isMobile(navigator.userAgent)},r}()}.call(this),function(t,e,n,i){"use strict";if(n)if(n.fn.fancybox)"console"in t&&console.log("fancyBox already initialized");else{var o,r={loop:!1,margin:[44,0],gutter:50,keyboard:!0,arrows:!0,infobar:!0,toolbar:!0,buttons:["slideShow","fullScreen","thumbs","share","close"],idleTime:3,smallBtn:"auto",protect:!1,modal:!1,image:{preload:"auto"},ajax:{settings:{data:{fancybox:!0}}},iframe:{tpl:'<iframe id="fancybox-frame{rnd}" name="fancybox-frame{rnd}" class="fancybox-iframe" frameborder="0" vspace="0" hspace="0" webkitAllowFullScreen mozallowfullscreen allowFullScreen allowtransparency="true" src=""></iframe>',preload:!0,css:{},attr:{scrolling:"auto"}},defaultType:"image",animationEffect:"zoom",animationDuration:500,zoomOpacity:"auto",transitionEffect:"fade",transitionDuration:366,slideClass:"",baseClass:"",baseTpl:'<div class="fancybox-container" role="dialog" tabindex="-1"><div class="fancybox-bg"></div><div class="fancybox-inner"><div class="fancybox-infobar"><span data-fancybox-index></span>&nbsp;/&nbsp;<span data-fancybox-count></span></div><div class="fancybox-toolbar">{{buttons}}</div><div class="fancybox-navigation">{{arrows}}</div><div class="fancybox-stage"></div><div class="fancybox-caption-wrap"><div class="fancybox-caption"></div></div></div></div>',spinnerTpl:'<div class="fancybox-loading"></div>',errorTpl:'<div class="fancybox-error"><p>{{ERROR}}<p></div>',btnTpl:{download:'<a download data-fancybox-download class="fancybox-button fancybox-button--download" title="{{DOWNLOAD}}"><svg viewBox="0 0 40 40"><path d="M20,23 L20,8 L20,23 L13,16 L20,23 L27,16 L20,23 M26,28 L13,28 L27,28 L14,28" /></svg></a>',zoom:'<button data-fancybox-zoom class="fancybox-button fancybox-button--zoom" title="{{ZOOM}}"><svg viewBox="0 0 40 40"><path d="M 18,17 m-8,0 a 8,8 0 1,0 16,0 a 8,8 0 1,0 -16,0 M25,23 L31,29 L25,23" /></svg></button>',close:'<button data-fancybox-close class="fancybox-button fancybox-button--close" title="{{CLOSE}}"><svg viewBox="0 0 40 40"><path d="M10,10 L30,30 M30,10 L10,30" /></svg></button>',smallBtn:'<button data-fancybox-close class="fancybox-close-small" title="{{CLOSE}}"></button>',arrowLeft:'<button data-fancybox-prev class="fancybox-button fancybox-button--arrow_left" title="{{PREV}}"><svg viewBox="0 0 40 40"><path d="M10,20 L30,20 L10,20 L18,28 L10,20 L18,12 L10,20"></path></svg></button>',arrowRight:'<button data-fancybox-next class="fancybox-button fancybox-button--arrow_right" title="{{NEXT}}"><svg viewBox="0 0 40 40"><path d="M30,20 L10,20 L30,20 L22,28 L30,20 L22,12 L30,20"></path></svg></button>'},parentEl:"body",autoFocus:!1,backFocus:!0,trapFocus:!0,fullScreen:{autoStart:!1},touch:{vertical:!0,momentum:!0},hash:null,media:{},slideShow:{autoStart:!1,speed:4e3},thumbs:{autoStart:!1,hideOnClose:!0,parentEl:".fancybox-container",axis:"y"},wheel:"auto",onInit:n.noop,beforeLoad:n.noop,afterLoad:n.noop,beforeShow:n.noop,afterShow:n.noop,beforeClose:n.noop,afterClose:n.noop,onActivate:n.noop,onDeactivate:n.noop,clickContent:function(t,e){return"image"===t.type&&"zoom"},clickSlide:"close",clickOutside:"close",dblclickContent:!1,dblclickSlide:!1,dblclickOutside:!1,mobile:{idleTime:!1,margin:0,clickContent:function(t,e){return"image"===t.type&&"toggleControls"},clickSlide:function(t,e){return"image"===t.type?"toggleControls":"close"},dblclickContent:function(t,e){return"image"===t.type&&"zoom"},dblclickSlide:function(t,e){return"image"===t.type&&"zoom"}},lang:"en",i18n:{en:{CLOSE:"Close",NEXT:"Next",PREV:"Previous",ERROR:"The requested content cannot be loaded. <br/> Please try again later.",PLAY_START:"Start slideshow",PLAY_STOP:"Pause slideshow",FULL_SCREEN:"Full screen",THUMBS:"Thumbnails",DOWNLOAD:"Download",SHARE:"Share",ZOOM:"Zoom"},de:{CLOSE:"Schliessen",NEXT:"Weiter",PREV:"Zurück",ERROR:"Die angeforderten Daten konnten nicht geladen werden. <br/> Bitte versuchen Sie es später nochmal.",PLAY_START:"Diaschau starten",PLAY_STOP:"Diaschau beenden",FULL_SCREEN:"Vollbild",THUMBS:"Vorschaubilder",DOWNLOAD:"Herunterladen",SHARE:"Teilen",ZOOM:"Maßstab"}}},s=n(t),a=n(e),l=0,c=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||function(e){return t.setTimeout(e,1e3/60)},u=function(){var t,n=e.createElement("fakeelement"),o={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(t in o)if(n.style[t]!==i)return o[t];return"transitionend"}(),h=function(t){return t&&t.length&&t[0].offsetHeight},d=function(t,i,o){var r=this;r.opts=n.extend(!0,{index:o},n.fancybox.defaults,i||{}),n.fancybox.isMobile&&(r.opts=n.extend(!0,{},r.opts,r.opts.mobile)),i&&n.isArray(i.buttons)&&(r.opts.buttons=i.buttons),r.id=r.opts.id||++l,r.group=[],r.currIndex=parseInt(r.opts.index,10)||0,r.prevIndex=null,r.prevPos=null,r.currPos=0,r.firstRun=null,r.createGroup(t),r.group.length&&(r.$lastFocus=n(e.activeElement).blur(),r.slides={},r.init())};n.extend(d.prototype,{init:function(){var o,r,s,l=this,c=l.group[l.currIndex],u=c.opts,h=n.fancybox.scrollbarWidth;l.scrollTop=a.scrollTop(),l.scrollLeft=a.scrollLeft(),n.fancybox.getInstance()||(n("body").addClass("fancybox-active"),/iPad|iPhone|iPod/.test(navigator.userAgent)&&!t.MSStream?"image"!==c.type&&n("body").css("top",-1*n("body").scrollTop()).addClass("fancybox-iosfix"):!n.fancybox.isMobile&&e.body.scrollHeight>t.innerHeight&&(h===i&&(o=n('<div style="width:50px;height:50px;overflow:scroll;" />').appendTo("body"),h=n.fancybox.scrollbarWidth=o[0].offsetWidth-o[0].clientWidth,o.remove()),n("head").append('<style id="fancybox-style-noscroll" type="text/css">.compensate-for-scrollbar { margin-right: '+h+"px; }</style>"),n("body").addClass("compensate-for-scrollbar"))),s="",n.each(u.buttons,(function(t,e){s+=u.btnTpl[e]||""})),r=n(l.translate(l,u.baseTpl.replace("{{buttons}}",s).replace("{{arrows}}",u.btnTpl.arrowLeft+u.btnTpl.arrowRight))).attr("id","fancybox-container-"+l.id).addClass("fancybox-is-hidden").addClass(u.baseClass).data("FancyBox",l).appendTo(u.parentEl),l.$refs={container:r},["bg","inner","infobar","toolbar","stage","caption","navigation"].forEach((function(t){l.$refs[t]=r.find(".fancybox-"+t)})),l.trigger("onInit"),l.activate(),l.jumpTo(l.currIndex)},translate:function(t,e){var n=t.opts.i18n[t.opts.lang];return e.replace(/\{\{(\w+)\}\}/g,(function(t,e){var o=n[e];return o===i?t:o}))},createGroup:function(t){var e=this,o=n.makeArray(t);n.each(o,(function(t,o){var r,s,a,l,c,u={},h={};n.isPlainObject(o)?(u=o,h=o.opts||o):"object"===n.type(o)&&n(o).length?(h=(r=n(o)).data(),(h=n.extend({},h,h.options||{})).$orig=r,u.src=h.src||r.attr("href"),u.type||u.src||(u.type="inline",u.src=o)):u={type:"html",src:o+""},u.opts=n.extend(!0,{},e.opts,h),n.isArray(h.buttons)&&(u.opts.buttons=h.buttons),s=u.type||u.opts.type,l=u.src||"",!s&&l&&(l.match(/(^data:image\/[a-z0-9+\/=]*,)|(\.(jp(e|g|eg)|gif|png|bmp|webp|svg|ico)((\?|#).*)?$)/i)?s="image":l.match(/\.(pdf)((\?|#).*)?$/i)?s="pdf":(a=l.match(/\.(mp4|mov|ogv)((\?|#).*)?$/i))?(s="video",u.opts.videoFormat||(u.opts.videoFormat="video/"+("ogv"===a[1]?"ogg":a[1]))):"#"===l.charAt(0)&&(s="inline")),s?u.type=s:e.trigger("objectNeedsType",u),u.index=e.group.length,u.opts.$orig&&!u.opts.$orig.length&&delete u.opts.$orig,!u.opts.$thumb&&u.opts.$orig&&(u.opts.$thumb=u.opts.$orig.find("img:first")),u.opts.$thumb&&!u.opts.$thumb.length&&delete u.opts.$thumb,"function"===n.type(u.opts.caption)&&(u.opts.caption=u.opts.caption.apply(o,[e,u])),"function"===n.type(e.opts.caption)&&(u.opts.caption=e.opts.caption.apply(o,[e,u])),u.opts.caption instanceof n||(u.opts.caption=u.opts.caption===i?"":u.opts.caption+""),"ajax"===s&&(c=l.split(/\s+/,2)).length>1&&(u.src=c.shift(),u.opts.filter=c.shift()),"auto"==u.opts.smallBtn&&(n.inArray(s,["html","inline","ajax"])>-1?(u.opts.toolbar=!1,u.opts.smallBtn=!0):u.opts.smallBtn=!1),"pdf"===s&&(u.type="iframe",u.opts.iframe.preload=!1),u.opts.modal&&(u.opts=n.extend(!0,u.opts,{infobar:0,toolbar:0,smallBtn:0,keyboard:0,slideShow:0,fullScreen:0,thumbs:0,touch:0,clickContent:!1,clickSlide:!1,clickOutside:!1,dblclickContent:!1,dblclickSlide:!1,dblclickOutside:!1})),e.group.push(u)}))},addEvents:function(){var i=this;i.removeEvents(),i.$refs.container.on("click.fb-close","[data-fancybox-close]",(function(t){t.stopPropagation(),t.preventDefault(),i.close(t)})).on("click.fb-prev touchend.fb-prev","[data-fancybox-prev]",(function(t){t.stopPropagation(),t.preventDefault(),i.previous()})).on("click.fb-next touchend.fb-next","[data-fancybox-next]",(function(t){t.stopPropagation(),t.preventDefault(),i.next()})).on("click.fb","[data-fancybox-zoom]",(function(t){i[i.isScaledDown()?"scaleToActual":"scaleToFit"]()})),s.on("orientationchange.fb resize.fb",(function(t){t&&t.originalEvent&&"resize"===t.originalEvent.type?c((function(){i.update()})):(i.$refs.stage.hide(),setTimeout((function(){i.$refs.stage.show(),i.update()}),600))})),a.on("focusin.fb",(function(t){var o=n.fancybox?n.fancybox.getInstance():null;o.isClosing||!o.current||!o.current.opts.trapFocus||n(t.target).hasClass("fancybox-container")||n(t.target).is(e)||o&&"fixed"!==n(t.target).css("position")&&!o.$refs.container.has(t.target).length&&(t.stopPropagation(),o.focus(),s.scrollTop(i.scrollTop).scrollLeft(i.scrollLeft))})),a.on("keydown.fb",(function(t){var e=i.current,o=t.keyCode||t.which;if(e&&e.opts.keyboard&&!n(t.target).is("input")&&!n(t.target).is("textarea"))return 8===o||27===o?(t.preventDefault(),void i.close(t)):37===o||38===o?(t.preventDefault(),void i.previous()):39===o||40===o?(t.preventDefault(),void i.next()):void i.trigger("afterKeydown",t,o)})),i.group[i.currIndex].opts.idleTime&&(i.idleSecondsCounter=0,a.on("mousemove.fb-idle mouseleave.fb-idle mousedown.fb-idle touchstart.fb-idle touchmove.fb-idle scroll.fb-idle keydown.fb-idle",(function(t){i.idleSecondsCounter=0,i.isIdle&&i.showControls(),i.isIdle=!1})),i.idleInterval=t.setInterval((function(){i.idleSecondsCounter++,i.idleSecondsCounter>=i.group[i.currIndex].opts.idleTime&&!i.isDragging&&(i.isIdle=!0,i.idleSecondsCounter=0,i.hideControls())}),1e3))},removeEvents:function(){var e=this;s.off("orientationchange.fb resize.fb"),a.off("focusin.fb keydown.fb .fb-idle"),this.$refs.container.off(".fb-close .fb-prev .fb-next"),e.idleInterval&&(t.clearInterval(e.idleInterval),e.idleInterval=null)},previous:function(t){return this.jumpTo(this.currPos-1,t)},next:function(t){return this.jumpTo(this.currPos+1,t)},jumpTo:function(t,e,o){var r,s,a,l,c,u,d,p=this,f=p.group.length;if(!(p.isDragging||p.isClosing||p.isAnimating&&p.firstRun)){if(t=parseInt(t,10),!(s=p.current?p.current.opts.loop:p.opts.loop)&&(t<0||t>=f))return!1;if(r=p.firstRun=null===p.firstRun,!(f<2&&!r&&p.isDragging)){if(l=p.current,p.prevIndex=p.currIndex,p.prevPos=p.currPos,a=p.createSlide(t),f>1&&((s||a.index>0)&&p.createSlide(t-1),(s||a.index<f-1)&&p.createSlide(t+1)),p.current=a,p.currIndex=a.index,p.currPos=a.pos,p.trigger("beforeShow",r),p.updateControls(),u=n.fancybox.getTranslate(a.$slide),a.isMoved=(0!==u.left||0!==u.top)&&!a.$slide.hasClass("fancybox-animated"),a.forcedDuration=i,n.isNumeric(e)?a.forcedDuration=e:e=a.opts[r?"animationDuration":"transitionDuration"],e=parseInt(e,10),r)return a.opts.animationEffect&&e&&p.$refs.container.css("transition-duration",e+"ms"),p.$refs.container.removeClass("fancybox-is-hidden"),h(p.$refs.container),p.$refs.container.addClass("fancybox-is-open"),a.$slide.addClass("fancybox-slide--current"),p.loadSlide(a),void p.preload("image");n.each(p.slides,(function(t,e){n.fancybox.stop(e.$slide)})),a.$slide.removeClass("fancybox-slide--next fancybox-slide--previous").addClass("fancybox-slide--current"),a.isMoved?(c=Math.round(a.$slide.width()),n.each(p.slides,(function(t,i){var o=i.pos-a.pos;n.fancybox.animate(i.$slide,{top:0,left:o*c+o*i.opts.gutter},e,(function(){i.$slide.removeAttr("style").removeClass("fancybox-slide--next fancybox-slide--previous"),i.pos===p.currPos&&(a.isMoved=!1,p.complete())}))}))):p.$refs.stage.children().removeAttr("style"),a.isLoaded?p.revealContent(a):p.loadSlide(a),p.preload("image"),l.pos!==a.pos&&(d="fancybox-slide--"+(l.pos>a.pos?"next":"previous"),l.$slide.removeClass("fancybox-slide--complete fancybox-slide--current fancybox-slide--next fancybox-slide--previous"),l.isComplete=!1,e&&(a.isMoved||a.opts.transitionEffect)&&(a.isMoved?l.$slide.addClass(d):(d="fancybox-animated "+d+" fancybox-fx-"+a.opts.transitionEffect,n.fancybox.animate(l.$slide,d,e,(function(){l.$slide.removeClass(d).removeAttr("style")})))))}}},createSlide:function(t){var e,i,o=this;return i=(i=t%o.group.length)<0?o.group.length+i:i,!o.slides[t]&&o.group[i]&&(e=n('<div class="fancybox-slide"></div>').appendTo(o.$refs.stage),o.slides[t]=n.extend(!0,{},o.group[i],{pos:t,$slide:e,isLoaded:!1}),o.updateSlide(o.slides[t])),o.slides[t]},scaleToActual:function(t,e,o){var r,s,a,l,c,u=this,h=u.current,d=h.$content,p=parseInt(h.$slide.width(),10),f=parseInt(h.$slide.height(),10),g=h.width,m=h.height;"image"!=h.type||h.hasError||!d||u.isAnimating||(n.fancybox.stop(d),u.isAnimating=!0,t=t===i?.5*p:t,e=e===i?.5*f:e,l=g/(r=n.fancybox.getTranslate(d)).width,c=m/r.height,s=.5*p-.5*g,a=.5*f-.5*m,g>p&&((s=r.left*l-(t*l-t))>0&&(s=0),s<p-g&&(s=p-g)),m>f&&((a=r.top*c-(e*c-e))>0&&(a=0),a<f-m&&(a=f-m)),u.updateCursor(g,m),n.fancybox.animate(d,{top:a,left:s,scaleX:l,scaleY:c},o||330,(function(){u.isAnimating=!1})),u.SlideShow&&u.SlideShow.isActive&&u.SlideShow.stop())},scaleToFit:function(t){var e,i=this,o=i.current,r=o.$content;"image"!=o.type||o.hasError||!r||i.isAnimating||(n.fancybox.stop(r),i.isAnimating=!0,e=i.getFitPos(o),i.updateCursor(e.width,e.height),n.fancybox.animate(r,{top:e.top,left:e.left,scaleX:e.width/r.width(),scaleY:e.height/r.height()},t||330,(function(){i.isAnimating=!1})))},getFitPos:function(t){var e,i,o,r,s,a=t.$content,l=t.width,c=t.height,u=t.opts.margin;return!(!a||!a.length||!l&&!c)&&("number"===n.type(u)&&(u=[u,u]),2==u.length&&(u=[u[0],u[1],u[0],u[1]]),e=parseInt(this.$refs.stage.width(),10)-(u[1]+u[3]),i=parseInt(this.$refs.stage.height(),10)-(u[0]+u[2]),o=Math.min(1,e/l,i/c),r=Math.floor(o*l),s=Math.floor(o*c),{top:Math.floor(.5*(i-s))+u[0],left:Math.floor(.5*(e-r))+u[3],width:r,height:s})},update:function(){var t=this;n.each(t.slides,(function(e,n){t.updateSlide(n)}))},updateSlide:function(t,e){var i=this,o=t&&t.$content;o&&(t.width||t.height)&&(i.isAnimating=!1,n.fancybox.stop(o),n.fancybox.setTranslate(o,i.getFitPos(t)),t.pos===i.currPos&&i.updateCursor()),t.$slide.trigger("refresh"),i.trigger("onUpdate",t)},centerSlide:function(t,e){var o,r;this.current&&(o=Math.round(t.$slide.width()),r=t.pos-this.current.pos,n.fancybox.animate(t.$slide,{top:0,left:r*o+r*t.opts.gutter,opacity:1},e===i?0:e,null,!1))},updateCursor:function(t,e){var n=this,o=n.$refs.container.removeClass("fancybox-is-zoomable fancybox-can-zoomIn fancybox-can-drag fancybox-can-zoomOut");n.current&&!n.isClosing&&(n.isZoomable()?(o.addClass("fancybox-is-zoomable"),(t!==i&&e!==i?t<n.current.width&&e<n.current.height:n.isScaledDown())?o.addClass("fancybox-can-zoomIn"):n.current.opts.touch?o.addClass("fancybox-can-drag"):o.addClass("fancybox-can-zoomOut")):n.current.opts.touch&&o.addClass("fancybox-can-drag"))},isZoomable:function(){var t,e=this,i=e.current;if(i&&!e.isClosing)return!!("image"===i.type&&i.isLoaded&&!i.hasError&&("zoom"===i.opts.clickContent||n.isFunction(i.opts.clickContent)&&"zoom"===i.opts.clickContent(i))&&(t=e.getFitPos(i),i.width>t.width||i.height>t.height))},isScaledDown:function(){var t=this.current,e=t.$content,i=!1;return e&&(i=(i=n.fancybox.getTranslate(e)).width<t.width||i.height<t.height),i},canPan:function(){var t=this.current,e=t.$content,n=!1;return e&&(n=this.getFitPos(t),n=Math.abs(e.width()-n.width)>1||Math.abs(e.height()-n.height)>1),n},loadSlide:function(t){var e,i,o,r=this;if(!t.isLoading&&!t.isLoaded){switch(t.isLoading=!0,r.trigger("beforeLoad",t),e=t.type,(i=t.$slide).off("refresh").trigger("onReset").addClass("fancybox-slide--"+(e||"unknown")).addClass(t.opts.slideClass),e){case"image":r.setImage(t);break;case"iframe":r.setIframe(t);break;case"html":r.setContent(t,t.src||t.content);break;case"inline":n(t.src).length?r.setContent(t,n(t.src)):r.setError(t);break;case"ajax":r.showLoading(t),o=n.ajax(n.extend({},t.opts.ajax.settings,{url:t.src,success:function(e,n){"success"===n&&r.setContent(t,e)},error:function(e,n){e&&"abort"!==n&&r.setError(t)}})),i.one("onReset",(function(){o.abort()}));break;case"video":r.setContent(t,'<video controls><source src="'+t.src+'" type="'+t.opts.videoFormat+"\">Your browser doesn't support HTML5 video</video>");break;default:r.setError(t)}return!0}},setImage:function(e){var i,o,r,s,a=this,l=e.opts.srcset||e.opts.image.srcset;if(l){r=t.devicePixelRatio||1,s=t.innerWidth*r,o=l.split(",").map((function(t){var e={};return t.trim().split(/\s+/).forEach((function(t,n){var i=parseInt(t.substring(0,t.length-1),10);if(0===n)return e.url=t;i&&(e.value=i,e.postfix=t[t.length-1])})),e})),o.sort((function(t,e){return t.value-e.value}));for(var c=0;c<o.length;c++){var u=o[c];if("w"===u.postfix&&u.value>=s||"x"===u.postfix&&u.value>=r){i=u;break}}!i&&o.length&&(i=o[o.length-1]),i&&(e.src=i.url,e.width&&e.height&&"w"==i.postfix&&(e.height=e.width/e.height*i.value,e.width=i.value))}e.$content=n('<div class="fancybox-image-wrap"></div>').addClass("fancybox-is-hidden").appendTo(e.$slide),!1!==e.opts.preload&&e.opts.width&&e.opts.height&&(e.opts.thumb||e.opts.$thumb)?(e.width=e.opts.width,e.height=e.opts.height,e.$ghost=n("<img />").one("error",(function(){n(this).remove(),e.$ghost=null,a.setBigImage(e)})).one("load",(function(){a.afterLoad(e),a.setBigImage(e)})).addClass("fancybox-image").appendTo(e.$content).attr("src",e.opts.thumb||e.opts.$thumb.attr("src"))):a.setBigImage(e)},setBigImage:function(t){var e=this,i=n("<img />");t.$image=i.one("error",(function(){e.setError(t)})).one("load",(function(){clearTimeout(t.timouts),t.timouts=null,e.isClosing||(t.width=t.opts.width||this.naturalWidth,t.height=t.opts.height||this.naturalHeight,t.opts.image.srcset&&i.attr("sizes","100vw").attr("srcset",t.opts.image.srcset),e.hideLoading(t),t.$ghost?t.timouts=setTimeout((function(){t.timouts=null,t.$ghost.hide()}),Math.min(300,Math.max(1e3,t.height/1600))):e.afterLoad(t))})).addClass("fancybox-image").attr("src",t.src).appendTo(t.$content),(i[0].complete||"complete"==i[0].readyState)&&i[0].naturalWidth&&i[0].naturalHeight?i.trigger("load"):i[0].error?i.trigger("error"):t.timouts=setTimeout((function(){i[0].complete||t.hasError||e.showLoading(t)}),100)},setIframe:function(t){var e,o=this,r=t.opts.iframe,s=t.$slide;t.$content=n('<div class="fancybox-content'+(r.preload?" fancybox-is-hidden":"")+'"></div>').css(r.css).appendTo(s),e=n(r.tpl.replace(/\{rnd\}/g,(new Date).getTime())).attr(r.attr).appendTo(t.$content),r.preload?(o.showLoading(t),e.on("load.fb error.fb",(function(e){this.isReady=1,t.$slide.trigger("refresh"),o.afterLoad(t)})),s.on("refresh.fb",(function(){var n,o,s=t.$content,a=r.css.width,l=r.css.height;if(1===e[0].isReady){try{o=e.contents().find("body")}catch(t){}o&&o.length&&(a===i&&(n=e[0].contentWindow.document.documentElement.scrollWidth,a=Math.ceil(o.outerWidth(!0)+(s.width()-n)),a+=s.outerWidth()-s.innerWidth()),l===i&&(l=Math.ceil(o.outerHeight(!0)),l+=s.outerHeight()-s.innerHeight()),a&&s.width(a),l&&s.height(l)),s.removeClass("fancybox-is-hidden")}}))):this.afterLoad(t),e.attr("src",t.src),!0===t.opts.smallBtn&&t.$content.prepend(o.translate(t,t.opts.btnTpl.smallBtn)),s.one("onReset",(function(){try{n(this).find("iframe").hide().attr("src","//about:blank")}catch(t){}n(this).empty(),t.isLoaded=!1}))},setContent:function(t,e){var i;this.isClosing||(this.hideLoading(t),t.$slide.empty(),(i=e)&&i.hasOwnProperty&&i instanceof n&&e.parent().length?(e.parent(".fancybox-slide--inline").trigger("onReset"),t.$placeholder=n("<div></div>").hide().insertAfter(e),e.css("display","inline-block")):t.hasError||("string"===n.type(e)&&3===(e=n("<div>").append(n.trim(e)).contents())[0].nodeType&&(e=n("<div>").html(e)),t.opts.filter&&(e=n("<div>").html(e).find(t.opts.filter))),t.$slide.one("onReset",(function(){n(this).find("video,audio").trigger("pause"),t.$placeholder&&(t.$placeholder.after(e.hide()).remove(),t.$placeholder=null),t.$smallBtn&&(t.$smallBtn.remove(),t.$smallBtn=null),t.hasError||(n(this).empty(),t.isLoaded=!1)})),t.$content=n(e).appendTo(t.$slide),this.afterLoad(t))},setError:function(t){t.hasError=!0,t.$slide.removeClass("fancybox-slide--"+t.type),this.setContent(t,this.translate(t,t.opts.errorTpl))},showLoading:function(t){(t=t||this.current)&&!t.$spinner&&(t.$spinner=n(this.opts.spinnerTpl).appendTo(t.$slide))},hideLoading:function(t){(t=t||this.current)&&t.$spinner&&(t.$spinner.remove(),delete t.$spinner)},afterLoad:function(t){var e=this;e.isClosing||(t.isLoading=!1,t.isLoaded=!0,e.trigger("afterLoad",t),e.hideLoading(t),t.opts.smallBtn&&!t.$smallBtn&&(t.$smallBtn=n(e.translate(t,t.opts.btnTpl.smallBtn)).appendTo(t.$content.filter("div,form").first())),t.opts.protect&&t.$content&&!t.hasError&&(t.$content.on("contextmenu.fb",(function(t){return 2==t.button&&t.preventDefault(),!0})),"image"===t.type&&n('<div class="fancybox-spaceball"></div>').appendTo(t.$content)),e.revealContent(t))},revealContent:function(t){var e,o,r,s,a,l=this,c=t.$slide,u=!1;return e=t.opts[l.firstRun?"animationEffect":"transitionEffect"],r=t.opts[l.firstRun?"animationDuration":"transitionDuration"],r=parseInt(t.forcedDuration===i?r:t.forcedDuration,10),!t.isMoved&&t.pos===l.currPos&&r||(e=!1),"zoom"!==e||t.pos===l.currPos&&r&&"image"===t.type&&!t.hasError&&(u=l.getThumbPos(t))||(e="fade"),"zoom"===e?((a=l.getFitPos(t)).scaleX=a.width/u.width,a.scaleY=a.height/u.height,delete a.width,delete a.height,"auto"==(s=t.opts.zoomOpacity)&&(s=Math.abs(t.width/t.height-u.width/u.height)>.1),s&&(u.opacity=.1,a.opacity=1),n.fancybox.setTranslate(t.$content.removeClass("fancybox-is-hidden"),u),h(t.$content),void n.fancybox.animate(t.$content,a,r,(function(){l.complete()}))):(l.updateSlide(t),e?(n.fancybox.stop(c),o="fancybox-animated fancybox-slide--"+(t.pos>=l.prevPos?"next":"previous")+" fancybox-fx-"+e,c.removeAttr("style").removeClass("fancybox-slide--current fancybox-slide--next fancybox-slide--previous").addClass(o),t.$content.removeClass("fancybox-is-hidden"),h(c),void n.fancybox.animate(c,"fancybox-slide--current",r,(function(e){c.removeClass(o).removeAttr("style"),t.pos===l.currPos&&l.complete()}),!0)):(h(c),t.$content.removeClass("fancybox-is-hidden"),void(t.pos===l.currPos&&l.complete())))},getThumbPos:function(i){var o,r=!1,s=i.opts.$thumb,a=s?s.offset():0;return a&&s[0].ownerDocument===e&&function(e){for(var i=e[0],o=i.getBoundingClientRect(),r=[];null!==i.parentElement;)"hidden"!==n(i.parentElement).css("overflow")&&"auto"!==n(i.parentElement).css("overflow")||r.push(i.parentElement.getBoundingClientRect()),i=i.parentElement;return r.every((function(t){var e=Math.min(o.right,t.right)-Math.max(o.left,t.left),n=Math.min(o.bottom,t.bottom)-Math.max(o.top,t.top);return e>0&&n>0}))&&o.bottom>0&&o.right>0&&o.left<n(t).width()&&o.top<n(t).height()}(s)&&(o=this.$refs.stage.offset(),r={top:a.top-o.top+parseFloat(s.css("border-top-width")||0),left:a.left-o.left+parseFloat(s.css("border-left-width")||0),width:s.width(),height:s.height(),scaleX:1,scaleY:1}),r},complete:function(){var t=this,i=t.current,o={};i.isMoved||!i.isLoaded||i.isComplete||(i.isComplete=!0,i.$slide.siblings().trigger("onReset"),t.preload("inline"),h(i.$slide),i.$slide.addClass("fancybox-slide--complete"),n.each(t.slides,(function(e,i){i.pos>=t.currPos-1&&i.pos<=t.currPos+1?o[i.pos]=i:i&&(n.fancybox.stop(i.$slide),i.$slide.off().remove())})),t.slides=o,t.updateCursor(),t.trigger("afterShow"),i.$slide.find("video,audio").first().trigger("play"),(n(e.activeElement).is("[disabled]")||i.opts.autoFocus&&"image"!=i.type&&"iframe"!==i.type)&&t.focus())},preload:function(t){var e=this,n=e.slides[e.currPos+1],i=e.slides[e.currPos-1];n&&n.type===t&&e.loadSlide(n),i&&i.type===t&&e.loadSlide(i)},focus:function(){var t,e=this.current;this.isClosing||(e&&e.isComplete&&((t=e.$slide.find("input[autofocus]:enabled:visible:first")).length||(t=e.$slide.find("button,:input,[tabindex],a").filter(":enabled:visible:first"))),(t=t&&t.length?t:this.$refs.container).focus())},activate:function(){var t=this;n(".fancybox-container").each((function(){var e=n(this).data("FancyBox");e&&e.id!==t.id&&!e.isClosing&&(e.trigger("onDeactivate"),e.removeEvents(),e.isVisible=!1)})),t.isVisible=!0,(t.current||t.isIdle)&&(t.update(),t.updateControls()),t.trigger("onActivate"),t.addEvents()},close:function(t,e){var i,o,r,s,a,l,d=this,p=d.current,f=function(){d.cleanUp(t)};return!d.isClosing&&(d.isClosing=!0,!1===d.trigger("beforeClose",t)?(d.isClosing=!1,c((function(){d.update()})),!1):(d.removeEvents(),p.timouts&&clearTimeout(p.timouts),r=p.$content,i=p.opts.animationEffect,o=n.isNumeric(e)?e:i?p.opts.animationDuration:0,p.$slide.off(u).removeClass("fancybox-slide--complete fancybox-slide--next fancybox-slide--previous fancybox-animated"),p.$slide.siblings().trigger("onReset").remove(),o&&d.$refs.container.removeClass("fancybox-is-open").addClass("fancybox-is-closing"),d.hideLoading(p),d.hideControls(),d.updateCursor(),"zoom"!==i||!0!==t&&r&&o&&"image"===p.type&&!p.hasError&&(l=d.getThumbPos(p))||(i="fade"),"zoom"===i?(n.fancybox.stop(r),(a=n.fancybox.getTranslate(r)).width=a.width*a.scaleX,a.height=a.height*a.scaleY,"auto"==(s=p.opts.zoomOpacity)&&(s=Math.abs(p.width/p.height-l.width/l.height)>.1),s&&(l.opacity=0),a.scaleX=a.width/l.width,a.scaleY=a.height/l.height,a.width=l.width,a.height=l.height,n.fancybox.setTranslate(p.$content,a),h(p.$content),n.fancybox.animate(p.$content,l,o,f),!0):(i&&o?!0===t?setTimeout(f,o):n.fancybox.animate(p.$slide.removeClass("fancybox-slide--current"),"fancybox-animated fancybox-slide--previous fancybox-fx-"+i,o,f):f(),!0)))},cleanUp:function(t){var i,o,r=this,a=n("body");r.current.$slide.trigger("onReset"),r.$refs.container.empty().remove(),r.trigger("afterClose",t),r.$lastFocus&&r.current.opts.backFocus&&r.$lastFocus.focus(),r.current=null,(i=n.fancybox.getInstance())?i.activate():(s.scrollTop(r.scrollTop).scrollLeft(r.scrollLeft),a.removeClass("fancybox-active compensate-for-scrollbar"),a.hasClass("fancybox-iosfix")&&(o=parseInt(e.body.style.top,10),a.removeClass("fancybox-iosfix").css("top","").scrollTop(-1*o)),n("#fancybox-style-noscroll").remove())},trigger:function(t,e){var i,o=Array.prototype.slice.call(arguments,1),r=this,s=e&&e.opts?e:r.current;if(s?o.unshift(s):s=r,o.unshift(r),n.isFunction(s.opts[t])&&(i=s.opts[t].apply(s,o)),!1===i)return i;"afterClose"!==t&&r.$refs?r.$refs.container.trigger(t+".fb",o):a.trigger(t+".fb",o)},updateControls:function(t){var e=this,n=e.current,i=n.index,o=n.opts.caption,r=e.$refs.container,s=e.$refs.caption;n.$slide.trigger("refresh"),e.$caption=o&&o.length?s.html(o):null,e.isHiddenControls||e.isIdle||e.showControls(),r.find("[data-fancybox-count]").html(e.group.length),r.find("[data-fancybox-index]").html(i+1),r.find("[data-fancybox-prev]").prop("disabled",!n.opts.loop&&i<=0),r.find("[data-fancybox-next]").prop("disabled",!n.opts.loop&&i>=e.group.length-1),"image"===n.type?r.find("[data-fancybox-download]").attr("href",n.opts.image.src||n.src).show():r.find("[data-fancybox-download],[data-fancybox-zoom]").hide()},hideControls:function(){this.isHiddenControls=!0,this.$refs.container.removeClass("fancybox-show-infobar fancybox-show-toolbar fancybox-show-caption fancybox-show-nav")},showControls:function(){var t=this,e=t.current?t.current.opts:t.opts,n=t.$refs.container;t.isHiddenControls=!1,t.idleSecondsCounter=0,n.toggleClass("fancybox-show-toolbar",!(!e.toolbar||!e.buttons)).toggleClass("fancybox-show-infobar",!!(e.infobar&&t.group.length>1)).toggleClass("fancybox-show-nav",!!(e.arrows&&t.group.length>1)).toggleClass("fancybox-is-modal",!!e.modal),t.$caption?n.addClass("fancybox-show-caption "):n.removeClass("fancybox-show-caption")},toggleControls:function(){this.isHiddenControls?this.showControls():this.hideControls()}}),n.fancybox={version:"3.2.10",defaults:r,getInstance:function(t){var e=n('.fancybox-container:not(".fancybox-is-closing"):last').data("FancyBox"),i=Array.prototype.slice.call(arguments,1);return e instanceof d&&("string"===n.type(t)?e[t].apply(e,i):"function"===n.type(t)&&t.apply(e,i),e)},open:function(t,e,n){return new d(t,e,n)},close:function(t){var e=this.getInstance();e&&(e.close(),!0===t&&this.close())},destroy:function(){this.close(!0),a.off("click.fb-start")},isMobile:e.createTouch!==i&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),use3d:(o=e.createElement("div"),t.getComputedStyle&&t.getComputedStyle(o).getPropertyValue("transform")&&!(e.documentMode&&e.documentMode<11)),getTranslate:function(t){var e;if(!t||!t.length)return!1;if((e=(e=t.eq(0).css("transform"))&&-1!==e.indexOf("matrix")?(e=(e=e.split("(")[1]).split(")")[0]).split(","):[]).length)e=(e=e.length>10?[e[13],e[12],e[0],e[5]]:[e[5],e[4],e[0],e[3]]).map(parseFloat);else{e=[0,0,1,1];var n=/\.*translate\((.*)px,(.*)px\)/i.exec(t.eq(0).attr("style"));n&&(e[0]=parseFloat(n[2]),e[1]=parseFloat(n[1]))}return{top:e[0],left:e[1],scaleX:e[2],scaleY:e[3],opacity:parseFloat(t.css("opacity")),width:t.width(),height:t.height()}},setTranslate:function(t,e){var n="",o={};if(t&&e)return e.left===i&&e.top===i||(n=(e.left===i?t.position().left:e.left)+"px, "+(e.top===i?t.position().top:e.top)+"px",n=this.use3d?"translate3d("+n+", 0px)":"translate("+n+")"),e.scaleX!==i&&e.scaleY!==i&&(n=(n.length?n+" ":"")+"scale("+e.scaleX+", "+e.scaleY+")"),n.length&&(o.transform=n),e.opacity!==i&&(o.opacity=e.opacity),e.width!==i&&(o.width=e.width),e.height!==i&&(o.height=e.height),t.css(o)},animate:function(t,e,o,r,s){n.isFunction(o)&&(r=o,o=null),n.isPlainObject(e)||t.removeAttr("style"),t.on(u,(function(o){(!o||!o.originalEvent||t.is(o.originalEvent.target)&&"z-index"!=o.originalEvent.propertyName)&&(n.fancybox.stop(t),n.isPlainObject(e)?(e.scaleX!==i&&e.scaleY!==i&&(t.css("transition-duration",""),e.width=Math.round(t.width()*e.scaleX),e.height=Math.round(t.height()*e.scaleY),e.scaleX=1,e.scaleY=1,n.fancybox.setTranslate(t,e)),!1===s&&t.removeAttr("style")):!0!==s&&t.removeClass(e),n.isFunction(r)&&r(o))})),n.isNumeric(o)&&t.css("transition-duration",o+"ms"),n.isPlainObject(e)?n.fancybox.setTranslate(t,e):t.addClass(e),e.scaleX&&t.hasClass("fancybox-image-wrap")&&t.parent().addClass("fancybox-is-scaling"),t.data("timer",setTimeout((function(){t.trigger("transitionend")}),o+16))},stop:function(t){clearTimeout(t.data("timer")),t.off("transitionend").css("transition-duration",""),t.hasClass("fancybox-image-wrap")&&t.parent().removeClass("fancybox-is-scaling")}},n.fn.fancybox=function(t){var e;return(e=(t=t||{}).selector||!1)?n("body").off("click.fb-start",e).on("click.fb-start",e,{options:t},p):this.off("click.fb-start").on("click.fb-start",{items:this,options:t},p),this},a.on("click.fb-start","[data-fancybox]",p)}function p(t){var e=n(t.currentTarget),i=t.data?t.data.options:{},o=e.attr("data-fancybox")||"",r=0,s=[];t.isDefaultPrevented()||(t.preventDefault(),o?(r=(s=(s=i.selector?n(i.selector):t.data?t.data.items:[]).length?s.filter('[data-fancybox="'+o+'"]'):n('[data-fancybox="'+o+'"]')).index(e))<0&&(r=0):s=[e],n.fancybox.open(s,i,r))}}(window,document,window.jQuery||jQuery),function(t){"use strict";var e=function(e,n,i){if(e)return i=i||"","object"===t.type(i)&&(i=t.param(i,!0)),t.each(n,(function(t,n){e=e.replace("$"+t,n||"")})),i.length&&(e+=(e.indexOf("?")>0?"&":"?")+i),e},n={youtube:{matcher:/(youtube\.com|youtu\.be|youtube\-nocookie\.com)\/(watch\?(.*&)?v=|v\/|u\/|embed\/?)?(videoseries\?list=(.*)|[\w-]{11}|\?listType=(.*)&list=(.*))(.*)/i,params:{autoplay:1,autohide:1,fs:1,rel:0,hd:1,wmode:"transparent",enablejsapi:1,html5:1},paramPlace:8,type:"iframe",url:"//www.youtube.com/embed/$4",thumb:"//img.youtube.com/vi/$4/hqdefault.jpg"},vimeo:{matcher:/^.+vimeo.com\/(.*\/)?([\d]+)(.*)?/,params:{autoplay:1,hd:1,show_title:1,show_byline:1,show_portrait:0,fullscreen:1,api:1},paramPlace:3,type:"iframe",url:"//player.vimeo.com/video/$2"},metacafe:{matcher:/metacafe.com\/watch\/(\d+)\/(.*)?/,type:"iframe",url:"//www.metacafe.com/embed/$1/?ap=1"},dailymotion:{matcher:/dailymotion.com\/video\/(.*)\/?(.*)/,params:{additionalInfos:0,autoStart:1},type:"iframe",url:"//www.dailymotion.com/embed/video/$1"},vine:{matcher:/vine.co\/v\/([a-zA-Z0-9\?\=\-]+)/,type:"iframe",url:"//vine.co/v/$1/embed/simple"},instagram:{matcher:/(instagr\.am|instagram\.com)\/p\/([a-zA-Z0-9_\-]+)\/?/i,type:"image",url:"//$1/p/$2/media/?size=l"},gmap_place:{matcher:/(maps\.)?google\.([a-z]{2,3}(\.[a-z]{2})?)\/(((maps\/(place\/(.*)\/)?\@(.*),(\d+.?\d+?)z))|(\?ll=))(.*)?/i,type:"iframe",url:function(t){return"//maps.google."+t[2]+"/?ll="+(t[9]?t[9]+"&z="+Math.floor(t[10])+(t[12]?t[12].replace(/^\//,"&"):""):t[12])+"&output="+(t[12]&&t[12].indexOf("layer=c")>0?"svembed":"embed")}},gmap_search:{matcher:/(maps\.)?google\.([a-z]{2,3}(\.[a-z]{2})?)\/(maps\/search\/)(.*)/i,type:"iframe",url:function(t){return"//maps.google."+t[2]+"/maps?q="+t[5].replace("query=","q=").replace("api=1","")+"&output=embed"}}};t(document).on("objectNeedsType.fb",(function(i,o,r){var s,a,l,c,u,h,d,p=r.src||"",f=!1;s=t.extend(!0,{},n,r.opts.media),t.each(s,(function(n,i){if(l=p.match(i.matcher)){if(f=i.type,h={},i.paramPlace&&l[i.paramPlace]){"?"==(u=l[i.paramPlace])[0]&&(u=u.substring(1)),u=u.split("&");for(var o=0;o<u.length;++o){var s=u[o].split("=",2);2==s.length&&(h[s[0]]=decodeURIComponent(s[1].replace(/\+/g," ")))}}return c=t.extend(!0,{},i.params,r.opts[n],h),p="function"===t.type(i.url)?i.url.call(this,l,c,r):e(i.url,l,c),a="function"===t.type(i.thumb)?i.thumb.call(this,l,c,r):e(i.thumb,l),"vimeo"===n&&(p=p.replace("&%23","#")),!1}})),f?(r.src=p,r.type=f,r.opts.thumb||r.opts.$thumb&&r.opts.$thumb.length||(r.opts.thumb=a),"iframe"===f&&(t.extend(!0,r.opts,{iframe:{preload:!1,attr:{scrolling:"no"}}}),r.contentProvider=d,r.opts.slideClass+=" fancybox-slide--video")):p&&(r.type=r.opts.defaultType)}))}(window.jQuery||jQuery),function(t,e,n){"use strict";var i=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||function(e){return t.setTimeout(e,1e3/60)},o=t.cancelAnimationFrame||t.webkitCancelAnimationFrame||t.mozCancelAnimationFrame||t.oCancelAnimationFrame||function(e){t.clearTimeout(e)},r=function(e){var n=[];for(var i in e=(e=e.originalEvent||e||t.e).touches&&e.touches.length?e.touches:e.changedTouches&&e.changedTouches.length?e.changedTouches:[e])e[i].pageX?n.push({x:e[i].pageX,y:e[i].pageY}):e[i].clientX&&n.push({x:e[i].clientX,y:e[i].clientY});return n},s=function(t,e,n){return e&&t?"x"===n?t.x-e.x:"y"===n?t.y-e.y:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)):0},a=function(t){if(t.is('a,area,button,[role="button"],input,label,select,summary,textarea')||n.isFunction(t.get(0).onclick)||t.data("selectable"))return!0;for(var e=0,i=t[0].attributes,o=i.length;e<o;e++)if("data-fancybox-"===i[e].nodeName.substr(0,14))return!0;return!1},l=function(e){for(var n,i,o,r,s,a=!1;n=e.get(0),i=void 0,o=void 0,r=void 0,s=void 0,i=t.getComputedStyle(n)["overflow-y"],o=t.getComputedStyle(n)["overflow-x"],r=("scroll"===i||"auto"===i)&&n.scrollHeight>n.clientHeight,s=("scroll"===o||"auto"===o)&&n.scrollWidth>n.clientWidth,!(a=r||s)&&(e=e.parent()).length&&!e.hasClass("fancybox-stage")&&!e.is("body"););return a},c=function(t){var e=this;e.instance=t,e.$bg=t.$refs.bg,e.$stage=t.$refs.stage,e.$container=t.$refs.container,e.destroy(),e.$container.on("touchstart.fb.touch mousedown.fb.touch",n.proxy(e,"ontouchstart"))};c.prototype.destroy=function(){this.$container.off(".fb.touch")},c.prototype.ontouchstart=function(i){var o=this,c=n(i.target),u=o.instance,h=u.current,d=h.$content,p="touchstart"==i.type;if(p&&o.$container.off("mousedown.fb.touch"),(!i.originalEvent||2!=i.originalEvent.button)&&c.length&&!a(c)&&!a(c.parent())&&(c.is("img")||!(i.originalEvent.clientX>c[0].clientWidth+c.offset().left))){if(!h||o.instance.isAnimating||o.instance.isClosing)return i.stopPropagation(),void i.preventDefault();o.realPoints=o.startPoints=r(i),o.startPoints&&(i.stopPropagation(),o.startEvent=i,o.canTap=!0,o.$target=c,o.$content=d,o.opts=h.opts.touch,o.isPanning=!1,o.isSwiping=!1,o.isZooming=!1,o.isScrolling=!1,o.sliderStartPos=o.sliderLastPos||{top:0,left:0},o.contentStartPos=n.fancybox.getTranslate(o.$content),o.contentLastPos=null,o.startTime=(new Date).getTime(),o.distanceX=o.distanceY=o.distance=0,o.canvasWidth=Math.round(h.$slide[0].clientWidth),o.canvasHeight=Math.round(h.$slide[0].clientHeight),n(e).off(".fb.touch").on(p?"touchend.fb.touch touchcancel.fb.touch":"mouseup.fb.touch mouseleave.fb.touch",n.proxy(o,"ontouchend")).on(p?"touchmove.fb.touch":"mousemove.fb.touch",n.proxy(o,"ontouchmove")),n.fancybox.isMobile&&e.addEventListener("scroll",o.onscroll,!0),(o.opts||u.canPan())&&(c.is(o.$stage)||o.$stage.find(c).length)?(n.fancybox.isMobile&&(l(c)||l(c.parent()))||i.preventDefault(),1===o.startPoints.length&&("image"===h.type&&(o.contentStartPos.width>o.canvasWidth+1||o.contentStartPos.height>o.canvasHeight+1)?(n.fancybox.stop(o.$content),o.$content.css("transition-duration",""),o.isPanning=!0):o.isSwiping=!0,o.$container.addClass("fancybox-controls--isGrabbing")),2!==o.startPoints.length||u.isAnimating||h.hasError||"image"!==h.type||!h.isLoaded&&!h.$ghost||(o.canTap=!1,o.isSwiping=!1,o.isPanning=!1,o.isZooming=!0,n.fancybox.stop(o.$content),o.$content.css("transition-duration",""),o.centerPointStartX=.5*(o.startPoints[0].x+o.startPoints[1].x)-n(t).scrollLeft(),o.centerPointStartY=.5*(o.startPoints[0].y+o.startPoints[1].y)-n(t).scrollTop(),o.percentageOfImageAtPinchPointX=(o.centerPointStartX-o.contentStartPos.left)/o.contentStartPos.width,o.percentageOfImageAtPinchPointY=(o.centerPointStartY-o.contentStartPos.top)/o.contentStartPos.height,o.startDistanceBetweenFingers=s(o.startPoints[0],o.startPoints[1]))):c.is("img")&&i.preventDefault())}},c.prototype.onscroll=function(t){self.isScrolling=!0},c.prototype.ontouchmove=function(t){var e=this,i=n(t.target);e.isScrolling||!i.is(e.$stage)&&!e.$stage.find(i).length?e.canTap=!1:(e.newPoints=r(t),(e.opts||e.instance.canPan())&&e.newPoints&&e.newPoints.length&&(e.isSwiping&&!0===e.isSwiping||t.preventDefault(),e.distanceX=s(e.newPoints[0],e.startPoints[0],"x"),e.distanceY=s(e.newPoints[0],e.startPoints[0],"y"),e.distance=s(e.newPoints[0],e.startPoints[0]),e.distance>0&&(e.isSwiping?e.onSwipe(t):e.isPanning?e.onPan():e.isZooming&&e.onZoom())))},c.prototype.onSwipe=function(e){var r,s=this,a=s.isSwiping,c=s.sliderStartPos.left||0;if(!0!==a)"x"==a&&(s.distanceX>0&&(s.instance.group.length<2||0===s.instance.current.index&&!s.instance.current.opts.loop)?c+=Math.pow(s.distanceX,.8):s.distanceX<0&&(s.instance.group.length<2||s.instance.current.index===s.instance.group.length-1&&!s.instance.current.opts.loop)?c-=Math.pow(-s.distanceX,.8):c+=s.distanceX),s.sliderLastPos={top:"x"==a?0:s.sliderStartPos.top+s.distanceY,left:c},s.requestId&&(o(s.requestId),s.requestId=null),s.requestId=i((function(){s.sliderLastPos&&(n.each(s.instance.slides,(function(t,e){var i=e.pos-s.instance.currPos;n.fancybox.setTranslate(e.$slide,{top:s.sliderLastPos.top,left:s.sliderLastPos.left+i*s.canvasWidth+i*e.opts.gutter})})),s.$container.addClass("fancybox-is-sliding"))}));else if(Math.abs(s.distance)>10){if(s.canTap=!1,s.instance.group.length<2&&s.opts.vertical?s.isSwiping="y":s.instance.isDragging||!1===s.opts.vertical||"auto"===s.opts.vertical&&n(t).width()>800?s.isSwiping="x":(r=Math.abs(180*Math.atan2(s.distanceY,s.distanceX)/Math.PI),s.isSwiping=r>45&&r<135?"y":"x"),s.canTap=!1,"y"===s.isSwiping&&n.fancybox.isMobile&&(l(s.$target)||l(s.$target.parent())))return void(s.isScrolling=!0);s.instance.isDragging=s.isSwiping,s.startPoints=s.newPoints,n.each(s.instance.slides,(function(t,e){n.fancybox.stop(e.$slide),e.$slide.css("transition-duration",""),e.inTransition=!1,e.pos===s.instance.current.pos&&(s.sliderStartPos.left=n.fancybox.getTranslate(e.$slide).left)})),s.instance.SlideShow&&s.instance.SlideShow.isActive&&s.instance.SlideShow.stop()}},c.prototype.onPan=function(){var t=this;s(t.newPoints[0],t.realPoints[0])<(n.fancybox.isMobile?10:5)?t.startPoints=t.newPoints:(t.canTap=!1,t.contentLastPos=t.limitMovement(),t.requestId&&(o(t.requestId),t.requestId=null),t.requestId=i((function(){n.fancybox.setTranslate(t.$content,t.contentLastPos)})))},c.prototype.limitMovement=function(){var t,e,n,i,o,r,s=this,a=s.canvasWidth,l=s.canvasHeight,c=s.distanceX,u=s.distanceY,h=s.contentStartPos,d=h.left,p=h.top,f=h.width,g=h.height;return o=f>a?d+c:d,r=p+u,t=Math.max(0,.5*a-.5*f),e=Math.max(0,.5*l-.5*g),n=Math.min(a-f,.5*a-.5*f),i=Math.min(l-g,.5*l-.5*g),f>a&&(c>0&&o>t&&(o=t-1+Math.pow(-t+d+c,.8)||0),c<0&&o<n&&(o=n+1-Math.pow(n-d-c,.8)||0)),g>l&&(u>0&&r>e&&(r=e-1+Math.pow(-e+p+u,.8)||0),u<0&&r<i&&(r=i+1-Math.pow(i-p-u,.8)||0)),{top:r,left:o,scaleX:h.scaleX,scaleY:h.scaleY}},c.prototype.limitPosition=function(t,e,n,i){var o=this.canvasWidth,r=this.canvasHeight;return t=n>o?(t=t>0?0:t)<o-n?o-n:t:Math.max(0,o/2-n/2),{top:e=i>r?(e=e>0?0:e)<r-i?r-i:e:Math.max(0,r/2-i/2),left:t}},c.prototype.onZoom=function(){var e=this,r=e.contentStartPos.width,a=e.contentStartPos.height,l=e.contentStartPos.left,c=e.contentStartPos.top,u=s(e.newPoints[0],e.newPoints[1])/e.startDistanceBetweenFingers,h=Math.floor(r*u),d=Math.floor(a*u),p=(r-h)*e.percentageOfImageAtPinchPointX,f=(a-d)*e.percentageOfImageAtPinchPointY,g=(e.newPoints[0].x+e.newPoints[1].x)/2-n(t).scrollLeft(),m=(e.newPoints[0].y+e.newPoints[1].y)/2-n(t).scrollTop(),v=g-e.centerPointStartX,y={top:c+(f+(m-e.centerPointStartY)),left:l+(p+v),scaleX:e.contentStartPos.scaleX*u,scaleY:e.contentStartPos.scaleY*u};e.canTap=!1,e.newWidth=h,e.newHeight=d,e.contentLastPos=y,e.requestId&&(o(e.requestId),e.requestId=null),e.requestId=i((function(){n.fancybox.setTranslate(e.$content,e.contentLastPos)}))},c.prototype.ontouchend=function(t){var i=this,s=Math.max((new Date).getTime()-i.startTime,1),a=i.isSwiping,l=i.isPanning,c=i.isZooming,u=i.isScrolling;if(i.endPoints=r(t),i.$container.removeClass("fancybox-controls--isGrabbing"),n(e).off(".fb.touch"),e.removeEventListener("scroll",i.onscroll,!0),i.requestId&&(o(i.requestId),i.requestId=null),i.isSwiping=!1,i.isPanning=!1,i.isZooming=!1,i.isScrolling=!1,i.instance.isDragging=!1,i.canTap)return i.onTap(t);i.speed=366,i.velocityX=i.distanceX/s*.5,i.velocityY=i.distanceY/s*.5,i.speedX=Math.max(.5*i.speed,Math.min(1.5*i.speed,1/Math.abs(i.velocityX)*i.speed)),l?i.endPanning():c?i.endZooming():i.endSwiping(a,u)},c.prototype.endSwiping=function(t,e){var i=this,o=!1,r=i.instance.group.length;i.sliderLastPos=null,"y"==t&&!e&&Math.abs(i.distanceY)>50?(n.fancybox.animate(i.instance.current.$slide,{top:i.sliderStartPos.top+i.distanceY+150*i.velocityY,opacity:0},150),o=i.instance.close(!0,300)):"x"==t&&i.distanceX>50&&r>1?o=i.instance.previous(i.speedX):"x"==t&&i.distanceX<-50&&r>1&&(o=i.instance.next(i.speedX)),!1!==o||"x"!=t&&"y"!=t||(e||r<2?i.instance.centerSlide(i.instance.current,150):i.instance.jumpTo(i.instance.current.index)),i.$container.removeClass("fancybox-is-sliding")},c.prototype.endPanning=function(){var t,e,i,o=this;o.contentLastPos&&(!1===o.opts.momentum?(t=o.contentLastPos.left,e=o.contentLastPos.top):(t=o.contentLastPos.left+o.velocityX*o.speed,e=o.contentLastPos.top+o.velocityY*o.speed),(i=o.limitPosition(t,e,o.contentStartPos.width,o.contentStartPos.height)).width=o.contentStartPos.width,i.height=o.contentStartPos.height,n.fancybox.animate(o.$content,i,330))},c.prototype.endZooming=function(){var t,e,i,o,r=this,s=r.instance.current,a=r.newWidth,l=r.newHeight;r.contentLastPos&&(t=r.contentLastPos.left,o={top:e=r.contentLastPos.top,left:t,width:a,height:l,scaleX:1,scaleY:1},n.fancybox.setTranslate(r.$content,o),a<r.canvasWidth&&l<r.canvasHeight?r.instance.scaleToFit(150):a>s.width||l>s.height?r.instance.scaleToActual(r.centerPointStartX,r.centerPointStartY,150):(i=r.limitPosition(t,e,a,l),n.fancybox.setTranslate(r.content,n.fancybox.getTranslate(r.$content)),n.fancybox.animate(r.$content,i,150)))},c.prototype.onTap=function(t){var e,i=this,o=n(t.target),s=i.instance,a=s.current,l=t&&r(t)||i.startPoints,c=l[0]?l[0].x-i.$stage.offset().left:0,u=l[0]?l[0].y-i.$stage.offset().top:0,h=function(e){var o=a.opts[e];if(n.isFunction(o)&&(o=o.apply(s,[a,t])),o)switch(o){case"close":s.close(i.startEvent);break;case"toggleControls":s.toggleControls(!0);break;case"next":s.next();break;case"nextOrClose":s.group.length>1?s.next():s.close(i.startEvent);break;case"zoom":"image"==a.type&&(a.isLoaded||a.$ghost)&&(s.canPan()?s.scaleToFit():s.isScaledDown()?s.scaleToActual(c,u):s.group.length<2&&s.close(i.startEvent))}};if((!t.originalEvent||2!=t.originalEvent.button)&&(o.is("img")||!(c>o[0].clientWidth+o.offset().left))){if(o.is(".fancybox-bg,.fancybox-inner,.fancybox-outer,.fancybox-container"))e="Outside";else if(o.is(".fancybox-slide"))e="Slide";else{if(!s.current.$content||!s.current.$content.find(o).addBack().filter(o).length)return;e="Content"}if(i.tapped){if(clearTimeout(i.tapped),i.tapped=null,Math.abs(c-i.tapX)>50||Math.abs(u-i.tapY)>50)return this;h("dblclick"+e)}else i.tapX=c,i.tapY=u,a.opts["dblclick"+e]&&a.opts["dblclick"+e]!==a.opts["click"+e]?i.tapped=setTimeout((function(){i.tapped=null,h("click"+e)}),500):h("click"+e);return this}},n(e).on("onActivate.fb",(function(t,e){e&&!e.Guestures&&(e.Guestures=new c(e))}))}(window,document,window.jQuery||jQuery),function(t,e){"use strict";e.extend(!0,e.fancybox.defaults,{btnTpl:{slideShow:'<button data-fancybox-play class="fancybox-button fancybox-button--play" title="{{PLAY_START}}"><svg viewBox="0 0 40 40"><path d="M13,12 L27,20 L13,27 Z" /><path d="M15,10 v19 M23,10 v19" /></svg></button>'},slideShow:{autoStart:!1,speed:3e3}});var n=function(t){this.instance=t,this.init()};e.extend(n.prototype,{timer:null,isActive:!1,$button:null,init:function(){var t=this;t.$button=t.instance.$refs.toolbar.find("[data-fancybox-play]").on("click",(function(){t.toggle()})),(t.instance.group.length<2||!t.instance.group[t.instance.currIndex].opts.slideShow)&&t.$button.hide()},set:function(t){var e=this;e.instance&&e.instance.current&&(!0===t||e.instance.current.opts.loop||e.instance.currIndex<e.instance.group.length-1)?e.timer=setTimeout((function(){e.isActive&&e.instance.jumpTo((e.instance.currIndex+1)%e.instance.group.length)}),e.instance.current.opts.slideShow.speed):(e.stop(),e.instance.idleSecondsCounter=0,e.instance.showControls())},clear:function(){clearTimeout(this.timer),this.timer=null},start:function(){var t=this,e=t.instance.current;e&&(t.isActive=!0,t.$button.attr("title",e.opts.i18n[e.opts.lang].PLAY_STOP).removeClass("fancybox-button--play").addClass("fancybox-button--pause"),t.set(!0))},stop:function(){var t=this,e=t.instance.current;t.clear(),t.$button.attr("title",e.opts.i18n[e.opts.lang].PLAY_START).removeClass("fancybox-button--pause").addClass("fancybox-button--play"),t.isActive=!1},toggle:function(){var t=this;t.isActive?t.stop():t.start()}}),e(t).on({"onInit.fb":function(t,e){e&&!e.SlideShow&&(e.SlideShow=new n(e))},"beforeShow.fb":function(t,e,n,i){var o=e&&e.SlideShow;i?o&&n.opts.slideShow.autoStart&&o.start():o&&o.isActive&&o.clear()},"afterShow.fb":function(t,e,n){var i=e&&e.SlideShow;i&&i.isActive&&i.set()},"afterKeydown.fb":function(n,i,o,r,s){var a=i&&i.SlideShow;!a||!o.opts.slideShow||80!==s&&32!==s||e(t.activeElement).is("button,a,input")||(r.preventDefault(),a.toggle())},"beforeClose.fb onDeactivate.fb":function(t,e){var n=e&&e.SlideShow;n&&n.stop()}}),e(t).on("visibilitychange",(function(){var n=e.fancybox.getInstance(),i=n&&n.SlideShow;i&&i.isActive&&(t.hidden?i.clear():i.set())}))}(document,window.jQuery||jQuery),function(t,e){"use strict";var n=function(){var e,n,i,o=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r={};for(n=0;n<o.length;n++)if((e=o[n])&&e[1]in t){for(i=0;i<e.length;i++)r[o[0][i]]=e[i];return r}return!1}();if(n){var i={request:function(e){(e=e||t.documentElement)[n.requestFullscreen](e.ALLOW_KEYBOARD_INPUT)},exit:function(){t[n.exitFullscreen]()},toggle:function(e){e=e||t.documentElement,this.isFullscreen()?this.exit():this.request(e)},isFullscreen:function(){return Boolean(t[n.fullscreenElement])},enabled:function(){return Boolean(t[n.fullscreenEnabled])}};e.extend(!0,e.fancybox.defaults,{btnTpl:{fullScreen:'<button data-fancybox-fullscreen class="fancybox-button fancybox-button--fullscreen" title="{{FULL_SCREEN}}"><svg viewBox="0 0 40 40"><path d="M9,12 h22 v16 h-22 v-16 v16 h22 v-16 Z" /></svg></button>'},fullScreen:{autoStart:!1}}),e(t).on({"onInit.fb":function(t,e){var n;e&&e.group[e.currIndex].opts.fullScreen?((n=e.$refs.container).on("click.fb-fullscreen","[data-fancybox-fullscreen]",(function(t){t.stopPropagation(),t.preventDefault(),i.toggle(n[0])})),e.opts.fullScreen&&!0===e.opts.fullScreen.autoStart&&i.request(n[0]),e.FullScreen=i):e&&e.$refs.toolbar.find("[data-fancybox-fullscreen]").hide()},"afterKeydown.fb":function(t,e,n,i,o){e&&e.FullScreen&&70===o&&(i.preventDefault(),e.FullScreen.toggle(e.$refs.container[0]))},"beforeClose.fb":function(t){t&&t.FullScreen&&i.exit()}}),e(t).on(n.fullscreenchange,(function(){var t=i.isFullscreen(),n=e.fancybox.getInstance();n&&(n.current&&"image"===n.current.type&&n.isAnimating&&(n.current.$content.css("transition","none"),n.isAnimating=!1,n.update(!0,!0,0)),n.trigger("onFullscreenChange",t),n.$refs.container.toggleClass("fancybox-is-fullscreen",t))}))}else e&&e.fancybox&&(e.fancybox.defaults.btnTpl.fullScreen=!1)}(document,window.jQuery||jQuery),function(t,e){"use strict";e.fancybox.defaults=e.extend(!0,{btnTpl:{thumbs:'<button data-fancybox-thumbs class="fancybox-button fancybox-button--thumbs" title="{{THUMBS}}"><svg viewBox="0 0 120 120"><path d="M30,30 h14 v14 h-14 Z M50,30 h14 v14 h-14 Z M70,30 h14 v14 h-14 Z M30,50 h14 v14 h-14 Z M50,50 h14 v14 h-14 Z M70,50 h14 v14 h-14 Z M30,70 h14 v14 h-14 Z M50,70 h14 v14 h-14 Z M70,70 h14 v14 h-14 Z" /></svg></button>'},thumbs:{autoStart:!1,hideOnClose:!0,parentEl:".fancybox-container",axis:"y"}},e.fancybox.defaults);var n=function(t){this.init(t)};e.extend(n.prototype,{$button:null,$grid:null,$list:null,isVisible:!1,isActive:!1,init:function(t){var e=this;e.instance=t,t.Thumbs=e;var n=t.group[0],i=t.group[1];e.opts=t.group[t.currIndex].opts.thumbs,e.$button=t.$refs.toolbar.find("[data-fancybox-thumbs]"),e.opts&&n&&i&&("image"==n.type||n.opts.thumb||n.opts.$thumb)&&("image"==i.type||i.opts.thumb||i.opts.$thumb)?(e.$button.show().on("click",(function(){e.toggle()})),e.isActive=!0):e.$button.hide()},create:function(){var t,n,i=this,o=i.instance,r=i.opts.parentEl;i.$grid=e('<div class="fancybox-thumbs fancybox-thumbs-'+i.opts.axis+'"></div>').appendTo(o.$refs.container.find(r).addBack().filter(r)),t="<ul>",e.each(o.group,(function(e,i){(n=i.opts.thumb||(i.opts.$thumb?i.opts.$thumb.attr("src"):null))||"image"!==i.type||(n=i.src),n&&n.length&&(t+='<li data-index="'+e+'"  tabindex="0" class="fancybox-thumbs-loading"><img data-src="'+n+'" /></li>')})),t+="</ul>",i.$list=e(t).appendTo(i.$grid).on("click","li",(function(){o.jumpTo(e(this).data("index"))})),i.$list.find("img").hide().one("load",(function(){var t,n,i,o,r=e(this).parent().removeClass("fancybox-thumbs-loading"),s=r.outerWidth(),a=r.outerHeight();t=this.naturalWidth||this.width,o=(n=this.naturalHeight||this.height)/a,(i=t/s)>=1&&o>=1&&(i>o?(t/=o,n=a):(t=s,n/=i)),e(this).css({width:Math.floor(t),height:Math.floor(n),"margin-top":n>a?Math.floor(.3*a-.3*n):Math.floor(.5*a-.5*n),"margin-left":Math.floor(.5*s-.5*t)}).show()})).each((function(){this.src=e(this).data("src")})),"x"===i.opts.axis&&i.$list.width(parseInt(i.$grid.css("padding-right"))+o.group.length*i.$list.children().eq(0).outerWidth(!0)+"px")},focus:function(t){var e,n,i=this,o=i.$list;i.instance.current&&(n=(e=o.children().removeClass("fancybox-thumbs-active").filter('[data-index="'+i.instance.current.index+'"]').addClass("fancybox-thumbs-active")).position(),"y"===i.opts.axis&&(n.top<0||n.top>o.height()-e.outerHeight())?o.stop().animate({scrollTop:o.scrollTop()+n.top},t):"x"===i.opts.axis&&(n.left<o.parent().scrollLeft()||n.left>o.parent().scrollLeft()+(o.parent().width()-e.outerWidth()))&&o.parent().stop().animate({scrollLeft:n.left},t))},update:function(){this.instance.$refs.container.toggleClass("fancybox-show-thumbs",this.isVisible),this.isVisible?(this.$grid||this.create(),this.instance.trigger("onThumbsShow"),this.focus(0)):this.$grid&&this.instance.trigger("onThumbsHide"),this.instance.update()},hide:function(){this.isVisible=!1,this.update()},show:function(){this.isVisible=!0,this.update()},toggle:function(){this.isVisible=!this.isVisible,this.update()}}),e(t).on({"onInit.fb":function(t,e){var i;e&&!e.Thumbs&&(i=new n(e)).isActive&&!0===i.opts.autoStart&&i.show()},"beforeShow.fb":function(t,e,n,i){var o=e&&e.Thumbs;o&&o.isVisible&&o.focus(i?0:250)},"afterKeydown.fb":function(t,e,n,i,o){var r=e&&e.Thumbs;r&&r.isActive&&71===o&&(i.preventDefault(),r.toggle())},"beforeClose.fb":function(t,e){var n=e&&e.Thumbs;n&&n.isVisible&&!1!==n.opts.hideOnClose&&n.$grid.hide()}})}(document,window.jQuery),function(t,e){"use strict";e.extend(!0,e.fancybox.defaults,{btnTpl:{share:'<button data-fancybox-share class="fancybox-button fancybox-button--share" title="{{SHARE}}"><svg viewBox="0 0 40 40"><path d="M6,30 C8,18 19,16 23,16 L23,16 L23,10 L33,20 L23,29 L23,24 C19,24 8,27 6,30 Z"></svg></button>'},share:{tpl:'<div class="fancybox-share"><h1>{{SHARE}}</h1><p class="fancybox-share__links"><a class="fancybox-share__button fancybox-share__button--fb" href="https://www.facebook.com/sharer/sharer.php?u={{url}}"><svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="m287 456v-299c0-21 6-35 35-35h38v-63c-7-1-29-3-55-3-54 0-91 33-91 94v306m143-254h-205v72h196" /></svg><span>Facebook</span></a><a class="fancybox-share__button fancybox-share__button--pt" href="https://www.pinterest.com/pin/create/button/?url={{url}}&description={{descr}}&media={{media}}"><svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="m265 56c-109 0-164 78-164 144 0 39 15 74 47 87 5 2 10 0 12-5l4-19c2-6 1-8-3-13-9-11-15-25-15-45 0-58 43-110 113-110 62 0 96 38 96 88 0 67-30 122-73 122-24 0-42-19-36-44 6-29 20-60 20-81 0-19-10-35-31-35-25 0-44 26-44 60 0 21 7 36 7 36l-30 125c-8 37-1 83 0 87 0 3 4 4 5 2 2-3 32-39 42-75l16-64c8 16 31 29 56 29 74 0 124-67 124-157 0-69-58-132-146-132z" fill="#fff"/></svg><span>Pinterest</span></a><a class="fancybox-share__button fancybox-share__button--tw" href="https://twitter.com/intent/tweet?url={{url}}&text={{descr}}"><svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path d="m456 133c-14 7-31 11-47 13 17-10 30-27 37-46-15 10-34 16-52 20-61-62-157-7-141 75-68-3-129-35-169-85-22 37-11 86 26 109-13 0-26-4-37-9 0 39 28 72 65 80-12 3-25 4-37 2 10 33 41 57 77 57-42 30-77 38-122 34 170 111 378-32 359-208 16-11 30-25 41-42z" /></svg><span>Twitter</span></a></p><p><input class="fancybox-share__input" type="text" value="{{url_raw}}" /></p></div>'}}),e(t).on("click","[data-fancybox-share]",(function(){var t,n,i,o,r=e.fancybox.getInstance();r&&(t=!1===r.current.opts.hash?r.current.src:window.location,n=r.current.opts.share.tpl.replace(/\{\{media\}\}/g,"image"===r.current.type?encodeURIComponent(r.current.src):"").replace(/\{\{url\}\}/g,encodeURIComponent(t)).replace(/\{\{url_raw\}\}/g,(i=t,o={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},String(i).replace(/[&<>"'`=\/]/g,(function(t){return o[t]})))).replace(/\{\{descr\}\}/g,r.$caption?encodeURIComponent(r.$caption.text()):""),e.fancybox.open({src:r.translate(r,n),type:"html",opts:{animationEffect:"fade",animationDuration:250,afterLoad:function(t,e){e.$content.find(".fancybox-share__links a").click((function(){return window.open(this.href,"Share","width=550, height=450"),!1}))}}}))}))}(document,window.jQuery||jQuery),function(t,e,n){"use strict";n.escapeSelector||(n.escapeSelector=function(t){return(t+"").replace(/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g,(function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}))});var i=!0,o=null,r=null;function s(){var t=e.location.hash.substr(1),n=t.split("-"),i=n.length>1&&/^\+?\d+$/.test(n[n.length-1])&&parseInt(n.pop(-1),10)||1;return i<1&&(i=1),{hash:t,index:i,gallery:n.join("-")}}function a(t){var e;""!==t.gallery&&((e=n("[data-fancybox='"+n.escapeSelector(t.gallery)+"']").eq(t.index-1)).length||(e=n("#"+n.escapeSelector(t.gallery))),e.length&&(i=!1,e.trigger("click")))}function l(t){var e;return!!t&&((e=t.current?t.current.opts:t.opts).hash||(e.$orig?e.$orig.data("fancybox"):""))}n((function(){!1!==n.fancybox.defaults.hash&&(n(t).on({"onInit.fb":function(t,e){var n,i;!1!==e.group[e.currIndex].opts.hash&&(n=s(),(i=l(e))&&n.gallery&&i==n.gallery&&(e.currIndex=n.index-1))},"beforeShow.fb":function(n,s,a){var c;a&&!1!==a.opts.hash&&(c=l(s))&&""!==c&&(e.location.hash.indexOf(c)<0&&(s.opts.origHash=e.location.hash),o=c+(s.group.length>1?"-"+(a.index+1):""),"replaceState"in e.history?(r&&clearTimeout(r),r=setTimeout((function(){e.history[i?"pushState":"replaceState"]({},t.title,e.location.pathname+e.location.search+"#"+o),r=null,i=!1}),300)):e.location.hash=o)},"beforeClose.fb":function(i,s,a){var c,u;r&&clearTimeout(r),!1!==a.opts.hash&&(c=l(s),u=s&&s.opts.origHash?s.opts.origHash:"",c&&""!==c&&("replaceState"in history?e.history.replaceState({},t.title,e.location.pathname+e.location.search+u):(e.location.hash=u,n(e).scrollTop(s.scrollTop).scrollLeft(s.scrollLeft))),o=null)}}),n(e).on("hashchange.fb",(function(){var t=s();n.fancybox.getInstance()?!o||o===t.gallery+"-"+t.index||1===t.index&&o==t.gallery||(o=null,n.fancybox.close()):""!==t.gallery&&a(t)})),setTimeout((function(){a(s())}),50))}))}(document,window,window.jQuery||jQuery),function(t,e){"use strict";var n=(new Date).getTime();e(t).on({"onInit.fb":function(t,e,i){e.$refs.stage.on("mousewheel DOMMouseScroll wheel MozMousePixelScroll",(function(t){var i=e.current,o=(new Date).getTime();e.group.length<1||!1===i.opts.wheel||"auto"===i.opts.wheel&&"image"!==i.type||(t.preventDefault(),t.stopPropagation(),i.$slide.hasClass("fancybox-animated")||(t=t.originalEvent||t,o-n<250||(n=o,e[(-t.deltaY||-t.deltaX||t.wheelDelta||-t.detail)<0?"next":"previous"]())))}))}})}(document,window.jQuery||jQuery),function(t){t.fn.appear=function(e,n){var i=t.extend({data:void 0,one:!0,accX:0,accY:0},n);return this.each((function(){var n=t(this);if(n.appeared=!1,e){var o=t(window),r=function(){if(n.is(":visible")){var t=o.scrollLeft(),e=o.scrollTop(),r=n.offset(),s=r.left,a=r.top,l=i.accX,c=i.accY,u=n.height(),h=o.height(),d=n.width(),p=o.width();a+u+c>=e&&a<=e+h+c&&s+d+l>=t&&s<=t+p+l?n.appeared||n.trigger("appear",i.data):n.appeared=!1}else n.appeared=!1},s=function(){if(n.appeared=!0,i.one){o.unbind("scroll",r);var s=t.inArray(r,t.fn.appear.checks);s>=0&&t.fn.appear.checks.splice(s,1)}e.apply(this,arguments)};i.one?n.one("appear",i.data,s):n.bind("appear",i.data,s),o.scroll(r),t.fn.appear.checks.push(r),r()}else n.trigger("appear",i.data)}))},t.extend(t.fn.appear,{checks:[],timeout:null,checkAll:function(){var e=t.fn.appear.checks.length;if(e>0)for(;e--;)t.fn.appear.checks[e]()},run:function(){t.fn.appear.timeout&&clearTimeout(t.fn.appear.timeout),t.fn.appear.timeout=setTimeout(t.fn.appear.checkAll,20)}}),t.each(["append","prepend","after","before","attr","removeAttr","addClass","removeClass","toggleClass","remove","css","show","hide"],(function(e,n){var i=t.fn[n];i&&(t.fn[n]=function(){var e=i.apply(this,arguments);return t.fn.appear.run(),e})}))}(jQuery),function(t){function e(){}function n(t){function n(e){e.prototype.option||(e.prototype.option=function(e){t.isPlainObject(e)&&(this.options=t.extend(!0,this.options,e))})}function o(e,n){t.fn[e]=function(o){if("string"==typeof o){for(var s=i.call(arguments,1),a=0,l=this.length;l>a;a++){var c=this[a],u=t.data(c,e);if(u)if(t.isFunction(u[o])&&"_"!==o.charAt(0)){var h=u[o].apply(u,s);if(void 0!==h)return h}else r("no such method '"+o+"' for "+e+" instance");else r("cannot call methods on "+e+" prior to initialization; attempted to call '"+o+"'")}return this}return this.each((function(){var i=t.data(this,e);i?(i.option(o),i._init()):(i=new n(this,o),t.data(this,e,i))}))}}if(t){var r="undefined"==typeof console?e:function(t){console.error(t)};return t.bridget=function(t,e){n(e),o(t,e)},t.bridget}}var i=Array.prototype.slice;"function"==typeof define&&define.amd?define("jquery-bridget/jquery.bridget",["jquery"],n):"object"==typeof exports?n(require("jquery")):n(t.jQuery)}(window),function(t){function e(e){var n=t.event;return n.target=n.target||n.srcElement||e,n}var n=document.documentElement,i=function(){};n.addEventListener?i=function(t,e,n){t.addEventListener(e,n,!1)}:n.attachEvent&&(i=function(t,n,i){t[n+i]=i.handleEvent?function(){var n=e(t);i.handleEvent.call(i,n)}:function(){var n=e(t);i.call(t,n)},t.attachEvent("on"+n,t[n+i])});var o=function(){};n.removeEventListener?o=function(t,e,n){t.removeEventListener(e,n,!1)}:n.detachEvent&&(o=function(t,e,n){t.detachEvent("on"+e,t[e+n]);try{delete t[e+n]}catch(i){t[e+n]=void 0}});var r={bind:i,unbind:o};"function"==typeof define&&define.amd?define("eventie/eventie",r):"object"==typeof exports?module.exports=r:t.eventie=r}(this),function(t){function e(t){"function"==typeof t&&(e.isReady?t():s.push(t))}function n(t){var n="readystatechange"===t.type&&"complete"!==r.readyState;e.isReady||n||i()}function i(){e.isReady=!0;for(var t=0,n=s.length;n>t;t++){(0,s[t])()}}function o(o){return"complete"===r.readyState?i():(o.bind(r,"DOMContentLoaded",n),o.bind(r,"readystatechange",n),o.bind(t,"load",n)),e}var r=t.document,s=[];e.isReady=!1,"function"==typeof define&&define.amd?define("doc-ready/doc-ready",["eventie/eventie"],o):"object"==typeof exports?module.exports=o(require("eventie")):t.docReady=o(t.eventie)}(window),function(){function t(){}function e(t,e){for(var n=t.length;n--;)if(t[n].listener===e)return n;return-1}function n(t){return function(){return this[t].apply(this,arguments)}}var i=t.prototype,o=this,r=o.EventEmitter;i.getListeners=function(t){var e,n,i=this._getEvents();if(t instanceof RegExp)for(n in e={},i)i.hasOwnProperty(n)&&t.test(n)&&(e[n]=i[n]);else e=i[t]||(i[t]=[]);return e},i.flattenListeners=function(t){var e,n=[];for(e=0;t.length>e;e+=1)n.push(t[e].listener);return n},i.getListenersAsObject=function(t){var e,n=this.getListeners(t);return n instanceof Array&&((e={})[t]=n),e||n},i.addListener=function(t,n){var i,o=this.getListenersAsObject(t),r="object"==typeof n;for(i in o)o.hasOwnProperty(i)&&-1===e(o[i],n)&&o[i].push(r?n:{listener:n,once:!1});return this},i.on=n("addListener"),i.addOnceListener=function(t,e){return this.addListener(t,{listener:e,once:!0})},i.once=n("addOnceListener"),i.defineEvent=function(t){return this.getListeners(t),this},i.defineEvents=function(t){for(var e=0;t.length>e;e+=1)this.defineEvent(t[e]);return this},i.removeListener=function(t,n){var i,o,r=this.getListenersAsObject(t);for(o in r)r.hasOwnProperty(o)&&(-1!==(i=e(r[o],n))&&r[o].splice(i,1));return this},i.off=n("removeListener"),i.addListeners=function(t,e){return this.manipulateListeners(!1,t,e)},i.removeListeners=function(t,e){return this.manipulateListeners(!0,t,e)},i.manipulateListeners=function(t,e,n){var i,o,r=t?this.removeListener:this.addListener,s=t?this.removeListeners:this.addListeners;if("object"!=typeof e||e instanceof RegExp)for(i=n.length;i--;)r.call(this,e,n[i]);else for(i in e)e.hasOwnProperty(i)&&(o=e[i])&&("function"==typeof o?r.call(this,i,o):s.call(this,i,o));return this},i.removeEvent=function(t){var e,n=typeof t,i=this._getEvents();if("string"===n)delete i[t];else if(t instanceof RegExp)for(e in i)i.hasOwnProperty(e)&&t.test(e)&&delete i[e];else delete this._events;return this},i.removeAllListeners=n("removeEvent"),i.emitEvent=function(t,e){var n,i,o,r=this.getListenersAsObject(t);for(o in r)if(r.hasOwnProperty(o))for(i=r[o].length;i--;)!0===(n=r[o][i]).once&&this.removeListener(t,n.listener),n.listener.apply(this,e||[])===this._getOnceReturnValue()&&this.removeListener(t,n.listener);return this},i.trigger=n("emitEvent"),i.emit=function(t){var e=Array.prototype.slice.call(arguments,1);return this.emitEvent(t,e)},i.setOnceReturnValue=function(t){return this._onceReturnValue=t,this},i._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},i._getEvents=function(){return this._events||(this._events={})},t.noConflict=function(){return o.EventEmitter=r,t},"function"==typeof define&&define.amd?define("eventEmitter/EventEmitter",[],(function(){return t})):"object"==typeof module&&module.exports?module.exports=t:o.EventEmitter=t}.call(this),function(t){function e(t){if(t){if("string"==typeof i[t])return t;t=t.charAt(0).toUpperCase()+t.slice(1);for(var e,o=0,r=n.length;r>o;o++)if(e=n[o]+t,"string"==typeof i[e])return e}}var n="Webkit Moz ms Ms O".split(" "),i=document.documentElement.style;"function"==typeof define&&define.amd?define("get-style-property/get-style-property",[],(function(){return e})):"object"==typeof exports?module.exports=e:t.getStyleProperty=e}(window),function(t){function e(t){var e=parseFloat(t);return-1===t.indexOf("%")&&!isNaN(e)&&e}function n(n){function r(){if(!u){u=!0;var o=t.getComputedStyle;if(a=function(){var t=o?function(t){return o(t,null)}:function(t){return t.currentStyle};return function(e){var n=t(e);return n||i("Style returned "+n+". Are you running this code in a hidden iframe on Firefox? See http://bit.ly/getsizebug1"),n}}(),l=n("boxSizing")){var r=document.createElement("div");r.style.width="200px",r.style.padding="1px 2px 3px 4px",r.style.borderStyle="solid",r.style.borderWidth="1px 2px 3px 4px",r.style[l]="border-box";var s=document.body||document.documentElement;s.appendChild(r);var h=a(r);c=200===e(h.width),s.removeChild(r)}}}function s(e,n){if(t.getComputedStyle||-1===n.indexOf("%"))return n;var i=e.style,o=i.left,r=e.runtimeStyle,s=r&&r.left;return s&&(r.left=e.currentStyle.left),i.left=n,n=i.pixelLeft,i.left=o,s&&(r.left=s),n}var a,l,c,u=!1;return function(t){if(r(),"string"==typeof t&&(t=document.querySelector(t)),t&&"object"==typeof t&&t.nodeType){var n=a(t);if("none"===n.display)return function(){for(var t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},e=0,n=o.length;n>e;e++)t[o[e]]=0;return t}();var i={};i.width=t.offsetWidth,i.height=t.offsetHeight;for(var u=i.isBorderBox=!(!l||!n[l]||"border-box"!==n[l]),h=0,d=o.length;d>h;h++){var p=o[h],f=n[p];f=s(t,f);var g=parseFloat(f);i[p]=isNaN(g)?0:g}var m=i.paddingLeft+i.paddingRight,v=i.paddingTop+i.paddingBottom,y=i.marginLeft+i.marginRight,b=i.marginTop+i.marginBottom,_=i.borderLeftWidth+i.borderRightWidth,w=i.borderTopWidth+i.borderBottomWidth,x=u&&c,C=e(n.width);!1!==C&&(i.width=C+(x?0:m+_));var E=e(n.height);return!1!==E&&(i.height=E+(x?0:v+w)),i.innerWidth=i.width-(m+_),i.innerHeight=i.height-(v+w),i.outerWidth=i.width+y,i.outerHeight=i.height+b,i}}}var i="undefined"==typeof console?function(){}:function(t){console.error(t)},o=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"];"function"==typeof define&&define.amd?define("get-size/get-size",["get-style-property/get-style-property"],n):"object"==typeof exports?module.exports=n(require("desandro-get-style-property")):t.getSize=n(t.getStyleProperty)}(window),function(t){function e(t,e){return t[o](e)}function n(t){t.parentNode||document.createDocumentFragment().appendChild(t)}var i,o=function(){if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],n=0,i=e.length;i>n;n++){var o=e[n]+"MatchesSelector";if(t[o])return o}}();if(o){var r=e(document.createElement("div"),"div");i=r?e:function(t,i){return n(t),e(t,i)}}else i=function(t,e){n(t);for(var i=t.parentNode.querySelectorAll(e),o=0,r=i.length;r>o;o++)if(i[o]===t)return!0;return!1};"function"==typeof define&&define.amd?define("matches-selector/matches-selector",[],(function(){return i})):"object"==typeof exports?module.exports=i:window.matchesSelector=i}(Element.prototype),function(t){function e(t,e,n){function o(t,e){t&&(this.element=t,this.layout=e,this.position={x:0,y:0},this._create())}var r=n("transition"),s=n("transform"),a=r&&s,l=!!n("perspective"),c={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"otransitionend",transition:"transitionend"}[r],u=["transform","transition","transitionDuration","transitionProperty"],h=function(){for(var t={},e=0,i=u.length;i>e;e++){var o=u[e],r=n(o);r&&r!==o&&(t[o]=r)}return t}();(function(t,e){for(var n in e)t[n]=e[n]})(o.prototype,t.prototype),o.prototype._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}},this.css({position:"absolute"})},o.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},o.prototype.getSize=function(){this.size=e(this.element)},o.prototype.css=function(t){var e=this.element.style;for(var n in t){e[h[n]||n]=t[n]}},o.prototype.getPosition=function(){var t=i(this.element),e=this.layout.options,n=e.isOriginLeft,o=e.isOriginTop,r=parseInt(t[n?"left":"right"],10),s=parseInt(t[o?"top":"bottom"],10);r=isNaN(r)?0:r,s=isNaN(s)?0:s;var a=this.layout.size;r-=n?a.paddingLeft:a.paddingRight,s-=o?a.paddingTop:a.paddingBottom,this.position.x=r,this.position.y=s},o.prototype.layoutPosition=function(){var t=this.layout.size,e=this.layout.options,n={};e.isOriginLeft?(n.left=this.position.x+t.paddingLeft+"px",n.right=""):(n.right=this.position.x+t.paddingRight+"px",n.left=""),e.isOriginTop?(n.top=this.position.y+t.paddingTop+"px",n.bottom=""):(n.bottom=this.position.y+t.paddingBottom+"px",n.top=""),this.css(n),this.emitEvent("layout",[this])};var d=l?function(t,e){return"translate3d("+t+"px, "+e+"px, 0)"}:function(t,e){return"translate("+t+"px, "+e+"px)"};o.prototype._transitionTo=function(t,e){this.getPosition();var n=this.position.x,i=this.position.y,o=parseInt(t,10),r=parseInt(e,10),s=o===this.position.x&&r===this.position.y;if(this.setPosition(t,e),!s||this.isTransitioning){var a=t-n,l=e-i,c={},u=this.layout.options;a=u.isOriginLeft?a:-a,l=u.isOriginTop?l:-l,c.transform=d(a,l),this.transition({to:c,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0})}else this.layoutPosition()},o.prototype.goTo=function(t,e){this.setPosition(t,e),this.layoutPosition()},o.prototype.moveTo=a?o.prototype._transitionTo:o.prototype.goTo,o.prototype.setPosition=function(t,e){this.position.x=parseInt(t,10),this.position.y=parseInt(e,10)},o.prototype._nonTransition=function(t){for(var e in this.css(t.to),t.isCleaning&&this._removeStyles(t.to),t.onTransitionEnd)t.onTransitionEnd[e].call(this)},o.prototype._transition=function(t){if(parseFloat(this.layout.options.transitionDuration)){var e=this._transn;for(var n in t.onTransitionEnd)e.onEnd[n]=t.onTransitionEnd[n];for(n in t.to)e.ingProperties[n]=!0,t.isCleaning&&(e.clean[n]=!0);if(t.from){this.css(t.from);this.element.offsetHeight;null}this.enableTransition(t.to),this.css(t.to),this.isTransitioning=!0}else this._nonTransition(t)};var p=s&&function(t){return t.replace(/([A-Z])/g,(function(t){return"-"+t.toLowerCase()}))}(s)+",opacity";o.prototype.enableTransition=function(){this.isTransitioning||(this.css({transitionProperty:p,transitionDuration:this.layout.options.transitionDuration}),this.element.addEventListener(c,this,!1))},o.prototype.transition=o.prototype[r?"_transition":"_nonTransition"],o.prototype.onwebkitTransitionEnd=function(t){this.ontransitionend(t)},o.prototype.onotransitionend=function(t){this.ontransitionend(t)};var f={"-webkit-transform":"transform","-moz-transform":"transform","-o-transform":"transform"};o.prototype.ontransitionend=function(t){if(t.target===this.element){var e=this._transn,n=f[t.propertyName]||t.propertyName;if(delete e.ingProperties[n],function(t){for(var e in t)return!1;return!0}(e.ingProperties)&&this.disableTransition(),n in e.clean&&(this.element.style[t.propertyName]="",delete e.clean[n]),n in e.onEnd)e.onEnd[n].call(this),delete e.onEnd[n];this.emitEvent("transitionEnd",[this])}},o.prototype.disableTransition=function(){this.removeTransitionStyles(),this.element.removeEventListener(c,this,!1),this.isTransitioning=!1},o.prototype._removeStyles=function(t){var e={};for(var n in t)e[n]="";this.css(e)};var g={transitionProperty:"",transitionDuration:""};return o.prototype.removeTransitionStyles=function(){this.css(g)},o.prototype.removeElem=function(){this.element.parentNode.removeChild(this.element),this.emitEvent("remove",[this])},o.prototype.remove=function(){if(r&&parseFloat(this.layout.options.transitionDuration)){var t=this;this.on("transitionEnd",(function(){return t.removeElem(),!0})),this.hide()}else this.removeElem()},o.prototype.reveal=function(){delete this.isHidden,this.css({display:""});var t=this.layout.options;this.transition({from:t.hiddenStyle,to:t.visibleStyle,isCleaning:!0})},o.prototype.hide=function(){this.isHidden=!0,this.css({display:""});var t=this.layout.options;this.transition({from:t.visibleStyle,to:t.hiddenStyle,isCleaning:!0,onTransitionEnd:{opacity:function(){this.isHidden&&this.css({display:"none"})}}})},o.prototype.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},o}var n=t.getComputedStyle,i=n?function(t){return n(t,null)}:function(t){return t.currentStyle};"function"==typeof define&&define.amd?define("outlayer/item",["eventEmitter/EventEmitter","get-size/get-size","get-style-property/get-style-property"],e):"object"==typeof exports?module.exports=e(require("wolfy87-eventemitter"),require("get-size"),require("desandro-get-style-property")):(t.Outlayer={},t.Outlayer.Item=e(t.EventEmitter,t.getSize,t.getStyleProperty))}(window),function(t){function e(t,e){for(var n in e)t[n]=e[n];return t}function n(t){var e=[];if(function(t){return"[object Array]"===c.call(t)}(t))e=t;else if(t&&"number"==typeof t.length)for(var n=0,i=t.length;i>n;n++)e.push(t[n]);else e.push(t);return e}function i(t,e){var n=h(e,t);-1!==n&&e.splice(n,1)}function o(o,c,h,d,p,f){function g(t,n){if("string"==typeof t&&(t=r.querySelector(t)),t&&u(t)){this.element=t,this.options=e({},this.constructor.defaults),this.option(n);var i=++m;this.element.outlayerGUID=i,v[i]=this,this._create(),this.options.isInitLayout&&this.layout()}else s&&s.error("Bad "+this.constructor.namespace+" element: "+t)}var m=0,v={};return g.namespace="outlayer",g.Item=f,g.defaults={containerStyle:{position:"relative"},isInitLayout:!0,isOriginLeft:!0,isOriginTop:!0,isResizeBound:!0,isResizingContainer:!0,transitionDuration:"1s",hiddenStyle:{opacity:0,transform:"scale(0.005) rotateX(180deg)"},visibleStyle:{opacity:1,transform:"scale(1) rotateX(0deg)"}},e(g.prototype,h.prototype),g.prototype.option=function(t){e(this.options,t)},g.prototype._create=function(){this.reloadItems(),this.stamps=[],this.stamp(this.options.stamp),e(this.element.style,this.options.containerStyle),this.options.isResizeBound&&this.bindResize()},g.prototype.reloadItems=function(){this.items=this._itemize(this.element.children)},g.prototype._itemize=function(t){for(var e=this._filterFindItemElements(t),n=this.constructor.Item,i=[],o=0,r=e.length;r>o;o++){var s=new n(e[o],this);i.push(s)}return i},g.prototype._filterFindItemElements=function(t){t=n(t);for(var e=this.options.itemSelector,i=[],o=0,r=t.length;r>o;o++){var s=t[o];if(u(s))if(e){p(s,e)&&i.push(s);for(var a=s.querySelectorAll(e),l=0,c=a.length;c>l;l++)i.push(a[l])}else i.push(s)}return i},g.prototype.getItemElements=function(){for(var t=[],e=0,n=this.items.length;n>e;e++)t.push(this.items[e].element);return t},g.prototype.layout=function(){this._resetLayout(),this._manageStamps();var t=void 0!==this.options.isLayoutInstant?this.options.isLayoutInstant:!this._isLayoutInited;this.layoutItems(this.items,t),this._isLayoutInited=!0},g.prototype._init=g.prototype.layout,g.prototype._resetLayout=function(){this.getSize()},g.prototype.getSize=function(){this.size=d(this.element)},g.prototype._getMeasurement=function(t,e){var n,i=this.options[t];i?("string"==typeof i?n=this.element.querySelector(i):u(i)&&(n=i),this[t]=n?d(n)[e]:i):this[t]=0},g.prototype.layoutItems=function(t,e){t=this._getItemsForLayout(t),this._layoutItems(t,e),this._postLayout()},g.prototype._getItemsForLayout=function(t){for(var e=[],n=0,i=t.length;i>n;n++){var o=t[n];o.isIgnored||e.push(o)}return e},g.prototype._layoutItems=function(t,e){function n(){i.emitEvent("layoutComplete",[i,t])}var i=this;if(t&&t.length){this._itemsOn(t,"layout",n);for(var o=[],r=0,s=t.length;s>r;r++){var a=t[r],l=this._getItemLayoutPosition(a);l.item=a,l.isInstant=e||a.isLayoutInstant,o.push(l)}this._processLayoutQueue(o)}else n()},g.prototype._getItemLayoutPosition=function(){return{x:0,y:0}},g.prototype._processLayoutQueue=function(t){for(var e=0,n=t.length;n>e;e++){var i=t[e];this._positionItem(i.item,i.x,i.y,i.isInstant)}},g.prototype._positionItem=function(t,e,n,i){i?t.goTo(e,n):t.moveTo(e,n)},g.prototype._postLayout=function(){this.resizeContainer()},g.prototype.resizeContainer=function(){if(this.options.isResizingContainer){var t=this._getContainerSize();t&&(this._setContainerMeasure(t.width,!0),this._setContainerMeasure(t.height,!1))}},g.prototype._getContainerSize=l,g.prototype._setContainerMeasure=function(t,e){if(void 0!==t){var n=this.size;n.isBorderBox&&(t+=e?n.paddingLeft+n.paddingRight+n.borderLeftWidth+n.borderRightWidth:n.paddingBottom+n.paddingTop+n.borderTopWidth+n.borderBottomWidth),t=Math.max(t,0),this.element.style[e?"width":"height"]=t+"px"}},g.prototype._itemsOn=function(t,e,n){function i(){return++o===r&&n.call(s),!0}for(var o=0,r=t.length,s=this,a=0,l=t.length;l>a;a++){t[a].on(e,i)}},g.prototype.ignore=function(t){var e=this.getItem(t);e&&(e.isIgnored=!0)},g.prototype.unignore=function(t){var e=this.getItem(t);e&&delete e.isIgnored},g.prototype.stamp=function(t){if(t=this._find(t)){this.stamps=this.stamps.concat(t);for(var e=0,n=t.length;n>e;e++){var i=t[e];this.ignore(i)}}},g.prototype.unstamp=function(t){if(t=this._find(t))for(var e=0,n=t.length;n>e;e++){var o=t[e];i(o,this.stamps),this.unignore(o)}},g.prototype._find=function(t){return t?("string"==typeof t&&(t=this.element.querySelectorAll(t)),t=n(t)):void 0},g.prototype._manageStamps=function(){if(this.stamps&&this.stamps.length){this._getBoundingRect();for(var t=0,e=this.stamps.length;e>t;t++){var n=this.stamps[t];this._manageStamp(n)}}},g.prototype._getBoundingRect=function(){var t=this.element.getBoundingClientRect(),e=this.size;this._boundingRect={left:t.left+e.paddingLeft+e.borderLeftWidth,top:t.top+e.paddingTop+e.borderTopWidth,right:t.right-(e.paddingRight+e.borderRightWidth),bottom:t.bottom-(e.paddingBottom+e.borderBottomWidth)}},g.prototype._manageStamp=l,g.prototype._getElementOffset=function(t){var e=t.getBoundingClientRect(),n=this._boundingRect,i=d(t);return{left:e.left-n.left-i.marginLeft,top:e.top-n.top-i.marginTop,right:n.right-e.right-i.marginRight,bottom:n.bottom-e.bottom-i.marginBottom}},g.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},g.prototype.bindResize=function(){this.isResizeBound||(o.bind(t,"resize",this),this.isResizeBound=!0)},g.prototype.unbindResize=function(){this.isResizeBound&&o.unbind(t,"resize",this),this.isResizeBound=!1},g.prototype.onresize=function(){this.resizeTimeout&&clearTimeout(this.resizeTimeout);var t=this;this.resizeTimeout=setTimeout((function(){t.resize(),delete t.resizeTimeout}),100)},g.prototype.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},g.prototype.needsResizeLayout=function(){var t=d(this.element);return this.size&&t&&t.innerWidth!==this.size.innerWidth},g.prototype.addItems=function(t){var e=this._itemize(t);return e.length&&(this.items=this.items.concat(e)),e},g.prototype.appended=function(t){var e=this.addItems(t);e.length&&(this.layoutItems(e,!0),this.reveal(e))},g.prototype.prepended=function(t){var e=this._itemize(t);if(e.length){var n=this.items.slice(0);this.items=e.concat(n),this._resetLayout(),this._manageStamps(),this.layoutItems(e,!0),this.reveal(e),this.layoutItems(n)}},g.prototype.reveal=function(t){var e=t&&t.length;if(e)for(var n=0;e>n;n++){t[n].reveal()}},g.prototype.hide=function(t){var e=t&&t.length;if(e)for(var n=0;e>n;n++){t[n].hide()}},g.prototype.getItem=function(t){for(var e=0,n=this.items.length;n>e;e++){var i=this.items[e];if(i.element===t)return i}},g.prototype.getItems=function(t){if(t&&t.length){for(var e=[],n=0,i=t.length;i>n;n++){var o=t[n],r=this.getItem(o);r&&e.push(r)}return e}},g.prototype.remove=function(t){t=n(t);var e=this.getItems(t);if(e&&e.length){this._itemsOn(e,"remove",(function(){this.emitEvent("removeComplete",[this,e])}));for(var o=0,r=e.length;r>o;o++){var s=e[o];s.remove(),i(s,this.items)}}},g.prototype.destroy=function(){var t=this.element.style;t.height="",t.position="",t.width="";for(var e=0,n=this.items.length;n>e;e++){this.items[e].destroy()}this.unbindResize();var i=this.element.outlayerGUID;delete v[i],delete this.element.outlayerGUID,a&&a.removeData(this.element,this.constructor.namespace)},g.data=function(t){var e=t&&t.outlayerGUID;return e&&v[e]},g.create=function(t,n){function i(){g.apply(this,arguments)}return Object.create?i.prototype=Object.create(g.prototype):e(i.prototype,g.prototype),i.prototype.constructor=i,i.defaults=e({},g.defaults),e(i.defaults,n),i.prototype.settings={},i.namespace=t,i.data=g.data,i.Item=function(){f.apply(this,arguments)},i.Item.prototype=new f,c((function(){for(var e=function(t){return t.replace(/(.)([A-Z])/g,(function(t,e,n){return e+"-"+n})).toLowerCase()}(t),n=r.querySelectorAll(".js-"+e),o="data-"+e+"-options",l=0,c=n.length;c>l;l++){var u,h=n[l],d=h.getAttribute(o);try{u=d&&JSON.parse(d)}catch(t){s&&s.error("Error parsing "+o+" on "+h.nodeName.toLowerCase()+(h.id?"#"+h.id:"")+": "+t);continue}var p=new i(h,u);a&&a.data(h,t,p)}})),a&&a.bridget&&a.bridget(t,i),i},g.Item=f,g}var r=t.document,s=t.console,a=t.jQuery,l=function(){},c=Object.prototype.toString,u="function"==typeof HTMLElement||"object"==typeof HTMLElement?function(t){return t instanceof HTMLElement}:function(t){return t&&"object"==typeof t&&1===t.nodeType&&"string"==typeof t.nodeName},h=Array.prototype.indexOf?function(t,e){return t.indexOf(e)}:function(t,e){for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n;return-1};"function"==typeof define&&define.amd?define("outlayer/outlayer",["eventie/eventie","doc-ready/doc-ready","eventEmitter/EventEmitter","get-size/get-size","matches-selector/matches-selector","./item"],o):"object"==typeof exports?module.exports=o(require("eventie"),require("doc-ready"),require("wolfy87-eventemitter"),require("get-size"),require("desandro-matches-selector"),require("./item")):t.Outlayer=o(t.eventie,t.docReady,t.EventEmitter,t.getSize,t.matchesSelector,t.Outlayer.Item)}(window),function(t){function e(t){function e(){t.Item.apply(this,arguments)}e.prototype=new t.Item,e.prototype._create=function(){this.id=this.layout.itemGUID++,t.Item.prototype._create.call(this),this.sortData={}},e.prototype.updateSortData=function(){if(!this.isIgnored){this.sortData.id=this.id,this.sortData["original-order"]=this.id,this.sortData.random=Math.random();var t=this.layout.options.getSortData,e=this.layout._sorters;for(var n in t){var i=e[n];this.sortData[n]=i(this.element,this)}}};var n=e.prototype.destroy;return e.prototype.destroy=function(){n.apply(this,arguments),this.css({display:""})},e}"function"==typeof define&&define.amd?define("isotope/js/item",["outlayer/outlayer"],e):"object"==typeof exports?module.exports=e(require("outlayer")):(t.Isotope=t.Isotope||{},t.Isotope.Item=e(t.Outlayer))}(window),function(t){function e(t,e){function n(t){this.isotope=t,t&&(this.options=t.options[this.namespace],this.element=t.element,this.items=t.filteredItems,this.size=t.size)}return function(){function t(t){return function(){return e.prototype[t].apply(this.isotope,arguments)}}for(var i=["_resetLayout","_getItemLayoutPosition","_manageStamp","_getContainerSize","_getElementOffset","needsResizeLayout"],o=0,r=i.length;r>o;o++){var s=i[o];n.prototype[s]=t(s)}}(),n.prototype.needsVerticalResizeLayout=function(){var e=t(this.isotope.element);return this.isotope.size&&e&&e.innerHeight!==this.isotope.size.innerHeight},n.prototype._getMeasurement=function(){this.isotope._getMeasurement.apply(this,arguments)},n.prototype.getColumnWidth=function(){this.getSegmentSize("column","Width")},n.prototype.getRowHeight=function(){this.getSegmentSize("row","Height")},n.prototype.getSegmentSize=function(t,e){var n=t+e,i="outer"+e;if(this._getMeasurement(n,i),!this[n]){var o=this.getFirstItemSize();this[n]=o&&o[i]||this.isotope.size["inner"+e]}},n.prototype.getFirstItemSize=function(){var e=this.isotope.filteredItems[0];return e&&e.element&&t(e.element)},n.prototype.layout=function(){this.isotope.layout.apply(this.isotope,arguments)},n.prototype.getSize=function(){this.isotope.getSize(),this.size=this.isotope.size},n.modes={},n.create=function(t,e){function i(){n.apply(this,arguments)}return i.prototype=new n,e&&(i.options=e),i.prototype.namespace=t,n.modes[t]=i,i},n}"function"==typeof define&&define.amd?define("isotope/js/layout-mode",["get-size/get-size","outlayer/outlayer"],e):"object"==typeof exports?module.exports=e(require("get-size"),require("outlayer")):(t.Isotope=t.Isotope||{},t.Isotope.LayoutMode=e(t.getSize,t.Outlayer))}(window),function(t){function e(t,e){var i=t.create("masonry");return i.prototype._resetLayout=function(){this.getSize(),this._getMeasurement("columnWidth","outerWidth"),this._getMeasurement("gutter","outerWidth"),this.measureColumns();var t=this.cols;for(this.colYs=[];t--;)this.colYs.push(0);this.maxY=0},i.prototype.measureColumns=function(){if(this.getContainerWidth(),!this.columnWidth){var t=this.items[0],n=t&&t.element;this.columnWidth=n&&e(n).outerWidth||this.containerWidth}this.columnWidth+=this.gutter,this.cols=Math.floor((this.containerWidth+this.gutter)/this.columnWidth),this.cols=Math.max(this.cols,1)},i.prototype.getContainerWidth=function(){var t=this.options.isFitWidth?this.element.parentNode:this.element,n=e(t);this.containerWidth=n&&n.innerWidth},i.prototype._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth%this.columnWidth,i=Math[e&&1>e?"round":"ceil"](t.size.outerWidth/this.columnWidth);i=Math.min(i,this.cols);for(var o=this._getColGroup(i),r=Math.min.apply(Math,o),s=n(o,r),a={x:this.columnWidth*s,y:r},l=r+t.size.outerHeight,c=this.cols+1-o.length,u=0;c>u;u++)this.colYs[s+u]=l;return a},i.prototype._getColGroup=function(t){if(2>t)return this.colYs;for(var e=[],n=this.cols+1-t,i=0;n>i;i++){var o=this.colYs.slice(i,i+t);e[i]=Math.max.apply(Math,o)}return e},i.prototype._manageStamp=function(t){var n=e(t),i=this._getElementOffset(t),o=this.options.isOriginLeft?i.left:i.right,r=o+n.outerWidth,s=Math.floor(o/this.columnWidth);s=Math.max(0,s);var a=Math.floor(r/this.columnWidth);a-=r%this.columnWidth?0:1,a=Math.min(this.cols-1,a);for(var l=(this.options.isOriginTop?i.top:i.bottom)+n.outerHeight,c=s;a>=c;c++)this.colYs[c]=Math.max(l,this.colYs[c])},i.prototype._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var t={height:this.maxY};return this.options.isFitWidth&&(t.width=this._getContainerFitWidth()),t},i.prototype._getContainerFitWidth=function(){for(var t=0,e=this.cols;--e&&0===this.colYs[e];)t++;return(this.cols-t)*this.columnWidth-this.gutter},i.prototype.needsResizeLayout=function(){var t=this.containerWidth;return this.getContainerWidth(),t!==this.containerWidth},i}var n=Array.prototype.indexOf?function(t,e){return t.indexOf(e)}:function(t,e){for(var n=0,i=t.length;i>n;n++){if(t[n]===e)return n}return-1};"function"==typeof define&&define.amd?define("masonry/masonry",["outlayer/outlayer","get-size/get-size"],e):"object"==typeof exports?module.exports=e(require("outlayer"),require("get-size")):t.Masonry=e(t.Outlayer,t.getSize)}(window),function(t){function e(t,e){var n=t.create("masonry"),i=n.prototype._getElementOffset,o=n.prototype.layout,r=n.prototype._getMeasurement;(function(t,e){for(var n in e)t[n]=e[n]})(n.prototype,e.prototype),n.prototype._getElementOffset=i,n.prototype.layout=o,n.prototype._getMeasurement=r;var s=n.prototype.measureColumns;n.prototype.measureColumns=function(){this.items=this.isotope.filteredItems,s.call(this)};var a=n.prototype._manageStamp;return n.prototype._manageStamp=function(){this.options.isOriginLeft=this.isotope.options.isOriginLeft,this.options.isOriginTop=this.isotope.options.isOriginTop,a.apply(this,arguments)},n}"function"==typeof define&&define.amd?define("isotope/js/layout-modes/masonry",["../layout-mode","masonry/masonry"],e):"object"==typeof exports?module.exports=e(require("../layout-mode"),require("masonry-layout")):e(t.Isotope.LayoutMode,t.Masonry)}(window),function(t){function e(t){var e=t.create("fitRows");return e.prototype._resetLayout=function(){this.x=0,this.y=0,this.maxY=0,this._getMeasurement("gutter","outerWidth")},e.prototype._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth+this.gutter,n=this.isotope.size.innerWidth+this.gutter;0!==this.x&&e+this.x>n&&(this.x=0,this.y=this.maxY);var i={x:this.x,y:this.y};return this.maxY=Math.max(this.maxY,this.y+t.size.outerHeight),this.x+=e,i},e.prototype._getContainerSize=function(){return{height:this.maxY}},e}"function"==typeof define&&define.amd?define("isotope/js/layout-modes/fit-rows",["../layout-mode"],e):"object"==typeof exports?module.exports=e(require("../layout-mode")):e(t.Isotope.LayoutMode)}(window),function(t){function e(t){var e=t.create("vertical",{horizontalAlignment:0});return e.prototype._resetLayout=function(){this.y=0},e.prototype._getItemLayoutPosition=function(t){t.getSize();var e=(this.isotope.size.innerWidth-t.size.outerWidth)*this.options.horizontalAlignment,n=this.y;return this.y+=t.size.outerHeight,{x:e,y:n}},e.prototype._getContainerSize=function(){return{height:this.y}},e}"function"==typeof define&&define.amd?define("isotope/js/layout-modes/vertical",["../layout-mode"],e):"object"==typeof exports?module.exports=e(require("../layout-mode")):e(t.Isotope.LayoutMode)}(window),function(t){function e(t){var e=[];if(function(t){return"[object Array]"===a.call(t)}(t))e=t;else if(t&&"number"==typeof t.length)for(var n=0,i=t.length;i>n;n++)e.push(t[n]);else e.push(t);return e}function n(t,e){var n=l(e,t);-1!==n&&e.splice(n,1)}function i(t,i,a,l,c){var u=t.create("isotope",{layoutMode:"masonry",isJQueryFiltering:!0,sortAscending:!0});u.Item=l,u.LayoutMode=c,u.prototype._create=function(){for(var e in this.itemGUID=0,this._sorters={},this._getSorters(),t.prototype._create.call(this),this.modes={},this.filteredItems=this.items,this.sortHistory=["original-order"],c.modes)this._initLayoutMode(e)},u.prototype.reloadItems=function(){this.itemGUID=0,t.prototype.reloadItems.call(this)},u.prototype._itemize=function(){for(var e=t.prototype._itemize.apply(this,arguments),n=0,i=e.length;i>n;n++){var o=e[n];o.id=this.itemGUID++}return this._updateItemsSortData(e),e},u.prototype._initLayoutMode=function(t){var e=c.modes[t],n=this.options[t]||{};this.options[t]=e.options?function(t,e){for(var n in e)t[n]=e[n];return t}(e.options,n):n,this.modes[t]=new e(this)},u.prototype.layout=function(){return!this._isLayoutInited&&this.options.isInitLayout?void this.arrange():void this._layout()},u.prototype._layout=function(){var t=this._getIsInstant();this._resetLayout(),this._manageStamps(),this.layoutItems(this.filteredItems,t),this._isLayoutInited=!0},u.prototype.arrange=function(t){function e(){i.reveal(n.needReveal),i.hide(n.needHide)}this.option(t),this._getIsInstant();var n=this._filter(this.items);this.filteredItems=n.matches;var i=this;this._isInstant?this._noTransition(e):e(),this._sort(),this._layout()},u.prototype._init=u.prototype.arrange,u.prototype._getIsInstant=function(){var t=void 0!==this.options.isLayoutInstant?this.options.isLayoutInstant:!this._isLayoutInited;return this._isInstant=t,t},u.prototype._filter=function(t){var e=this.options.filter;e=e||"*";for(var n=[],i=[],o=[],r=this._getFilterTest(e),s=0,a=t.length;a>s;s++){var l=t[s];if(!l.isIgnored){var c=r(l);c&&n.push(l),c&&l.isHidden?i.push(l):c||l.isHidden||o.push(l)}}return{matches:n,needReveal:i,needHide:o}},u.prototype._getFilterTest=function(t){return o&&this.options.isJQueryFiltering?function(e){return o(e.element).is(t)}:"function"==typeof t?function(e){return t(e.element)}:function(e){return a(e.element,t)}},u.prototype.updateSortData=function(t){var n;t?(t=e(t),n=this.getItems(t)):n=this.items,this._getSorters(),this._updateItemsSortData(n)},u.prototype._getSorters=function(){var t=this.options.getSortData;for(var e in t){var n=t[e];this._sorters[e]=h(n)}},u.prototype._updateItemsSortData=function(t){for(var e=t&&t.length,n=0;e&&e>n;n++){t[n].updateSortData()}};var h=function(t){if("string"!=typeof t)return t;var e=r(t).split(" "),n=e[0],i=n.match(/^\[(.+)\]$/),o=function(t,e){return t?function(e){return e.getAttribute(t)}:function(t){var n=t.querySelector(e);return n&&s(n)}}(i&&i[1],n),a=u.sortDataParsers[e[1]];return a?function(t){return t&&a(o(t))}:function(t){return t&&o(t)}};u.sortDataParsers={parseInt:function(t){return parseInt(t,10)},parseFloat:function(t){return parseFloat(t)}},u.prototype._sort=function(){var t=this.options.sortBy;if(t){var e=function(t,e){return function(n,i){for(var o=0,r=t.length;r>o;o++){var s=t[o],a=n.sortData[s],l=i.sortData[s];if(a>l||l>a)return(a>l?1:-1)*((void 0!==e[s]?e[s]:e)?1:-1)}return 0}}([].concat.apply(t,this.sortHistory),this.options.sortAscending);this.filteredItems.sort(e),t!==this.sortHistory[0]&&this.sortHistory.unshift(t)}},u.prototype._mode=function(){var t=this.options.layoutMode,e=this.modes[t];if(!e)throw Error("No layout mode: "+t);return e.options=this.options[t],e},u.prototype._resetLayout=function(){t.prototype._resetLayout.call(this),this._mode()._resetLayout()},u.prototype._getItemLayoutPosition=function(t){return this._mode()._getItemLayoutPosition(t)},u.prototype._manageStamp=function(t){this._mode()._manageStamp(t)},u.prototype._getContainerSize=function(){return this._mode()._getContainerSize()},u.prototype.needsResizeLayout=function(){return this._mode().needsResizeLayout()},u.prototype.appended=function(t){var e=this.addItems(t);if(e.length){var n=this._filterRevealAdded(e);this.filteredItems=this.filteredItems.concat(n)}},u.prototype.prepended=function(t){var e=this._itemize(t);if(e.length){this._resetLayout(),this._manageStamps();var n=this._filterRevealAdded(e);this.layoutItems(this.filteredItems),this.filteredItems=n.concat(this.filteredItems),this.items=e.concat(this.items)}},u.prototype._filterRevealAdded=function(t){var e=this._filter(t);return this.hide(e.needHide),this.reveal(e.matches),this.layoutItems(e.matches,!0),e.matches},u.prototype.insert=function(t){var e=this.addItems(t);if(e.length){var n,i,o=e.length;for(n=0;o>n;n++)i=e[n],this.element.appendChild(i.element);var r=this._filter(e).matches;for(n=0;o>n;n++)e[n].isLayoutInstant=!0;for(this.arrange(),n=0;o>n;n++)delete e[n].isLayoutInstant;this.reveal(r)}};var d=u.prototype.remove;return u.prototype.remove=function(t){t=e(t);var i=this.getItems(t);if(d.call(this,t),i&&i.length)for(var o=0,r=i.length;r>o;o++){n(i[o],this.filteredItems)}},u.prototype.shuffle=function(){for(var t=0,e=this.items.length;e>t;t++){this.items[t].sortData.random=Math.random()}this.options.sortBy="random",this._sort(),this._layout()},u.prototype._noTransition=function(t){var e=this.options.transitionDuration;this.options.transitionDuration=0;var n=t.call(this);return this.options.transitionDuration=e,n},u.prototype.getFilteredItemElements=function(){for(var t=[],e=0,n=this.filteredItems.length;n>e;e++)t.push(this.filteredItems[e].element);return t},u}var o=t.jQuery,r=String.prototype.trim?function(t){return t.trim()}:function(t){return t.replace(/^\s+|\s+$/g,"")},s=document.documentElement.textContent?function(t){return t.textContent}:function(t){return t.innerText},a=Object.prototype.toString,l=Array.prototype.indexOf?function(t,e){return t.indexOf(e)}:function(t,e){for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n;return-1};"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size","matches-selector/matches-selector","isotope/js/item","isotope/js/layout-mode","isotope/js/layout-modes/masonry","isotope/js/layout-modes/fit-rows","isotope/js/layout-modes/vertical"],i):"object"==typeof exports?module.exports=i(require("outlayer"),require("get-size"),require("desandro-matches-selector"),require("./item"),require("./layout-mode"),require("./layout-modes/masonry"),require("./layout-modes/fit-rows"),require("./layout-modes/vertical")):t.Isotope=i(t.Outlayer,t.getSize,t.matchesSelector,t.Isotope.Item,t.Isotope.LayoutMode)}(window),jQuery,jQuery(document).ready((function(t){0<t(".offset-side-bar").length&&t(".offset-side-bar").on("click",(function(e){e.preventDefault(),e.stopPropagation(),t(".cart-group").addClass("isActive")})),0<t(".close-side-widget").length&&t(".close-side-widget").on("click",(function(e){e.preventDefault(),t(".cart-group").removeClass("isActive")})),0<t(".navSidebar-button").length&&t(".navSidebar-button").on("click",(function(e){e.preventDefault(),e.stopPropagation(),t(".info-group").addClass("isActive")})),0<t(".close-side-widget").length&&t(".close-side-widget").on("click",(function(e){e.preventDefault(),t(".info-group").removeClass("isActive")})),t("body").on("click",(function(e){t(".info-group").removeClass("isActive"),t(".cart-group").removeClass("isActive")})),t(".xs-sidebar-widget").on("click",(function(t){t.stopPropagation()})),0<t(".xs-modal-popup").length&&t(".xs-modal-popup").magnificPopup({type:"inline",fixedContentPos:!1,fixedBgPos:!0,overflowY:"auto",closeBtnInside:!1,callbacks:{beforeOpen:function(){this.st.mainClass="my-mfp-slide-bottom xs-promo-popup"}}})})),function(t){"use strict";"function"==typeof define&&define.amd?define("parollerjs",["jquery"],t):"object"==typeof module&&"object"==typeof module.exports?module.exports=t(require("jquery")):t(jQuery)}((function(t){"use strict";var e=!1,n=function(){e=!1},i=function(t,e){return t.css({"background-position":"center "+-e+"px"})},o=function(t,e){return t.css({"background-position":-e+"px center"})},r=function(t,e,n){return"none"!==n||(n=""),t.css({"-webkit-transform":"translateY("+e+"px)"+n,"-moz-transform":"translateY("+e+"px)"+n,transform:"translateY("+e+"px)"+n,transition:"transform 0.6s cubic-bezier(0, 0, 0, 1) 0s","will-change":"transform"})},s=function(t,e,n){return"none"!==n||(n=""),t.css({"-webkit-transform":"translateX("+e+"px)"+n,"-moz-transform":"translateX("+e+"px)"+n,transform:"translateX("+e+"px)"+n,transition:"transform linear","will-change":"transform"})},a=function(t,e,n){var i=t.data("paroller-factor")||n.factor;return e<576?t.data("paroller-factor-xs")||n.factorXs||i:e<=768?t.data("paroller-factor-sm")||n.factorSm||i:e<=1024?t.data("paroller-factor-md")||n.factorMd||i:e<=1200?t.data("paroller-factor-lg")||n.factorLg||i:e<=1920&&(t.data("paroller-factor-xl")||n.factorXl)||i};t.fn.paroller=function(l){var c=t(window).height(),u=t(document).height();return l=t.extend({factor:0,factorXs:0,factorSm:0,factorMd:0,factorLg:0,factorXl:0,type:"background",direction:"vertical"},l),this.each((function(){var h=t(this),d=t(window).width(),p=h.offset().top,f=h.outerHeight(),g=h.data("paroller-type"),m=h.data("paroller-direction"),v=h.css("transform"),y=g||l.type,b=m||l.direction,_=a(h,d,l),w=function(t,e){return Math.round(t*e)}(p,_),x=function(t,e,n,i){return Math.round((t-n/2+i)*e)}(p,_,c,f);"background"===y?"vertical"===b?i(h,w):"horizontal"===b&&o(h,w):"foreground"===y&&("vertical"===b?r(h,x,v):"horizontal"===b&&s(h,x,v)),t(window).on("resize",(function(){var g=t(this).scrollTop();d=t(window).width(),p=h.offset().top,f=h.outerHeight(),_=a(h,d,l),w=Math.round(p*_),x=Math.round((p-c/2+f)*_),e||(window.requestAnimationFrame(n),e=!0),"background"===y?(function(t){t.css({"background-position":"unset"})}(h),"vertical"===b?i(h,w):"horizontal"===b&&o(h,w)):"foreground"===y&&g<=u&&(function(t){t.css({transform:"unset",transition:"unset"})}(h),"vertical"===b?r(h,x):"horizontal"===b&&s(h,x))})),t(window).on("scroll",(function(){var a=t(this).scrollTop();u=t(document).height(),w=Math.round((p-a)*_),x=Math.round((p-c/2+f-a)*_),e||(window.requestAnimationFrame(n),e=!0),"background"===y?"vertical"===b?i(h,w):"horizontal"===b&&o(h,w):"foreground"===y&&a<=u&&("vertical"===b?r(h,x,v):"horizontal"===b&&s(h,x,v))}))}))}})),function(t){"use strict";function e(){if(t(".main-header").length){var e=t(window).scrollTop(),n=t(".main-header"),i=t(".scroll-top");e>=110?(n.addClass("fixed-header"),i.addClass("open")):(n.removeClass("fixed-header"),i.removeClass("open"))}}if(t(".preloader-close").length&&t(".preloader-close").on("click",(function(){t(".loader-wrap").delay(200).fadeOut(500)})),e(),t(".main-header li.dropdown ul").length&&t(".main-header .navigation li.dropdown").append('<div class="dropdown-btn"><span class="fas fa-angle-down"></span></div>'),t(".mobile-menu").length){t(".mobile-menu .menu-box");var n=t(".main-header .menu-area .main-menu").html();t(".mobile-menu .menu-box .menu-outer").append(n),t(".sticky-header .main-menu").append(n),t(".mobile-menu li.dropdown .dropdown-btn").on("click",(function(){t(this).toggleClass("open"),t(this).prev("ul").slideToggle(500)})),t(".mobile-menu li.dropdown .dropdown-btn").on("click",(function(){t(this).prev(".megamenu").slideToggle(900)})),t(".mobile-nav-toggler").on("click",(function(){t("body").addClass("mobile-menu-visible")})),t(".mobile-menu .menu-backdrop,.mobile-menu .close-btn").on("click",(function(){t("body").removeClass("mobile-menu-visible")}))}(t(".scroll-to-target").length&&t(".scroll-to-target").on("click",(function(){var e=t(this).attr("data-target");t("html, body").animate({scrollTop:t(e).offset().top},1e3)})),t(".wow").length)&&new WOW({mobile:!1}).init();function i(){if(t(".sortable-masonry").length){var e=t(window),n=t(".sortable-masonry .items-container"),i=t(".filter-btns");n.isotope({filter:"*",masonry:{columnWidth:".masonry-item.small-column"},animationOptions:{duration:500,easing:"linear"}}),i.find("li").on("click",(function(){var e=t(this).attr("data-filter");try{n.isotope({filter:e,animationOptions:{duration:500,easing:"linear",queue:!1}})}catch(t){}return!1})),e.on("resize",(function(){var t=i.find("li.active").attr("data-filter");n.isotope({filter:t,animationOptions:{duration:500,easing:"linear",queue:!1}})}));var o=t(".filter-btns li");o.on("click",(function(){var e=t(this);e.hasClass("active")||(o.removeClass("active"),e.addClass("active"))}))}}function o(){t(".page_direction").length&&t(".direction_switch button").on("click",(function(){t("body").toggleClass((function(){return t(this).is(".rtl, .ltr")?"rtl ltr":"rtl"}))}))}t("#contact-form").length&&t("#contact-form").validate({rules:{username:{required:!0},email:{required:!0,email:!0},phone:{required:!0},subject:{required:!0},message:{required:!0}}}),t(".count-box").length&&t(".count-box").appear((function(){var e=t(this),n=e.find(".count-text").attr("data-stop"),i=parseInt(e.find(".count-text").attr("data-speed"),10);e.hasClass("counted")||(e.addClass("counted"),t({countNum:e.find(".count-text").text()}).animate({countNum:n},{duration:i,easing:"linear",step:function(){e.find(".count-text").text(Math.floor(this.countNum))},complete:function(){e.find(".count-text").text(this.countNum)}}))}),{accY:0}),t(".lightbox-image").length&&t(".lightbox-image").fancybox({openEffect:"fade",closeEffect:"fade",helpers:{media:{}}}),t(".tabs-box").length&&t(".tabs-box .tab-buttons .tab-btn").on("click",(function(e){e.preventDefault();var n=t(t(this).attr("data-tab"));if(t(n).is(":visible"))return!1;n.parents(".tabs-box").find(".tab-buttons").find(".tab-btn").removeClass("active-btn"),t(this).addClass("active-btn"),n.parents(".tabs-box").find(".tabs-content").find(".tab").fadeOut(0),n.parents(".tabs-box").find(".tabs-content").find(".tab").removeClass("active-tab"),t(n).fadeIn(300),t(n).addClass("active-tab")})),t(".accordion-box").length&&t(".accordion-box").on("click",".acc-btn",(function(){var e=t(this).parents(".accordion-box"),n=t(this).parents(".accordion");if(!0!==t(this).hasClass("active")&&t(e).find(".accordion .acc-btn").removeClass("active"),t(this).next(".acc-content").is(":visible"))return!1;t(this).addClass("active"),t(e).children(".accordion").removeClass("active-block"),t(e).find(".accordion").children(".acc-content").slideUp(300),n.addClass("active-block"),t(this).next(".acc-content").slideDown(300)})),t(".banner-carousel").length&&t(".banner-carousel").owlCarousel({loop:!0,margin:0,nav:!0,animateOut:"fadeOut",animateIn:"fadeIn",active:!0,smartSpeed:1e3,autoplay:1e4,navText:['<span class="icon-19"></span>','<span class="icon-18"></span>'],responsive:{0:{items:1},600:{items:1},800:{items:1},1024:{items:1}}}),t(".single-item-carousel").length&&t(".single-item-carousel").owlCarousel({loop:!0,margin:30,nav:!1,smartSpeed:500,autoplay:4e3,navText:['<span class="icon-19"></span>','<span class="icon-18"></span>'],responsive:{0:{items:1},480:{items:1},600:{items:1},800:{items:1},1200:{items:1}}}),t(".two-column-carousel").length&&t(".two-column-carousel").owlCarousel({loop:!0,margin:30,nav:!0,smartSpeed:500,autoplay:4e3,navText:['<span class="icon-19"></span>','<span class="icon-18"></span>'],responsive:{0:{items:1},480:{items:1},600:{items:1},800:{items:2},1024:{items:2}}}),t(".three-item-carousel").length&&t(".three-item-carousel").owlCarousel({loop:!0,margin:30,nav:!0,smartSpeed:500,autoplay:4e3,navText:['<span class="icon-19"></span>','<span class="icon-18"></span>'],responsive:{0:{items:1},480:{items:1},600:{items:2},800:{items:2},1024:{items:3}}}),t(".four-item-carousel").length&&t(".four-item-carousel").owlCarousel({loop:!0,margin:30,nav:!0,smartSpeed:500,autoplay:4e3,navText:['<span class="icon-19"></span>','<span class="icon-18"></span>'],responsive:{0:{items:1},600:{items:2},800:{items:3},1024:{items:4},1200:{items:4}}}),t(".five-item-carousel").length&&t(".five-item-carousel").owlCarousel({loop:!0,margin:30,nav:!0,smartSpeed:500,autoplay:4e3,navText:['<span class="fas fa-angle-left"></span>','<span class="fas fa-angle-right"></span>'],responsive:{0:{items:1},600:{items:2},800:{items:3},1024:{items:4},1200:{items:5}}}),t(".project-carousel").length&&t(".project-carousel").owlCarousel({loop:!0,margin:20,nav:!0,smartSpeed:500,autoplay:4e3,navText:['<span class="icon-19"></span>','<span class="icon-18"></span>'],responsive:{0:{items:1},600:{items:2},800:{items:3},1024:{items:4},1200:{items:4}}}),t("#search-popup").length&&(t(".search-toggler").on("click",(function(){t("#search-popup").addClass("popup-visible")})),t(document).keydown((function(e){27===e.keyCode&&t("#search-popup").removeClass("popup-visible")})),t(".close-search,.search-popup .overlay-layer").on("click",(function(){t("#search-popup").removeClass("popup-visible")}))),t(".scroll-nav").length&&t(".scroll-nav").onePageNav(),i(),t(".paroller").length&&t(".paroller").paroller({factor:.1,factorLg:.1,type:"foreground",direction:"vertical"}),t(".paroller-2").length&&t(".paroller-2").paroller({factor:-.1,factorLg:-.1,type:"foreground",direction:"vertical"}),t(".count-bar").length&&t(".count-bar").appear((function(){var e=t(this),n=e.data("percent");t(e).css("width",n).addClass("counted")}),{accY:-50}),jQuery(document).on("ready",(function(){jQuery,o()})),t(window).on("scroll",(function(){e()})),t(window).on("load",(function(){t(".loader-wrap").length&&t(".loader-wrap").delay(1e3).fadeOut(500),i()}))}(window.jQuery);
