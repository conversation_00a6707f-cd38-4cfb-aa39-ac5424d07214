<?php
namespace App\Http\Menus;

use App\Http\Controllers\HomeController;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class Menu
{
    public static function getTopMenuHtml($menuName)
    {
        $menu = Arr::get(nova_get_menus(app()->getLocale())->where('name', $menuName), '0.menuItems');
        return view('components.menu')->with([
            'menu' => $menu,
            'submenu' => false,
        ]);
    }

    public static function getSideMenuHtml($menuName,$slug,$slug2,$slug3)
    {
        $menu = Arr::get(nova_get_menus(app()->getLocale())->where('name', $menuName), '0.menuItems');
        foreach($menu as $item)
        {
            if (\Illuminate\Support\Str::slug($item['name'],'-')==$slug3)
            {
                return view('components.side-menu')->with([
                    'menu' => $menu,
                    'submenu' => false,
                    'slug'=>$slug,
                    'slug2'=>$slug2,
                    'slug3'=>$slug3,
                ]);
            }
        }
    }

}
