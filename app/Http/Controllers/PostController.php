<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\FindCertificate;
use App\Models\Sertificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Transliterator;

class PostController extends Controller
{
    public function ContactForm(Request $request)
    {
        Mail::to('<EMAIL>')->send(new  \App\Mail\ContactMail($request));
        return view('contact')->with([
            'formSubmitting' => true,
        ]);
//        return Http::asForm()->post('', [
//            'Ad Soyad' => $request->fullname,
//            'EPosta' => $request->email,
//            'Telefon' => $request->phone,
//            'Konu' => $request->subject,
//            'Mesaj' => $request->message,
//        ]);
    }

    public function InstructorForm(Request $request)
    {
        Mail::to('<EMAIL>')->send(new  \App\Mail\InstructionMail($request));

        return view('instructor-application')->with([
            'formSubmitting' => true
        ]);
//        return Http::asForm()->post('', [
//            'AdSoyad' => $request->username,
//            'Eposta' => $request->email,
//            'Telefon' => $request->phone,
//            'GorevYaptigiBirim' => $request->gorevbirim,
//            'EğitimSekli' => $request->egitimsekli,
//            'VermekIstedigiEgitim' => $request->egitimadi,
//        ]);
    }

    public function SuggestingForm(Request $request)
    {
        Mail::to('<EMAIL>')->send(new  \App\Mail\SuggestionMail($request));
        $certificate = Sertificate::where('is_active', 1)->get('title')->toArray();
        $course = Course::where('is_active', 1)->get('title')->toArray();
        $mergedArray = array_merge($certificate, $course);
        return view('training-suggestions')->with([
            'mergedarray' => $mergedArray,
            'formSubmitting' => true
        ]);
//        return Http::asForm()->post('', [
//            'AdSoyad' => $request->username,
//            'Eposta' => $request->email,
//            'Telefon' => $request->phone,
//            'GorevYaptigiBirim' => $request->meslek,
//            'EğitimSekli' => $request->unvan,
//            'VermekIstedigiEgitim' => $request->mesaj,
//        ]);
    }

    public function CertificateForm2(Request $request)
    {
        Mail::to('<EMAIL>')->send(new  \App\Mail\CertificateMail($request));

        return redirect('/anasayfa');
//        return Http::asForm()->post('', [
//            'Ad Soyad' => $request->fullname,
//            'EPosta' => $request->email,
//            'Telefon' => $request->phone,
//            'TCNo' => $request->idnumber,
//            'EgitimTipi' => $request->educationtype,
//            'KVKK' => $request->kvkk,
//        ]);
    }

    public function CourseForm(Request $request)
    {
        Mail::to('<EMAIL>')->send(new  \App\Mail\CourseMail($request));

        return redirect('/anasayfa');
//        return Http::asForm()->post('', [
//            'Ad Soyad' => $request->fullname,
//            'EPosta' => $request->email,
//            'Telefon' => $request->phone,
//            'TCNo' => $request->idnumber,
//            'EgitimTipi' => $request->educationtype,
//            'KVKK' => $request->kvkk,
//        ]);
    }

    public function CertificateForm(Request $request)
    {
        //dd($request);
        $person = FindCertificate::where('id_number', $request->idnumber)
//            ->where('full_name', $request->username)
            ->whereRaw('LOWER(full_name) LIKE ?', ['%' . Transliterator::create('tr-lower')->transliterate($request->username) . '%'])->get();
        //dd($person);
//        return Http::asForm()->post('', [
//            'TCNo' => $request->idnumber,
//            'AdSoyad' => $request->username,
//        ]);
        if ($person->count() > 0) {
            return view('query-certificate')->with([
                'formSubmitting' => true,
                'persons' => $person,
            ]);
        } else {
            return view('query-certificate')->with([
                'formSubmitting' => false,
            ]);
        }
    }


}
