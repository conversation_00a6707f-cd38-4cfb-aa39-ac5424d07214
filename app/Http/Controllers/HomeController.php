<?php

namespace App\Http\Controllers;

use App\Models\About;
use App\Models\Course;
use App\Models\News;
use App\Models\Page;
use App\Models\Sertificate;
use App\Models\Slider;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class HomeController extends Controller
{
    public function Anasayfa()
    {
//        dd( BranchPost::where('branch_id' ,4)->first());
        return view('dashboard')->with([
            'sliders' => Slider::where('is_active', 1)->orderBy('order')->get(),
            'notifications' => Sertificate::where('is_active', 1)->where('is_publish', 1)
                ->select(['title', 'about', 'slug', 'education_time'])
                ->union(Course::where('is_active', 1)->where('is_publish', 1)->select(['title', 'about', 'slug', 'education_time']))
                ->orderBy('education_time')
                ->get(),
            'news' => News::where('is_active', 1)->where('is_publish', 1)->orderBy('news_date')->get(),
        ]);
    }

    public function Education($slug)
    {
        if ($slug == 'dil-egitimlerimiz') {
            $pages = Sertificate::where('is_active', 1)->orderBy('order')->get();
            return view('certificates-grid')->with([
                'toptitle' => 'Dil Eğitimlerimiz',
                'slug' => 'dil-egitimlerimiz',
                'formslug' => 'sertifika-on-form',
                'pages' => $pages,
            ]);
        }
        if ($slug == 'bireysel-egitimlerimiz') {
            $pages = Course::where('is_active', 1)->orderBy('order')->get();
            return view('certificates-grid')->with([
                'toptitle' => 'Bireysel Eğitimlerimiz',
                'slug' => 'bireysel-egitimlerimiz',
                'formslug' => 'kurs-on-form',
                'pages' => $pages,
            ]);
        } else {
            return $this->slug($slug);
        }
    }

    public function About($slug)
    {
        $page = About::where('is_active', 1)->where('slug', $slug)->orderBy('order')->first();
        if ($page) {
            return view('about')->with([
                'toptitle' => $page->title,
                'pagedetails' => $page
            ]);
        } else {
            return $this->slug($slug);
        }
    }

    public function EducationDetail($slug)
    {
        $page = Sertificate::where('is_active', 1)->where('slug', $slug)->orderBy('order')->first();
        return view('certificates-detail')->with([
            'toptitle' => 'Dil Eğitimlerimiz',
            'slug' => 'dil-egitimlerimiz',
            'formslug' => 'sertifika-on-form',
            'pagedetails' => $page,
        ]);
    }

    public function SuggestionForm($formSubmitting = null)
    {
        $certificate = Sertificate::where('is_active', 1)->get('title')->toArray();
        $course = Course::where('is_active', 1)->get('title')->toArray();
        $mergedArray = array_merge($certificate, $course);
        if ($formSubmitting == 'OK') {
            return view('training-suggestions')->with([
                'mergedarray' => $mergedArray,
                'formSubmitting' => 'OK'
            ]);
        } else {
            return view('training-suggestions')->with([
                'mergedarray' => $mergedArray,

            ]);
        }
    }

    public function CourseDetail($slug)
    {
        $page = Course::where('is_active', 1)->where('slug', $slug)->orderBy('order')->first();
        return view('certificates-detail')->with([
            'toptitle' => 'Bireysel Eğitimlerimiz',
            'slug' => 'bireysel-egitimlerimiz',
            'formslug' => 'kurs-on-form',
            'pagedetails' => $page,
        ]);
    }

    public function News($slug)
    {
        $page = News::orderBy('news_date')->where('slug', $slug)->first();
        $pages = News::where('is_active', 1)->orderBy('news_date', 'desc')->get();
        if ($page) {
            return view('news-page')->with([
                'pagedetails' => $page,
                'toptitle' => $page->title,
                'pages' => $pages,
                'slug' => Str::slug($page->title, '-'),
                'title' => $page->title,
            ]);
        } else {
            return $this->slug($slug);
        }
    }

    public function slug($slug)
    {
        $page = Page::where('slug', $slug)->first();
        if ($page) {
            return view('detail-page')->with([
                'page' => $page,
                'toptitle' => $page->title,
                'slug' => Str::slug($page->title, '-'),
                'title' => $page->title,
            ]);
        }

        if ($slug == 'haberler') {
            $page = News::orderBy('news_date')->first();
            $pages = News::where('is_active', 1)->orderBy('news_date', 'desc')->get();
            if ($page) {
                return view('news-page')->with([
                    'pagedetails' => $page,
                    'toptitle' => $page->title,
                    'pages' => $pages,
                    'slug' => Str::slug($page->title, '-'),
                    'title' => $page->title,
                ]);
            }
        }
        if ($slug == 'iletisim') {
            return view('contact')->with([
                'toptitle' => 'İletişim'
            ]);
        }

        return redirect()->action([HomeController::class, 'Anasayfa']);
    }
}
