<?php

namespace App\Providers;

use App\Models\About;
use App\Models\Course;
use App\Models\FindCertificate;
use App\Models\News;
use App\Models\Page;
use App\Models\ResourceName;
use App\Models\Sertificate;
use App\Observers\AboutObserver;
use App\Observers\CoursObserver;
use App\Observers\FindCertificateObserver;
use App\Observers\NewsObserver;
use App\Observers\PageObserver;
use App\Observers\ResourceNameObserver;
use App\Observers\SertificateObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Page::observe(PageObserver::class);
        About::observe(AboutObserver::class);
        ResourceName::observe(ResourceNameObserver::class);
        News::observe(NewsObserver::class);
        Sertificate::observe(SertificateObserver::class);
        Course::observe(CoursObserver::class);
        FindCertificate::observe(FindCertificateObserver::class);
    }
}
