<?php

namespace App\Providers;

use App\Policies\ResourceNamePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        \OptimistDigital\MenuBuilder\Models\Menu::class => 'App\Policies\MenuPolicy',
        'App\Models\User' => 'App\Policies\UserPolicy',
        'App\Models\ResourceName' => 'App\Policies\ResourceNamePolicy',

    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
