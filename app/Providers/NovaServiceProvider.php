<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON>vel\Nova\Cards\Help;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Nova;
use <PERSON>vel\Nova\NovaApplicationServiceProvider;
use Laravel\Nova\Panel;
use OptimistDigital\NovaSettings\NovaSettings;
use OwenMelbz\RadioField\RadioButton;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Nova::sortResourcesBy(function ($resource) {
            return $resource::$priority ?? 9999;
        });
        NovaSettings::addSettingsFields([
            Panel::make('İletisim Bilgileri', [
                Number::make('Telefon Numarası', 'tel1')->rules('max:10')->help('Başında "0" olmadan yazınız'),
                Number::make('Telefon Numarası', 'tel2')->rules('max:10')->help('Başında "0" olmadan yazınız'),
                Text::make('E-Posta', 'email')->rules('email')->help('Örnek: example@example'),
                Textarea::make('Adres', 'address'),
            ]),


            Panel::make('Sosyal Medya Bilgileri', [
                Text::make('Facebook Linki', 'facebook'),
                Text::make('İnstagram Linki', 'instagram'),
                Text::make('Twitter Linki', 'twitter'),
                Text::make('Linkedin Linki', 'linkedin'),
            ]),
        ], [], 'İletişim Bilgileri');
        NovaSettings::addSettingsFields([
            RadioButton::make('Hangisi Gösterilsin ?', 'isactive')
                ->options([
                    3 => 'Popup Gösterme',
                    0 => 'Popup Metni',
                    1 => 'Popup Görseli',
                    2 => 'Popup Frame',
                ])
                ->default(1)
                ->marginBetween()
                ->toggle([
                    3 => ['popupimage', 'videoframe', 'popupmetin', 'popuplink', 'target', 'buttonactive'],
                    2 => ['popupimage', 'popupmetin'],
                    1 => ['videoframe', 'popupmetin'],
                    0 => ['popupimage', 'videoframe']
                ]),
            Textarea::make('Popup Metni', 'popupmetin'),
            Image::make('Popup Görseli', 'popupimage'),
            Text::make('Popup Frame', 'videoframe')->help('Eklenmek istenen içeriğin Embed kodu girilmelidir.'),
            Text::make('Yönlendirme Linki', 'popuplink'),
            Boolean::make('Yeni Sekmede Aç', 'target')->trueValue('_blank')->falseValue('_self'),
            RadioButton::make('Buton Gösterilsin', 'buttonactive')
                ->options([
                    0 => 'Hayır',
                    1 => 'Evet',
                ])
                ->default(0)
                ->marginBetween()
                ->toggle([0 => ['popupyazi']]),
            Text::make('Buton Yazısı', 'popupyazi'),

        ], [], 'Popup');

        NovaSettings::addSettingsFields([
            Image::make('Varsayılan Logo', 'default_logo'),
            /*            Image::make('Logo (TR)','logo_tr'),
                        Image::make('Logo (EN)','logo_en'),*/
            Image::make('Favicon Logo', 'favicon_logo'),
        ], [], 'Logo Yönetimi');

        NovaSettings::addSettingsFields([
            Text::make('Sayfa Başlığı', 'pagetitle_1'),
            Text::make('Sayfa Linki', 'pagelink_1'),
            Image::make('Sayfa Logo', 'pagelogo_1'),

            Text::make('Sayfa Başlığı', 'pagetitle_2'),
            Text::make('Sayfa Linki', 'pagelink_2'),
            Image::make('Sayfa Logo ', 'pagelogo_2'),

            Text::make('Sayfa Başlığı', 'pagetitle_3'),
            Text::make('Sayfa Linki', 'pagelink_3'),
            Image::make('Sayfa Logo', 'pagelogo_3'),

            Text::make('Sayfa Başlığı', 'pagetitle_4'),
            Text::make('Sayfa Linki', 'pagelink_4'),
            Image::make('Sayfa Logo', 'pagelogo_4'),
        ], [], 'Faydalı Linkler');

        NovaSettings::addSettingsFields([
            Text::make('Google Analytics ID ', 'analytics'),
            Textarea::make('Google Maps Iframe', 'map'),
            Text::make('Slider Boyutu', 'sliderboyut'),
            /*            Text::make('Duyuru Boyutu','duyuruboyut'),*/
            Text::make('Anasayfa Görsel Boyutu', 'anasayfa_boyut'),
            Text::make('Site Title Ön Ek', 'site_title'),
            Textarea::make('Meta Description', 'description'),
            Textarea::make('Meta Keywords', 'keywords'),
            Textarea::make('Script Alanı', 'headarea'),
        ], [], 'Site Ayarları');
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
            ->withAuthenticationRoutes()
            ->withPasswordResetRoutes()
            ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function ($user) {
            return $user->is_admin != 0;
        });
    }

    /**
     * Get the cards that should be displayed on the default Nova dashboard.
     *
     * @return array
     */
    protected function cards()
    {
        return [
            new Help,
        ];
    }

    /**
     * Get the extra dashboards that should be displayed on the Nova dashboard.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [
            new \OptimistDigital\MenuBuilder\MenuBuilder,
            new \ClassicO\NovaMediaLibrary\NovaMediaLibrary(),
            new \OptimistDigital\NovaSettings\NovaSettings,
        ];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
