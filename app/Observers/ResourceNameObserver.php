<?php

namespace App\Observers;

use App\Models\ResourceName;

class ResourceNameObserver
{
    /**
     * Handle the ResourceName "created" event.
     *
     * @param  \App\Models\ResourceName  $resourceName
     * @return void
     */
    public function created(ResourceName $resourceName)
    {
        //
    }

    /**
     * Handle the ResourceName "updated" event.
     *
     * @param  \App\Models\ResourceName  $resourceName
     * @return void
     */
    public function updated(ResourceName $resourceName)
    {
        //
    }

    /**
     * Handle the ResourceName "deleted" event.
     *
     * @param  \App\Models\ResourceName  $resourceName
     * @return void
     */
    public function deleted(ResourceName $resourceName)
    {
        //
    }

    /**
     * Handle the ResourceName "restored" event.
     *
     * @param  \App\Models\ResourceName  $resourceName
     * @return void
     */
    public function restored(ResourceName $resourceName)
    {
        //
    }

    /**
     * Handle the ResourceName "force deleted" event.
     *
     * @param  \App\Models\ResourceName  $resourceName
     * @return void
     */
    public function forceDeleted(ResourceName $resourceName)
    {
        //
    }
}
