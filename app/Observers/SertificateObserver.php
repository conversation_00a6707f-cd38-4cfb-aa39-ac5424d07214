<?php

namespace App\Observers;

use App\Models\Sertificate;
use Illuminate\Support\Str;

class SertificateObserver
{
    /**
     * Handle the Sertificate "created" event.
     *
     * @param  \App\Models\Sertificate  $sertificate
     * @return void
     */
    public function created(Sertificate $sertificate)
    {
        //
    }

    public function saving(Sertificate $sertificate)
    {
        if ( $sertificate->is_active == 0 ) {
            $sertificate->is_publish = 0;
        }
        $sertificate->slug = Str::slug($sertificate->title,'-');
    }

    /**
     * Handle the Sertificate "updated" event.
     *
     * @param  \App\Models\Sertificate  $sertificate
     * @return void
     */
    public function updated(Sertificate $sertificate)
    {
        //
    }

    /**
     * Handle the Sertificate "deleted" event.
     *
     * @param  \App\Models\Sertificate  $sertificate
     * @return void
     */
    public function deleted(Sertificate $sertificate)
    {
        //
    }

    /**
     * Handle the Sertificate "restored" event.
     *
     * @param  \App\Models\Sertificate  $sertificate
     * @return void
     */
    public function restored(Sertificate $sertificate)
    {
        //
    }

    /**
     * Handle the Sertificate "force deleted" event.
     *
     * @param  \App\Models\Sertificate  $sertificate
     * @return void
     */
    public function forceDeleted(Sertificate $sertificate)
    {
        //
    }
}
