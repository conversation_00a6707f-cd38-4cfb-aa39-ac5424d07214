<?php

namespace App\Observers;

use App\Models\FindCertificate;
use Illuminate\Support\Str;

class FindCertificateObserver
{
    /**
     * Handle the FindCertificate "created" event.
     *
     * @param \App\Models\FindCertificate $findCertificate
     * @return void
     */
    public function saving(FindCertificate $findCertificate)
    {
        $findCertificate->full_name = ($findCertificate->full_name);
    }

    /**
     * Handle the FindCertificate "updated" event.
     *
     * @param \App\Models\FindCertificate $findCertificate
     * @return void
     */
    public function updated(FindCertificate $findCertificate)
    {
        //
    }

    /**
     * Handle the FindCertificate "deleted" event.
     *
     * @param \App\Models\FindCertificate $findCertificate
     * @return void
     */
    public function deleted(FindCertificate $findCertificate)
    {
        //
    }

    /**
     * Handle the FindCertificate "restored" event.
     *
     * @param \App\Models\FindCertificate $findCertificate
     * @return void
     */
    public function restored(FindCertificate $findCertificate)
    {
        //
    }

    /**
     * Handle the FindCertificate "force deleted" event.
     *
     * @param \App\Models\FindCertificate $findCertificate
     * @return void
     */
    public function forceDeleted(FindCertificate $findCertificate)
    {
        //
    }
}
