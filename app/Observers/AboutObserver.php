<?php

namespace App\Observers;

use App\Models\About;
use Illuminate\Support\Str;

class AboutObserver
{
    /**
     * Handle the About "created" event.
     *
     * @param  \App\Models\About  $about
     * @return void
     */
    public function created(About $about)
    {
        //
    }

    public function saving(About $about)
    {
        if ( $about->is_active == 0 ) {
            $about->is_publish = 0;
        }
        $about->slug = Str::slug($about->title,'-');
    }

    /**
     * Handle the About "updated" event.
     *
     * @param  \App\Models\About  $about
     * @return void
     */
    public function updated(About $about)
    {
        //
    }

    /**
     * Handle the About "deleted" event.
     *
     * @param  \App\Models\About  $about
     * @return void
     */
    public function deleted(About $about)
    {
        //
    }

    /**
     * Handle the About "restored" event.
     *
     * @param  \App\Models\About  $about
     * @return void
     */
    public function restored(About $about)
    {
        //
    }

    /**
     * Handle the About "force deleted" event.
     *
     * @param  \App\Models\About  $about
     * @return void
     */
    public function forceDeleted(About $about)
    {
        //
    }
}
