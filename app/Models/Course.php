<?php

namespace App\Models;

use App\Casts\PagesLayout;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class Course extends Model implements Sortable
{
    use HasFactory;
    use SortableTrait;

    protected $fillable = [
        'is_active',
        'is_publish',
        'title',
        'menu_name',
        'about',
        'content',
        'meta_keywords',
        'meta_description',
        'seo_title',
        'slug',
        'quota',
        'subscriber',
        'details',
        'education_type',
        'education_time',
        'start_date',
        'image_id'
    ];

    protected $casts = [
        'content' => PagesLayout::class,
        'details' => PagesLayout::class,
        'start_date' => 'date',
    ];

    public $sortable = [
        'order_column_name' => 'order',
        'sort_when_creating' => false,
    ];
}
