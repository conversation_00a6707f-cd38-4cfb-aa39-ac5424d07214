<?php

namespace App\Models;

use App\Casts\PagesLayout;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class About extends Model implements Sortable
{
    use HasFactory;
    use SortableTrait;

    protected $fillable = [
        'is_active',
        'is_publish',
        'title',
        'menu_name',
        'about',
        'content',
        'meta_keywords',
        'meta_description',
        'seo_title',
        'slug',
        'image_id'
    ];

    protected $casts = [
        'content' => PagesLayout::class,
    ];

    public $sortable = [
        'order_column_name' => 'order',
        'sort_when_creating' => false,
    ];
}
