<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class ResourceName extends Model implements Sortable
{
    use HasFactory;
    use SortableTrait;
    protected $fillable= ['slug','title'];

    public $sortable = [
        'order_column_name' => 'order',
        'sort_when_creating' => false,
    ];

}
