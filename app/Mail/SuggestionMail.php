<?php

namespace App\Mail;

use http\Env\Request;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SuggestionMail extends Mailable
{
    use Queueable, SerializesModels;

    public $form;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->form = $request->all();
    }

    public function envelope()
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Sürekli Eğitim Merkezi'),
            replyTo: [
                new Address('<EMAIL>', 'Sürekli Eğitim Merkezi'),
            ],
            subject: 'Eğitmen Öneri Formu',
        );
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('mail.suggestion-mail');
    }
}
