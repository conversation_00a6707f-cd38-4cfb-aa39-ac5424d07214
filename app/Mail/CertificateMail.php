<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Laravel\Nova\Tests\Fixtures\Address;

class CertificateMail extends Mailable
{
    use Queueable, SerializesModels;

    public $form;

    public function __construct($request)
    {
        $this->form = $request->all();
    }

    public function envelope()
    {
        return new Envelope(
            from: new Address(env(), 'Sürekli Eğitim Merkezi'),
            replyTo: [
                new Address('<EMAIL>', 'Sürekli Eğitim Merkezi'),
            ],
            bcc: [
                new Address('<EMAIL>', 'Burak Çelikkıran')
            ],
            subject: 'Sertifika Ön Kayıt Formu',
        );
    }

    public function build()
    {
        return $this->view('mail.certificate-mail');
    }
}
