<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CourseMail extends Mailable
{
    use Queueable, SerializesModels;

    public $form;

    public function __construct($request)
    {
        $this->form = $request->all();
    }

    public function envelope()
    {
        return new Envelope(
            from: new Address('<EMAIL>', 'Sürekli Eğitim Merkezi'),
            replyTo: [
                new Address('<EMAIL>', 'Sürekli Eğitim Merkezi'),
            ],
            subject: '<PERSON><PERSON> Ön Kayıt Formu',
        );
    }

    public function build()
    {
        return $this->view('mail.course-mail');
    }
}
