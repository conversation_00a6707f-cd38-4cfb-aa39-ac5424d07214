<?php

namespace App\Nova\MenuBuilderTypes;

use App\Models\Page;
use Illuminate\Support\Str;
use OptimistDigital\MenuBuilder\MenuItemTypes\MenuItemSelectType;

class PageLink extends MenuItemSelectType
{
    /**
     * Get menu link name shown in CMS when selecting link type.
     * ie ('Product Link' or 'Image Link').
     *
     * @return string
     **/
    public static function getName(): string
    {
        return 'Sayfalar';
    }

    public static function getIdentifier(): string
    {
        return 'Sayfalar';
    }

    /**
     * Get list of options shown in a select dropdown.
     *
     * Should be a map of [key => value, ...], where key is a unique identifier
     * and value is the displayed string.
     *
     * @return array
     **/
    public static function getOptions($locale): array
    {
        return Page::all()->pluck('title', 'id')->toArray();
    }

    /**
     * Get the subtitle value shown in CMS menu items list.
     *
     * @param null $value
     * @param array|null $data The data from item fields.
     * @param $locale
     * @return string
     */
    public static function getDisplayValue($value, ?array $data, $locale)
    {
        if (Page::find($value)==null)
        {
            return '<PERSON><PERSON> Silinmiş';
        }
        else
        {
            return Page::find($value)->menu_name;
        }
    }

    /**
     * Get the value of the link visible to the front-end.
     *
     * Can be anything. It is up to you how you will handle parsing it.
     *
     * This will only be called when using the nova_get_menu()
     * and nova_get_menus() helpers or when you call formatForAPI()
     * on the Menu model.
     *
     * @param null $value The key from options list that was selected.
     * @param array|null $data The data from item fields.
     * @param $locale
     * @return any
     */
    public static function getValue($value, ?array $data, $locale)
    {
        if (Page::find($value)==null){
            return 'sayfa-silinmis';
        }
        else
        {
            return Str::remove('http://allstarsmusic.ko.com.tr/',Page::find($value)->slug);
        }
    }

    /**
     * Get the rules for the resource.
     *
     * @return array A key-value map of attributes and rules.
     */

    public static function getRules(): array
    {
        return [
            'value' => 'required',
        ];
    }
}
