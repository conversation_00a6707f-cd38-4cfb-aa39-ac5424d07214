<?php

namespace App\Nova;

use App\Nova\Actions\PublishPage;
use ClassicO\NovaMediaLibrary\MediaLibrary;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Fields\Trix;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Panel;
use OptimistDigital\NovaSortable\Traits\HasSortableRows;
use OwenMelbz\RadioField\RadioButton;
use Whitecube\NovaFlexibleContent\Flexible;

class Course extends Resource
{
    public static function label()
    {
        return 'Bireysel Eğitimler';
    }

    public static $priority = 5;

    use HasSortableRows;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Course::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'title', 'content', 'menu_name', 'details', 'education_type'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable()->hideFromIndex(),
            Text::make('Başlık', 'title')->help('Sayfanın Başılığı burada yazılır.')->rules('required'),
            Text::make('Sayfa Linki', 'slug')->hideFromIndex()->withMeta(['extraAttributes' => ['readonly' => true]]),
            Text::make('Kısa Açıklama', 'about')->hideFromIndex()->required()
                ->help('İstendiğinde Anasayfada görünmesi için 150 Karakteri geçmeyecek şekilde bir açıklama metni girilmelidir.'),
            Text::make('Kontenjan Sayısı', 'quota')->rules('required')
                ->help('Toplam Kontenjan Sayısı Girilmelidir'),
            Text::make('Katılımcı Sayısı', 'subscriber')->rules('required')->default('0')
                ->help('Güncel Katılımcı Sayısı Girilmelidir'),
            Date::make('Başlangıç Tarihi', 'start_date')->required()
                ->help('Başlangıç Tarihi Girilmelidir'),
            Flexible::make('Kurs Detayları', 'details')->button('Kurs Detayı Ekle')
                ->addLayout('Kurs Detayı', 'education_detail', [
                    Select::make('Detay İkonu', 'icon')->options(function () {
                        $options = config('options.options');
                        $optionsArray = [];
                        for ($i = 1; $i <= 60; $i++) {
                            $optionsArray[$i] = 'İkon ' . $i;
                        }
                        return $optionsArray;
                    }),
                    Text::make('Açıklama', 'text'),])
                ->help('Kursla alakalı daha fazla bilgi sunmak için eklenebilir.'),
            Flexible::make('İçerik', 'content')->button('Sayfa İçeriği Ekle')
                ->help('Sayfa içerisinde görünecek içeriği bu alandan ekleyebilirsiniz.')
                ->addLayout('Tek Görsel Bölümü', 'image-library', [
                    MediaLibrary::make('Foto', 'image_id'),
                ])
                ->addLayout('İkili Görsel Bölümü', '2-photo', [
                    MediaLibrary::make('İlk Görsel', 'image_id1')->types('image'),
                    MediaLibrary::make('İkinci Görsel', 'image_id2')->types('image'),
                ])
                ->addLayout('Üçlü Görsel Bölümü', '3-photo', [
                    MediaLibrary::make('İlk Görsel', 'image_id1')->types('image'),
                    MediaLibrary::make('İkinci Görsel', 'image_id2')->types('image'),
                    MediaLibrary::make('Üçüncü Görsel', 'image_id3')->types('image'),
                ])
                ->addLayout('Yazı Bölümü', 'text', [
                    Trix::make('Yazı', 'text')->withFiles('public'),
                ])
                ->addLayout('Video Bölümü', 'video', [
                    Text::make('Video Frame', 'video'),
                ])
                ->addLayout('İkili Video Bölümü', '2-video', [
                    Text::make('Başlık', 'title1'),
                    Textarea::make('İlk Video Frame', 'video1'),
                    Text::make('Başlık', 'title2'),
                    Textarea::make('İkinci Video Frame', 'video2'),
                ])
                ->addLayout('Üçlü Video Bölümü', '3-video', [
                    Text::make('Başlık', 'title1'),
                    Textarea::make('İlk Video Frame', 'video1'),
                    Text::make('Başlık', 'title2'),
                    Textarea::make('İkinci Video Frame', 'video2'),
                    Text::make('Başlık', 'title3'),
                    Textarea::make('Üçüncü Video Frame', 'video3'),
                ])
                ->addLayout('Galeri Bölümü', 'gallery', [
                    Text::make('Galeri Başlığı', 'title'),
                    MediaLibrary::make('Görsel', 'image_id')->types('Image')->array('list'),
                ])
                ->addLayout('Buton İçerisine Döküman Bölümü', 'document', [
                    Text::make('Döküman Adı', 'document_name'),
                    MediaLibrary::make('Döküman', 'document_id')->types('docs'),
                ])
                ->addLayout('Metin İçerisine Döküman Bölümü', 'document-metin', [
                    Text::make('Döküman Adı', 'document_name'),
                    MediaLibrary::make('Döküman', 'document_id')->types('docs'),
                ])
                ->addLayout('Solda Metin Sağda Fotoğraf Bölümü', 'metin-link', [
                    Trix::make('Metin', 'text'),
                    MediaLibrary::make('Görsel', 'image_id')->types('image'),
                ])
                ->addLayout('Solda Fotoğraf Sağda Metin Bölümü', 'metin-link2', [
                    MediaLibrary::make('Görsel', 'image_id')->types('image'),
                    Trix::make('Metin', 'text'),
                ])
                ->addLayout('Solda Metin Sağda Video Bölümü', 'metin-video', [
                    Trix::make('Metin', 'text'),
                    Textarea::make('Frame', 'frame'),
                ])
                ->addLayout('Solda Video Sağda Metin Bölümü', 'metin-video2', [
                    Textarea::make('Frame', 'frame'),
                    Trix::make('Metin', 'text'),
                ])
                ->addLayout('Üçlü Görsel ve Metin Bölümü', '3-photo-text', [
                    MediaLibrary::make('İlk Görsel', 'image_id1')->types('image'),
                    MediaLibrary::make('İkinci Görsel', 'image_id2')->types('image'),
                    MediaLibrary::make('Üçüncü Görsel', 'image_id3')->types('image'),
                    Text::make('Başlık', 'title'),
                    Trix::make('Metin', 'text')->withFiles('public'),
                ])
                ->addLayout('Metin ve Üçlü Görsel Bölümü', 'text-3-photo', [
                    MediaLibrary::make('İlk Görsel', 'image_id1')->types('image'),
                    MediaLibrary::make('İkinci Görsel', 'image_id2')->types('image'),
                    MediaLibrary::make('Üçüncü Görsel', 'image_id3')->types('image'),
                    Text::make('Başlık', 'title'),
                    Trix::make('Metin', 'text')->withFiles('public'),
                ])
                ->addLayout('Akordiyon Bölümü', 'akordion', [
                    Text::make('Başlık', 'akordion_title'),
                    Trix::make('Metin', 'akordion_content')->withFiles('public'),
                ])
                ->addLayout('Başkan Ekleme Bölümü', 'president-layout', [
                    Text::make('Ad Soyad', 'fullname'),
                    Text::make('Yetki', 'authority'),
                    Text::make('Mail Adresi', 'email')->rules('email'),
                    Text::make('Instagram Adresi', 'instagram'),
                    Text::make('Twitter Adresi', 'twitter'),
                    MediaLibrary::make('Kişi Görseli', 'person_photo')->types('image'),
                ])
                ->addLayout('Üye Ekleme Bölümü', 'director-layout', [
                    Text::make('Ad Soyad', 'fullname'),
                    Text::make('Yetki', 'authority'),
                    Text::make('Mail Adresi', 'email')->rules('email'),
                    Text::make('Instagram Adresi', 'instagram'),
                    Text::make('Twitter Adresi', 'twitter'),
                    MediaLibrary::make('Kişi Görseli', 'person_photo')->types('image'),
                ]),

            new Panel('Sayfa Yayınlama Bilgileri', $this->PublishFields()),
            new Panel('SEO Bilgileri', $this->SEOFields()),];
    }

    public function PublishFields()
    {
        return [
            RadioButton::make('Sayfayı Yayınla', 'is_active')
                ->options([
                    0 => 'Hayır',
                    1 => 'Evet',
                ])
                ->default(1)
                ->marginBetween()
                ->toggle([0 => ['is_publish', 'about', 'image_id']]),

            RadioButton::make('Duyurularda Göster', 'is_publish')
                ->options([
                    0 => 'Hayır',
                    1 => 'Evet',
                ])
                ->default(1)
                ->marginBetween()
        ];
    }

    public function SEOFields()
    {
        return [
            Text::make('Seo Title', 'seo_title')->hideFromIndex(),
            Textarea::make('Meta Description', 'meta_description')->hideFromIndex(),
            Textarea::make('Meta Keywords', 'meta_keywords')->hideFromIndex(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new PublishPage(),
        ];
    }
}
