<?php

namespace App\Nova;

use ClassicO\NovaMediaLibrary\MediaLibrary;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class FindCertificate extends Resource
{
    public static function label()
    {
        return 'Sertifika Ekleme';
    }

    public static $priority = 7;
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\FindCertificate::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'full_name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'full_name', 'id_number', 'certificate_name', 'barcode'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable()->hideFromIndex(),
            Number::make('Tc Kimlik No', 'id_number')->help('T.C. Kim<PERSON> Numarası burada yazılır.')->rules('required', 'max:11'),
            Text::make('Ad Soyad', 'full_name')->help('Ad Soyad tam olacak şekilde yazılır.')->rules('required'),
            Text::make('Sertifika Adı', 'certificate_name')->help('Sertifika Adı burada yazılır.')->rules('required'),
            Text::make('Barkod No', 'barcode')->help('Sertifika Barkodu burada yazılır.')->rules('required'),
            MediaLibrary::make('Sertifika Dosyası', 'media_id')->help('Sertifikanın kendisi bu alana yüklenmelidir'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
