<?php

namespace App\Nova;

use App\Nova\Actions\PublishSlider;
use ClassicO\NovaMediaLibrary\MediaLibrary;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Heading;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Trix;
use Laravel\Nova\Http\Requests\NovaRequest;
use OptimistDigital\NovaSettings\NovaSettings;
use OptimistDigital\NovaSortable\Traits\HasSortableRows;
use OwenMelbz\RadioField\RadioButton;

class Slider extends Resource
{
    public static function label() {
        return 'Slider';}

    use HasSortableRows;

    public static $priority = 0;
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Slider::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'slider_title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id','slider_title','slider_text'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable()->hideFromIndex(),
            Text::make('Başlık(Zorunlu)','slider_title')->rules('required'),
            Trix::make('Slider Üzeri Metni','slider_text')->hideFromIndex(),
            RadioButton::make('Buton Göster ','text_align')
                ->hideFromIndex()
                ->options([
                    false  => 'Hayır',
                    true => 'Evet',
                ])
                ->default(false )
                ->marginBetween()
                ->toggle([false => ['slider_link','target']]),
            Text::make('Yönlendirme Linki','slider_link')->hideFromIndex()
                ->help('Link site dışına çıkacaksa "https://" ile başlamak zorundadır.'),
            Boolean::make('Yeni Sekmede Aç','target')->trueValue('_blank')->falseValue('_self'),
            MediaLibrary::make('Slider Fotoğrafı(Zorunlu)', 'image_id')->types(['Image'])->preview('full')->required(),
            Heading::make('Slider Boyutu "'.NovaSettings::getSetting('sliderboyut',0).'" olmalıdır.'),
            Boolean::make('Sliderda Göster','is_active')->trueValue(1)
                ->falseValue(0),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new PublishSlider(),
        ];
    }
}
